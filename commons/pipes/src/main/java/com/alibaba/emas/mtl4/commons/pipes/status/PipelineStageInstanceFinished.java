package com.alibaba.emas.mtl4.commons.pipes.status;

import com.alibaba.emas.mtl4.commons.pipes.RunStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PipelineStageInstanceFinished {
    Long pipelineStageInstanceId;
    Long pipelineInstanceId;
    RunStatus runStatus;
}
