package com.alibaba.emas.mtl4.acl.commons.amdp.dto;

import com.alibaba.ihr.amdplatform.common.annotation.AmdpDomain;
import com.alibaba.ihr.amdplatform.common.annotation.AmdpDomainField;

import java.util.Date;

/**
 * 聚合单条数据返回值.
 *
 * <AUTHOR>
 * @date 2021/6/26
 */
public class CombineDeptInfo {

    /**
     * Dept域
     */
    @AmdpDomain(
            code = "ORG_DEPT"
    )
    private OrgDept orgDept;

    public OrgDept getOrgDept() {
        return this.orgDept;
    }

    public void setOrgDept(OrgDept orgDept) {
        this.orgDept = orgDept;
    }

    /**
     * Dept域
     */
    @AmdpDomain(
            code = "ORG_DEPT"
    )
    public static class OrgDept {
        /**
         * 部门编号
         */
        @AmdpDomainField(
                code = "deptNo",
                domainCode = "ORG_DEPT"
        )
        private String deptNo;

        /**
         * division编号
         */
        @AmdpDomainField(
                code = "divisionDeptNo",
                domainCode = "ORG_DEPT"
        )
        private String divisionDeptNo;

        /**
         * Corp编号
         */
        @AmdpDomainField(
                code = "corpDeptNo",
                domainCode = "ORG_DEPT"
        )
        private String corpDeptNo;

        /**
         * BG编号
         */
        @AmdpDomainField(
                code = "bgDeptNo",
                domainCode = "ORG_DEPT"
        )
        private String bgDeptNo;

        /**
         * BU编号
         */
        @AmdpDomainField(
                code = "buDeptNo",
                domainCode = "ORG_DEPT"
        )
        private String buDeptNo;

        /**
         * 部门名称
         */
        @AmdpDomainField(
                code = "deptName",
                domainCode = "ORG_DEPT"
        )
        private String deptName;

        /**
         * 部门短名称
         */
        @AmdpDomainField(
                code = "deptShortName",
                domainCode = "ORG_DEPT"
        )
        private String deptShortName;

        /**
         * 部门主管工号
         */
        @AmdpDomainField(
                code = "masterWorkNo",
                domainCode = "ORG_DEPT"
        )
        private String masterWorkNo;

        /**
         * 部门主管任职记录
         */
        @AmdpDomainField(
                code = "masterOrderNum",
                domainCode = "ORG_DEPT"
        )
        private Integer masterOrderNum;

        /**
         * 部门Hrg工号
         */
        @AmdpDomainField(
                code = "hrgWorkNo",
                domainCode = "ORG_DEPT"
        )
        private String hrgWorkNo;

        /**
         * 部门Hrg任职记录
         */
        @AmdpDomainField(
                code = "hrgOrderNum",
                domainCode = "ORG_DEPT"
        )
        private Integer hrgOrderNum;

        /**
         * 生效日期
         */
        @AmdpDomainField(
                code = "gmtActive",
                domainCode = "ORG_DEPT"
        )
        private Date gmtActive;

        /**
         * 状态
         */
        @AmdpDomainField(
                code = "status",
                domainCode = "ORG_DEPT"
        )
        private String status;

        /**
         * 部门英文名称
         */
        @AmdpDomainField(
                code = "deptEnName",
                domainCode = "ORG_DEPT"
        )
        private String deptEnName;

        /**
         * 组织类型
         */
        @AmdpDomainField(
                code = "deptOrgType",
                domainCode = "ORG_DEPT"
        )
        private String deptOrgType;

        /**
         * 部门别名
         */
        @AmdpDomainField(
                code = "aliasName",
                domainCode = "ORG_DEPT"
        )
        private String aliasName;

        /**
         * 部门英文别名
         */
        @AmdpDomainField(
                code = "aliasNameEn",
                domainCode = "ORG_DEPT"
        )
        private String aliasNameEn;

        /**
         * buNo
         */
        @AmdpDomainField(
                code = "buNo",
                domainCode = "ORG_DEPT"
        )
        private String buNo;

        /**
         * 上级部门编号
         */
        @AmdpDomainField(
                code = "superDeptNo",
                domainCode = "ORG_DEPT"
        )
        private String superDeptNo;

        /**
         * 部门全路径
         */
        @AmdpDomainField(
                code = "deptPath",
                domainCode = "ORG_DEPT"
        )
        private String deptPath;

        /**
         * 所属公司
         */
        @AmdpDomainField(
                code = "companyNo",
                domainCode = "ORG_DEPT"
        )
        private String companyNo;

        /**
         * 部门所在地编码
         */
        @AmdpDomainField(
                code = "locationNo",
                domainCode = "ORG_DEPT"
        )
        private String locationNo;

        /**
         * 部门level
         */
        @AmdpDomainField(
                code = "level",
                domainCode = "ORG_DEPT"
        )
        private String level;

        /**
         * 公司类型
         */
        @AmdpDomainField(
                code = "companyType",
                domainCode = "ORG_DEPT"
        )
        private String companyType;

        /**
         * 部门职能编码
         */
        @AmdpDomainField(
                code = "funcCode",
                domainCode = "ORG_DEPT"
        )
        private String funcCode;

        /**
         * 自动计算部门主管工号
         */
        @AmdpDomainField(
                code = "autoMasterWorkNo",
                domainCode = "ORG_DEPT"
        )
        private String autoMasterWorkNo;

        /**
         * 自动计算部门主管工号职务序号
         */
        @AmdpDomainField(
                code = "autoMasterOrderNum",
                domainCode = "ORG_DEPT"
        )
        private Integer autoMasterOrderNum;

        /**
         * 职务体系
         */
        @AmdpDomainField(
                code = "groupCode",
                domainCode = "ORG_DEPT"
        )
        private String groupCode;

        /**
         * 企业id
         */
        @AmdpDomainField(
                code = "groupId",
                domainCode = "ORG_DEPT"
        )
        private String groupId;

        /**
         * 推荐主管
         */
        @AmdpDomainField(
                code = "recomdMaster",
                domainCode = "ORG_DEPT"
        )
        private String recomdMaster;

        /**
         * 英文部门简称
         */
        @AmdpDomainField(
                code = "shortEnName",
                domainCode = "ORG_DEPT"
        )
        private String shortEnName;

        /**
         * 是否海外部门
         */
        @AmdpDomainField(
                code = "isOversea",
                domainCode = "ORG_DEPT"
        )
        private String isOversea;

        public String getDeptNo() {
            return this.deptNo;
        }

        public void setDeptNo(String deptNo) {
            this.deptNo = deptNo;
        }

        public String getDivisionDeptNo() {
            return this.divisionDeptNo;
        }

        public void setDivisionDeptNo(String divisionDeptNo) {
            this.divisionDeptNo = divisionDeptNo;
        }

        public String getCorpDeptNo() {
            return this.corpDeptNo;
        }

        public void setCorpDeptNo(String corpDeptNo) {
            this.corpDeptNo = corpDeptNo;
        }

        public String getBgDeptNo() {
            return this.bgDeptNo;
        }

        public void setBgDeptNo(String bgDeptNo) {
            this.bgDeptNo = bgDeptNo;
        }

        public String getBuDeptNo() {
            return this.buDeptNo;
        }

        public void setBuDeptNo(String buDeptNo) {
            this.buDeptNo = buDeptNo;
        }

        public String getDeptName() {
            return this.deptName;
        }

        public void setDeptName(String deptName) {
            this.deptName = deptName;
        }

        public String getDeptShortName() {
            return this.deptShortName;
        }

        public void setDeptShortName(String deptShortName) {
            this.deptShortName = deptShortName;
        }

        public String getMasterWorkNo() {
            return this.masterWorkNo;
        }

        public void setMasterWorkNo(String masterWorkNo) {
            this.masterWorkNo = masterWorkNo;
        }

        public Integer getMasterOrderNum() {
            return this.masterOrderNum;
        }

        public void setMasterOrderNum(Integer masterOrderNum) {
            this.masterOrderNum = masterOrderNum;
        }

        public String getHrgWorkNo() {
            return this.hrgWorkNo;
        }

        public void setHrgWorkNo(String hrgWorkNo) {
            this.hrgWorkNo = hrgWorkNo;
        }

        public Integer getHrgOrderNum() {
            return this.hrgOrderNum;
        }

        public void setHrgOrderNum(Integer hrgOrderNum) {
            this.hrgOrderNum = hrgOrderNum;
        }

        public Date getGmtActive() {
            return this.gmtActive;
        }

        public void setGmtActive(Date gmtActive) {
            this.gmtActive = gmtActive;
        }

        public String getStatus() {
            return this.status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getDeptEnName() {
            return this.deptEnName;
        }

        public void setDeptEnName(String deptEnName) {
            this.deptEnName = deptEnName;
        }

        public String getDeptOrgType() {
            return this.deptOrgType;
        }

        public void setDeptOrgType(String deptOrgType) {
            this.deptOrgType = deptOrgType;
        }

        public String getAliasName() {
            return this.aliasName;
        }

        public void setAliasName(String aliasName) {
            this.aliasName = aliasName;
        }

        public String getAliasNameEn() {
            return this.aliasNameEn;
        }

        public void setAliasNameEn(String aliasNameEn) {
            this.aliasNameEn = aliasNameEn;
        }

        public String getBuNo() {
            return this.buNo;
        }

        public void setBuNo(String buNo) {
            this.buNo = buNo;
        }

        public String getSuperDeptNo() {
            return this.superDeptNo;
        }

        public void setSuperDeptNo(String superDeptNo) {
            this.superDeptNo = superDeptNo;
        }

        public String getDeptPath() {
            return this.deptPath;
        }

        public void setDeptPath(String deptPath) {
            this.deptPath = deptPath;
        }

        public String getCompanyNo() {
            return this.companyNo;
        }

        public void setCompanyNo(String companyNo) {
            this.companyNo = companyNo;
        }

        public String getLocationNo() {
            return this.locationNo;
        }

        public void setLocationNo(String locationNo) {
            this.locationNo = locationNo;
        }

        public String getLevel() {
            return this.level;
        }

        public void setLevel(String level) {
            this.level = level;
        }

        public String getCompanyType() {
            return this.companyType;
        }

        public void setCompanyType(String companyType) {
            this.companyType = companyType;
        }

        public String getFuncCode() {
            return this.funcCode;
        }

        public void setFuncCode(String funcCode) {
            this.funcCode = funcCode;
        }

        public String getAutoMasterWorkNo() {
            return this.autoMasterWorkNo;
        }

        public void setAutoMasterWorkNo(String autoMasterWorkNo) {
            this.autoMasterWorkNo = autoMasterWorkNo;
        }

        public Integer getAutoMasterOrderNum() {
            return this.autoMasterOrderNum;
        }

        public void setAutoMasterOrderNum(Integer autoMasterOrderNum) {
            this.autoMasterOrderNum = autoMasterOrderNum;
        }

        public String getGroupCode() {
            return this.groupCode;
        }

        public void setGroupCode(String groupCode) {
            this.groupCode = groupCode;
        }

        public String getGroupId() {
            return this.groupId;
        }

        public void setGroupId(String groupId) {
            this.groupId = groupId;
        }

        public String getRecomdMaster() {
            return this.recomdMaster;
        }

        public void setRecomdMaster(String recomdMaster) {
            this.recomdMaster = recomdMaster;
        }

        public String getShortEnName() {
            return this.shortEnName;
        }

        public void setShortEnName(String shortEnName) {
            this.shortEnName = shortEnName;
        }

        public String getIsOversea() {
            return this.isOversea;
        }

        public void setIsOversea(String isOversea) {
            this.isOversea = isOversea;
        }
    }

}
