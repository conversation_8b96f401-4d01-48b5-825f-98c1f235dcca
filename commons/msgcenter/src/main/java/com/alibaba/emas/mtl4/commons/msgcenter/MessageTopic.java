package com.alibaba.emas.mtl4.commons.msgcenter;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
public class MessageTopic {
    private String groupId;
    private String topic;

    public MessageTopic() {}

    public MessageTopic(String topic) {
        this.topic = topic;
    }

    public MessageTopic(String groupId, String topic) {
        this.groupId = groupId;
        this.topic = topic;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MessageTopic)) {
            return false;
        }
        MessageTopic that = (MessageTopic) o;
        return Objects.equals(getGroupId(), that.getGroupId()) &&
                Objects.equals(getTopic(), that.getTopic());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getGroupId(), getTopic());
    }
}
