package com.alibaba.emas.mtl4.commons.msgcenter.ons;

import com.alibaba.emas.mtl4.commons.msgcenter.SendResultConvent;
import com.alibaba.emas.mtl4.commons.msgcenter.SendResultWrapper;
import com.aliyun.openservices.ons.api.SendResult;

public class OnsSendResultConvent implements SendResultConvent<SendResult> {

    @Override
    public SendResultWrapper convertToSendResultWrapper(SendResult sendResult) {
        return new SendResultWrapper(sendResult.getTopic(), sendResult.getMessageId());
    }

    @Override
    public SendResult convertToSendResult(SendResultWrapper sendResultWrapper) {
        SendResult sendResult = new SendResult();
        sendResult.setTopic(sendResultWrapper.getTopic());
        sendResult.setMessageId(sendResultWrapper.getMessageId());
        return sendResult;
    }
}