package com.alibaba.emas.mtl4.commons.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * created by qingli.hwz on 2019-10-17
 */
@Slf4j
public class RetryUtils {

    public static <T> T retry(Function<T> function, int times) throws Exception {
        AtomicInteger retry = new AtomicInteger(0);
        while (true) {
            try {
                return function.call();
            } catch (Exception e) {
                log.error("retry1 error:" + e);
                if (retry.incrementAndGet() >= times) {
                    throw e;
                }
            }
            try {
                Thread.sleep(400);
            } catch (Exception e) {
                log.error("RetryUtils retry1 sleep error:", e);
            }
        }
    }

    public static void retry(VoidFunction function, int times) throws Exception {
        AtomicInteger retry = new AtomicInteger(0);
        while (true) {
            try {
                function.call();
                break;
            } catch (Exception e) {
                log.error("retry2 error:" + e);
                if (retry.incrementAndGet() >= times) {
                    throw e;
                }
            }
            try {
                Thread.sleep(400);
            } catch (Exception e) {
                log.error("RetryUtils retry sleep error:", e);
            }
        }
    }

    public interface VoidFunction {

        void call() throws Exception;
    }

    public static interface Function<T> {

        T call() throws Exception;
    }

}
