package com.alibaba.emas.mtl4.commons.utils;

import lombok.Getter;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.concurrent.atomic.AtomicInteger;

//
public class SnowflakeIDGenerator implements IDGenerator {

    @Getter
    private String ip;
    private String ipBase62;
    private AtomicInteger incr = new AtomicInteger(0);

    public SnowflakeIDGenerator() {
        try {
            this.ip = InetAddress.getLocalHost().getHostAddress();
            this.ipBase62 = Base62.encoding(toInt(ip));
        } catch (UnknownHostException e) {
            throw new RuntimeException(e);
        }
    }

    public SnowflakeIDGenerator(String ip) {
        this.ip = ip;
        this.ipBase62 = Base62.encoding(toInt(ip));
    }

    private int toInt(String ip) {
        String[] parts = ip.split("\\.");
        return (Integer.valueOf(parts[0]) & 0xff) << 24
                | (Integer.valueOf(parts[1]) & 0xff) << 16
                | (Integer.valueOf(parts[2]) & 0xff) << 8
                | Integer.valueOf(parts[3]) & 0xff;
    }

    @Override
    public String get() {
        long ts = System.currentTimeMillis();
        int i = incr.incrementAndGet();
        return ipBase62 + Base62.encoding(ts) + Base62.encoding(i);
    }
}
