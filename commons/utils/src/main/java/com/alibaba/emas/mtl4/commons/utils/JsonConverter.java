package com.alibaba.emas.mtl4.commons.utils;

import javax.persistence.AttributeConverter;
import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONObject;

abstract public class JsonConverter<T> implements AttributeConverter<T, String> {


    public static class MapConverter extends Json<PERSON>onverter<Map> {
    }

    public static class ListConverter extends JsonConverter<List> {
    }

    public static class SetConverter extends JsonConverter<Set> {
    }

    public static class JSONObjectConverter extends JsonConverter<JSONObject> {
    }

    Class<T> entityClass = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];

    @Override
    public String convertToDatabaseColumn(T attribute) {
        try {
            if(attribute == null){
                return null;
            }
            return JsonUtils.toJson(attribute);
        } catch (IOException e) {
            throw new ConvertException(e);
        }
    }

    @Override
    public T convertToEntityAttribute(String dbData) {
        try {
            if(dbData == null){
                return null;
            }
            return JsonUtils.toObject(dbData, entityClass);
        } catch (IOException e) {
            throw new ConvertException(e);
        }
    }
}
