<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.alibaba.emas</groupId>
        <artifactId>mtl4-dependencies</artifactId>
        <version>${revision}</version>
        <relativePath>../dependencies</relativePath>
    </parent>

    <artifactId>mtl4-commons</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>MTL4 :: Commons</name>

    <modules>
        <module>utils</module>
        <module>pipes</module>
        <module>msgcenter</module>
        <module>aliyun</module>
        <module>storage</module>
        <module>storage-oss</module>
        <module>acl</module>
        <module>amdp</module>
    </modules>

    <dependencies>
        <!-- junit -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- slf4j -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.alibaba.security</groupId>-->
<!--            <artifactId>security-all</artifactId>-->
<!--            <version>2.0.1-BETA-SNAPSHOT</version>-->
<!--        </dependency>-->
    </dependencies>
</project>