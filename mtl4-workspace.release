# 1. 你可以直接编辑本文件的内容，或者通过工具来帮你校验合法性和自动生成，请点击：http://aliwing.alibaba-inc.com/apprelease/home.htm
# 2. 更多关于Release文件的规范和约定，请点击: http://docs.alibaba-inc.com/pages/viewpage.action?pageId=252891532

# 构建打包使用jdk版本
baseline.jdk=ali-jdk-8.2.3

# 构建打包所用的maven版本，pandora boot应用需要使用maven 3
build.tools.maven=maven3

# 构建打包使用的maven settings文件
build.tools.maven.settings=tao

# 构建源码语言类型
code.language=java

# 构建使用的maven命令，当前支持maven命令，不写使用打包规范默命令认. 该项填写时，必须指定 build.output(采用打包规范默认方式不需要关注这里)
build.command=mvn -DskipTests clean package -U -pl com.alibaba.emas:mtl4-services-workspace-application -am

# 打包为tgz文件的目录，pom.xml里指定ant插件解压fat jar到target/${appName}目录
build.output=services/workspace/application/target/mtl4-workspace

# 设置Dockerfile里的APP_NAME变量，必须要配置
build.tools.docker.args=--build-arg APP_NAME=mtl4-workspace