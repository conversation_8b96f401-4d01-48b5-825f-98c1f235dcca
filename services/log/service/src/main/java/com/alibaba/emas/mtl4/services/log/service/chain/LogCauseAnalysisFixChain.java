package com.alibaba.emas.mtl4.services.log.service.chain;

import com.alibaba.emas.mtl4.commons.utils.StringKit;
import com.alibaba.emas.mtl4.log.enums.BuildErrorDistributeFlag;
import com.alibaba.emas.mtl4.log.enums.BuildErrorRootType;
import com.alibaba.emas.mtl4.log.model.*;
import com.alibaba.emas.mtl4.scheduler.api.LogAnalyzeService;
import com.alibaba.emas.mtl4.services.dev.api.model.PipelineInstanceBO;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.template.PipelineSceneType;
import com.alibaba.emas.mtl4.services.log.service.BuildErrorService;
import com.alibaba.emas.mtl4.services.log.service.ErrorLogAnalyzeService;
import com.alibaba.emas.mtl4.services.log.service.LogErrorService;
import com.alibaba.emas.mtl4.services.log.service.impl.CommonService;
import com.alibaba.emas.mtl4.services.log.service.model.AppWatcherInfo;
import com.alibaba.emas.mtl4.services.log.service.utils.Constants;
import com.alibaba.emas.mtl4.services.log.service.utils.LogAnalyzeContextUtils;
import com.alibaba.emas.mtl4.services.scheduler.domain.LogAnalyzeResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * 日志根因分析chain.
 *
 * <AUTHOR>
 * @date 2023/11/6
 */
@Slf4j
@Component
public class LogCauseAnalysisFixChain extends AbstractLogAnalyzeChain {

    @Resource
    private CommonService commonService;

    @Resource
    private LogErrorService logErrorService;

    @Autowired
    private ErrorLogAnalyzeService errorLogAnalyzeService;

    @Autowired
    private LogAnalyzeService logAnalyzeService;

    @Resource
    private BuildErrorService buildErrorService;

    public void logErrorAnalysis(LogAnalyzeResult logAnalyzeResult) {
        Flux.just(logAnalyzeResult)
                // 错误打标
                .doOnNext(this::logRootCauseAnalysis)
                // 发送钉钉
                .doOnNext(this::sendErrorSolutionMessage)
                // 自助解决
                .map(result -> buildErrorService.fixBuildError(logAnalyzeResult.getId()))
                .blockLast();
    }

    /**
     * 发送日志根因解决方案消息
     * @param result 日志分析结果
     */
    public void sendErrorSolutionMessage(LogAnalyzeResult result) {
        LogAnalyzeResultVO logAnalyzeResultVO = logAnalyzeService.getLogAnalyzeResultByIdWithContent(result.getId(), 0L, 10L);
        if (Objects.isNull(logAnalyzeResultVO.getMatchKnowledgeItemId()) || Objects.isNull(logAnalyzeResultVO.getRootCause())) {
            return;
        }
        // 消息开关
        if (BooleanUtils.isNotTrue(commonService.getBaseConfigObject().getBoolean(Constants.ERROR_SOLUTION_MESSAGE_SWITCH_KEY))) {
            return;
        }
        // TODO 其实这里应该先评估，知识库匹配分数如果小于阈值，也不发送消息
        Double matchScore = logAnalyzeResultVO.getBestMatchKnowledgeItem().getMatchScore();
        if (Objects.isNull(matchScore)
                || matchScore < commonService.getBaseConfigObject().getDouble(Constants.ERROR_SOLUTION_MESSAGE_MATCH_THRESHOLD_KEY)) {
            return;
        }
        // TODO 暂时只针对MC的持续集成变更单开启
        if (!PipelineSceneType.AGILE_CI_ALTER_SHEET_BUILD.name().equals(logAnalyzeResultVO.getSceneInfo().getPipelineSceneType())) {
            return;
        }
        LogErrorItemVO logErrorItemVO = logErrorService.getLogErrorItemInfoById(logAnalyzeResultVO.getMatchKnowledgeItemId());
        Long applicationId = LogAnalyzeContextUtils.getActualApplicationId(logAnalyzeResultVO.getAnalyzeParams());
        Long pipelineInstanceId = logAnalyzeResultVO.getSceneInfo().getPipelineInstanceId();
        PipelineInstanceBO pipelineInstanceBO = commonService.getPipelineInstance(applicationId, pipelineInstanceId);
        String jumpUrl = commonService.getPipelineBuildDetailDingTalkUrl(
                logAnalyzeResultVO.getSceneInfo().getPipelineInstanceId());
        String title = StringKit.format(Constants.ERROR_SOLUTION_MESSAGE_TITLE_TEMPLATE, pipelineInstanceBO.getPipelineName());
        commonService.sendBuildErrorSolutionMessage(Lists.newArrayList(logAnalyzeResultVO.getInvokeUser()), title,
                logAnalyzeResultVO.getRootCauseDetail().getCoordinators(), logErrorItemVO.getErrorSolution().getErrorSolution(),
                logAnalyzeResultVO.getRootCause().getDesc(), jumpUrl);
    }

    /**
     * 发送日志根因解决方案消息
     * @param logAnalyzeResultId 日志分析结果id
     */
    public void sendLogErrorSolutionMessage(Long logAnalyzeResultId) {
        if (Objects.isNull(logAnalyzeResultId)) {
            return;
        }
        LogErrorViewVO logErrorViewVO = errorLogAnalyzeService.getLogErrorAnalyzeResultById(logAnalyzeResultId, 0L, 10L);
        if (Objects.isNull(logErrorViewVO.getMatchKnowledgeItemId()) || Objects.isNull(logErrorViewVO.getRootCause())
                || Objects.isNull(logErrorViewVO.getBestMatchKnowledgeSolution())) {
            return;
        }

    }

    /**
     * 日志根因分析
     * @param logAnalyzeResult 日志分析结果
     */
    public void logRootCauseAnalysis(LogAnalyzeResult logAnalyzeResult) {
        if (!isMatchKnowledge(logAnalyzeResult.getAnalyzeResultConclusion())) {
            log.info("构建错误[{}]尚未匹配知识库", logAnalyzeResult.getId());
            return;
        }
        Long matchLogErrorItemId = logAnalyzeResult.getAnalyzeResultConclusion().getMatchKnowledgeList()
                .get(0).getMatchKnowledgeItemId();
        AppWatcherInfo appWatcherInfo = getRootCauseAndDetail(logAnalyzeResult);
        LogErrorItemVO errorItemVO = logErrorService.getLogErrorItemInfoById(matchLogErrorItemId);
        if (Objects.isNull(errorItemVO) || Objects.isNull(errorItemVO.getErrorSolution())) {
            return;
        }

        LogAnalyzeSceneInfo sceneInfo = errorLogAnalyzeService.selectLogAnalyzeSceneInfo(logAnalyzeResult.getAnalyzeParams());
        String errorType = errorItemVO.getErrorSolution().getErrorType();
        BuildErrorRootCauseInfo causeInfo = getErrorDutyEmpIds(appWatcherInfo, errorType, sceneInfo);

        // 更新错误分析
        LogAnalyzeResultDTO analyzeResultDTO = new LogAnalyzeResultDTO();
        analyzeResultDTO.setRootCause(BuildErrorRootType.of(causeInfo.getRootCause()));
        analyzeResultDTO.setRootCauseDetail(causeInfo);
        // 设置错误分发结果待标记
        analyzeResultDTO.setErrorDistributeFlag(BuildErrorDistributeFlag.WAIT_CONFIRM);

        // 更新场景信息
        analyzeResultDTO.setSceneInfo(sceneInfo);
        Optional.ofNullable(sceneInfo).ifPresent(info -> analyzeResultDTO.setInvokeUser(sceneInfo.getBuildUser()));

        logAnalyzeService.patchUpdateLogAnalyzeResult(logAnalyzeResult.getId(), analyzeResultDTO);
    }

    /**
     * 获取错误相关的值班人
     * @param appWatcherInfo 应用值班人信息
     * @param errorType 错误类型
     * @return 错误值班人
     */
    public BuildErrorRootCauseInfo getErrorDutyEmpIds(AppWatcherInfo appWatcherInfo, String errorType, LogAnalyzeSceneInfo sceneInfo) {
        String[] errorTypeArray = StringUtils.split(errorType, Constants.COMMA);
        // TODO 暂时只取第一个
        BuildErrorRootCauseInfo buildErrorRootCauseInfo = new BuildErrorRootCauseInfo();
        BuildErrorRootType errorRootType = BuildErrorRootType.of(errorTypeArray[0]);
        if (Objects.isNull(errorRootType)) {
            return buildErrorRootCauseInfo;
        }
        buildErrorRootCauseInfo.setRootCause(errorRootType.getValue());
        switch (errorRootType) {
            case ARCHITECTURAL_ISSUE:
                buildErrorRootCauseInfo.setCoordinators(appWatcherInfo.getArchWatchman());
                break;
            case PLATFORM_ISSUE:
                buildErrorRootCauseInfo.setCoordinators(appWatcherInfo.getPlatformWatchman());
                break;
            case BIZ_ISSUE:
                // buildErrorRootCauseInfo.setCoordinators(sceneInfo.getBuildUser());
                break;
            default:
                break;
        }
        return buildErrorRootCauseInfo;
    }

    private AppWatcherInfo getRootCauseAndDetail(LogAnalyzeResult logAnalyzeResult) {
        return commonService.getAppCurrentWatcherInfo(
                LogAnalyzeContextUtils.getAssociatedClientId(logAnalyzeResult.getAnalyzeParams()));
    }

}
