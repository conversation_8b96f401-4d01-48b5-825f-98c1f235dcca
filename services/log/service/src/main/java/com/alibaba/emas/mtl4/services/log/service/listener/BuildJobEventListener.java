package com.alibaba.emas.mtl4.services.log.service.listener;

import com.alibaba.emas.mtl4.commons.pipes.RunStatus;
import com.alibaba.emas.mtl4.scheduler.api.event.JobFinished;
import com.alibaba.emas.mtl4.services.log.service.ErrorLogAnalyzeService;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


/**
 * metaq消息 jobFinished listener.
 *
 * <AUTHOR>
 * @Date 2024/08/30
 */
@Slf4j
@Service(value = "buildJobEventListener")
public class BuildJobEventListener implements MessageListenerConcurrently {

    protected static final ThreadPoolExecutor LOG_ANALYZE_POST_HANDLER;

    @Resource
    private ErrorLogAnalyzeService errorLogAnalyzeService;

    static {
        int cpuCount = Runtime.getRuntime().availableProcessors();
        LOG_ANALYZE_POST_HANDLER = new ThreadPoolExecutor(cpuCount * 4, cpuCount * 8, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(5000),
                r -> {
                    Thread t = new Thread(r);
                    t.setDaemon(true);
                    t.setName("log-analyze-post-handle-thread-" + t.getId());
                    return t;
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            log.info("receive metaq message：【{}】", msg);
            String receivedMsg = new String(msg.getBody(), StandardCharsets.UTF_8);
            log.info("metaq message body：【{}】", receivedMsg);

            JobFinished jobFinished = JSON.parseObject(receivedMsg, JobFinished.class);
            log.trace("收到的 jobFinished 信息：{}", jobFinished);

            if (jobFinished.getJobResult().getStatus() == RunStatus.FAILED) {
                log.info("listening failed job scheduledId: {}", jobFinished.getJobScheduleId());
            }

            LOG_ANALYZE_POST_HANDLER.submit(() -> errorLogAnalyzeService.analyzeErrorLog(jobFinished.getJobScheduleId()));

        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
