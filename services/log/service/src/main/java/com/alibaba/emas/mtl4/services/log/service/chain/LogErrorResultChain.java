package com.alibaba.emas.mtl4.services.log.service.chain;

import com.alibaba.emas.mtl4.services.log.service.ErrorLogAnalyzeService;
import com.alibaba.emas.mtl4.services.log.service.chain.handler.*;
import com.alibaba.emas.mtl4.services.log.service.impl.CommonService;
import com.alibaba.emas.mtl4.services.log.service.model.LogAnalyzeResultChainContext;
import com.alibaba.emas.mtl4.services.scheduler.domain.LogAnalyzeResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 日志错误结果处理chain.
 *
 * <AUTHOR>
 * @date 2023/10/27
 */
@Slf4j
@Component("logErrorResultChain")
public class LogErrorResultChain extends AbstractLogAnalyzeChain {

    @Resource
    private CommonService commonService;

    @Resource
    private ErrorLogAnalyzeService errorLogAnalyzeService;

    @Resource
    private ContextPrepareHandler contextPrepareHandler;

    @Resource
    private LogMatchSimilarityCheckHandler logMatchSimilarityCheckHandler;

    @Resource
    private SummaryLogMatchCheckHandler summaryLogMatchCheckHandler;

    @Resource
    private ErrorContentSummaryHandler errorContentSummaryHandler;

    @Resource
    private CreateLogKnowledgeHandler createLogKnowledgeHandler;

    /**
     * 扫描处理新日志分析结果
     */
    public void scanNewLogAnalyzeResult(LogAnalyzeResult logAnalyzeResult) {
        scanNewLogAnalyzeResult(logAnalyzeResult.getId());
    }

    /**
     * 扫描处理新日志分析结果
     */
    public void scanNewLogAnalyzeResult(Long analyzeResultId) {
        LogAnalyzeResultChainContext resultHandlerContext = new LogAnalyzeResultChainContext();
        resultHandlerContext.setLogAnalyzeResultId(analyzeResultId);
        resultHandlerContext.setChainName(this.getClass().getSimpleName());

        // 处理链
        contextPrepareHandler.startHandle(resultHandlerContext, contextPrepareOutput -> {
            // 是否是需要处理的应用appId
            if (!isMatchWhitelistApplicationId(resultHandlerContext, getLogAnalyzeAppWhiteListByDiamondConfig(commonService))) {
                log.info("[{}]未命中白名单，跳过", resultHandlerContext.getLogAnalyzeResultId());
                return;
            }
            if (!isMatchKnowledge(resultHandlerContext)) {
                log.info("[{}]未匹配知识库，跳过", resultHandlerContext.getLogAnalyzeResultId());
                return;
            }
            // 看日志匹配得分相似度
            logMatchSimilarityCheckHandler.startHandle(resultHandlerContext, matchScoreOutput -> {
                LogMatchSimilarityCheckHandler.LogMatchHandleOutput simHandleOutput =
                        (LogMatchSimilarityCheckHandler.LogMatchHandleOutput) matchScoreOutput;
                if (BooleanUtils.isTrue(simHandleOutput.getMatchCorrect())) {
                    log.info("[{}]任务相似度大于阈值，直接跳过", resultHandlerContext.getLogAnalyzeResultId());
                    return;
                }
                // 接下来的流程，总结日志
                errorContentSummaryHandler.startHandle(resultHandlerContext, summaryOutput -> {
                    ErrorContentSummaryHandler.LogContentSummaryHandleOutput summaryHandleOutput =
                            (ErrorContentSummaryHandler.LogContentSummaryHandleOutput) summaryOutput;

                    // 更新数据库中的关键日志信息
                    errorLogAnalyzeService.updateErrorLogAnalyzeKeyContent(resultHandlerContext.getLogAnalyzeResultId(), summaryHandleOutput.getKeyErrorLogContent());

                    // 用总结的输出去匹配知识库，看看是否大于阈值
                    summaryLogMatchCheckHandler.startHandle(resultHandlerContext, matchOutput -> {
                        LogMatchSimilarityCheckHandler.LogMatchHandleOutput matchHandleOutput =
                                (LogMatchSimilarityCheckHandler.LogMatchHandleOutput) matchOutput;
                        if (BooleanUtils.isTrue(matchHandleOutput.getMatchCorrect())) {
                            log.info("[{}]现在相似度匹配成功，跳过", resultHandlerContext.getLogAnalyzeResultId());
                            return;
                        }
                        // 如果走到了这步，就只能创建一个新的问题了
                        createLogKnowledgeHandler.startHandle(resultHandlerContext, newLogKnowledgeOutput -> {
                            // 处理链结束
                            log.info("[{}] LogErrorResultChain end", resultHandlerContext.getLogAnalyzeResultId());
                        });
                    });
                });
            });

        });
    }

}
