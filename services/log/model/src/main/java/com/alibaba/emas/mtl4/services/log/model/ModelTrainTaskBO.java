package com.alibaba.emas.mtl4.services.log.model;

import com.alibaba.emas.mtl4.services.log.domain.ModelDataSource;
import com.alibaba.emas.mtl4.services.log.domain.TrainTaskExecutor;
import com.alibaba.emas.mtl4.services.log.domain.enums.ModelTrainTaskStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 模型训练任务
 *
 * <AUTHOR>
 * @date 2023/4/13
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class ModelTrainTaskBO {

    /**
     * id.
     */
    private Long id;

    /**
     * 训练任务名
     */
    private String taskName;

    /**
     * 训练任务详细信息
     */
    private String taskDetailInfo;

    /**
     * 训练模型名
     */
    private String modelName;

    /**
     * 训练模型唯一标识
     */
    private String modelIdentifier;

    /**
     * 训练模型数据，json数组
     */
    private String trainData;

    /**
     * 训练模型数据源，json数组
     */
    private List<ModelDataSource> trainDataSource;

    /**
     * 模型测试数据源，json数组
     */
    private String evaluationDataIds;

    /**
     * 训练次数
     */
    private Integer trainEpochs;

    /**
     * 训练其他参数
     */
    private String trainOtherArgs;

    /**
     * 模型实验阈值信息
     */
    private Map<String, String> modelExperimentThreshold;

    /**
     * 任务执行executor
     */
    private TrainTaskExecutor taskExecutor;

    /**
     * 任务扩展信息
     */
    private Map<String, Object> taskExtra;

    /**
     * 模型是否需要检查再上线
     */
    private Boolean modelNeedCheck;

    /**
     * 模型归属人
     */
    private String modelOwner;

    /**
     * 任务状态.
     */
    private ModelTrainTaskStatus status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 最后修改人
     */
    private String modifier;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtModified;

}
