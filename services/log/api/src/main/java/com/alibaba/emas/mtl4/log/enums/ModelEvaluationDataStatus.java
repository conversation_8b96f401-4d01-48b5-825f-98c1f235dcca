package com.alibaba.emas.mtl4.log.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模型评估数据状态
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
@Getter
@AllArgsConstructor
public enum ModelEvaluationDataStatus {

    /**
     * 有效.
     */
    VALID("VALID", "有效"),

    /**
     * 无效.
     */
    INVALID("INVALID", "无效");

    /**
     * value.
     */
    private final String value;

    /**
     * 描述.
     */
    private final String desc;

}
