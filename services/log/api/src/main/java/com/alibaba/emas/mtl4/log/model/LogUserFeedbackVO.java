package com.alibaba.emas.mtl4.log.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 用户反馈值对象.
 *
 * <AUTHOR>
 * @date 2023/6/13
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
public class LogUserFeedbackVO extends LogUserFeedbackDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 最后修改人
     */
    private String modifier;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

}
