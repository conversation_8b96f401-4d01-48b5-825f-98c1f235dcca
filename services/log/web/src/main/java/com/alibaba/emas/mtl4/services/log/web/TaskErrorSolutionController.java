package com.alibaba.emas.mtl4.services.log.web;

import com.alibaba.emas.mtl4.log.api.TaskErrorSolutionService;
import com.alibaba.emas.mtl4.log.model.TaskErrorSolution;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * task错误解决方案api
 *
 * <AUTHOR>
 * @date 2023/5/18
 */
@Api
@Slf4j
@RestController
@RequestMapping("/api/v1/taskErrorSolution")
public class TaskErrorSolutionController {

    @Autowired
    private TaskErrorSolutionService taskErrorSolutionService;

    @GetMapping("/getTaskKeyErrorSolution")
    @ApiOperation("查询任务的关键错误信息和解决方案")
    public TaskErrorSolution getTaskKeyErrorSolution(Long taskInstanceId) {
        return taskErrorSolutionService.getTaskKeyErrorSolution(taskInstanceId);
    }

    @PostMapping("/batchGetTaskKeyErrorSolution")
    @ApiOperation("批量查询任务的关键错误信息和解决方案")
    public List<TaskErrorSolution> batchGetTaskKeyErrorSolution(@RequestBody List<Long> taskInstanceIdList) {
        return taskErrorSolutionService.batchGetTaskKeyErrorSolution(taskInstanceIdList);
    }

}
