package com.alibaba.emas.mtl4.services.insight.executer;

import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.services.insight.command.InsightRuleSetDisableCmd;
import com.alibaba.emas.mtl4.services.insight.gateway.InsightRuleSetGateway;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
public class InsightRuleSetDisableCmdExe implements ExeI<InsightRuleSetDisableCmd, Void> {
    private final InsightRuleSetDisableCmdValidator validator;
    private final InsightRuleSetGateway insightRuleSetGateway;

    public InsightRuleSetDisableCmdExe(InsightRuleSetDisableCmdValidator validator, InsightRuleSetGateway insightRuleSetGateway) {
        this.validator = validator;
        this.insightRuleSetGateway = insightRuleSetGateway;
    }

    @Transactional(rollbackFor = Throwable.class)
    public BizResult<Void> execute(InsightRuleSetDisableCmd command) {
        this.validator.validate(command);

        this.insightRuleSetGateway.disable(command.getRuleSetId(), command.getOperator());

        return BizResult.success(null);
    }
}
