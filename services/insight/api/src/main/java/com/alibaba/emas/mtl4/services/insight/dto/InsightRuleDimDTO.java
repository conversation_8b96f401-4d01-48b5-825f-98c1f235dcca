package com.alibaba.emas.mtl4.services.insight.dto;

import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * 洞察规则维度模型
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class InsightRuleDimDTO {
    /**
     * 维度名称
     **/
    private String name;

    /**
     * 维度唯一标志
     **/
    private String identifier;

    /**
     * 维度数据类型
     **/
    private String dataType;

    /**
     * 是否必选
     **/
    private Boolean isRequired;

    /**
     * 维度描述
     **/
    private String desc;

    /**
     * 维度链接
     **/
    private String href;

    /**
     * 业务属性
     **/
    private String bizAttr;
}
