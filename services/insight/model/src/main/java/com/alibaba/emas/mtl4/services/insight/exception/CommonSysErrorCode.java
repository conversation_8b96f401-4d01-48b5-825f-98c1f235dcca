package com.alibaba.emas.mtl4.services.insight.exception;

import lombok.Getter;

@Getter
public enum CommonSysErrorCode implements SysErrorCode {
    /**
     * 系统异常
     */
    SYS_ERROR("System", "系统异常"),
    DATABASE_ERROR("Database", "数据库异常"),
    ;
    private final String dependencyType;

    private final String messagePattern;

    CommonSysErrorCode(String dependencyType, String messagePattern) {
        this.dependencyType = dependencyType;
        this.messagePattern = messagePattern;
    }
}
