package com.alibaba.emas.mtl4.services.insight.exception;

import lombok.Getter;

/**
 * 系统异常
 * 这里约束必须传message，所以不提供无message的构造
 */
@Getter
public class SysException extends RuntimeException {
    private final SysErrorCode errorCode;

    public String getErrorCodeStr() {
        return errorCode.getErrorCode();
    }

    public SysException(String message, SysErrorCode errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public SysException(String message, Throwable cause, SysErrorCode errorCode) {
        super(message, cause);
        this.errorCode = errorCode;
    }
}
