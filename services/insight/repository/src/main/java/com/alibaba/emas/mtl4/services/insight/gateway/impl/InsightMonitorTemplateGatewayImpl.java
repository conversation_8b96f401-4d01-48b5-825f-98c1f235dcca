package com.alibaba.emas.mtl4.services.insight.gateway.impl;

import com.alibaba.emas.mtl4.services.insight.entity.InsightMonitorTemplate;
import com.alibaba.emas.mtl4.services.insight.gateway.InsightMonitorTemplateGateway;
import com.alibaba.emas.mtl4.services.insight.gateway.impl.mapper.InsightMonitorTemplateMapper;
import com.alibaba.emas.mtl4.services.insight.repository.InsightMonitorDORepository;
import com.alibaba.emas.mtl4.services.insight.repository.model.InsightMonitorDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;

@Slf4j
@Component
public class InsightMonitorTemplateGatewayImpl implements InsightMonitorTemplateGateway {
    private final InsightMonitorDORepository repository;

    private final InsightMonitorTemplateMapper mapper;

    public InsightMonitorTemplateGatewayImpl(InsightMonitorDORepository repository, InsightMonitorTemplateMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    @Override
    public InsightMonitorTemplate save(InsightMonitorTemplate insightMonitorTemplate, String operator) {
        Date date = new Date();
        if (insightMonitorTemplate.getId() == null) {
            insightMonitorTemplate.setGmtCreate(date);
            insightMonitorTemplate.setCreator(operator);
        }
        insightMonitorTemplate.setGmtModified(date);
        insightMonitorTemplate.setModifier(operator);

        InsightMonitorDO templateDO = this.repository.save(mapper.toDO(insightMonitorTemplate));
        return mapper.toBO(templateDO);
    }

    @Override
    public Optional<InsightMonitorTemplate> findById(Long id) {
        return this.repository.findById(id)
                .map(mapper::toBO);
    }

    @Override
    public void remove(InsightMonitorTemplate insightMonitorTemplate) {
        this.repository.deleteById(insightMonitorTemplate.getId());
    }
}
