package com.alibaba.emas.mtl4.services.insight.gateway.impl.mapper;

import com.alibaba.emas.motu.insight.command.InsightJobAddCommand;
import com.alibaba.emas.motu.insight.dto.*;
import com.alibaba.emas.motu.insight.dto.action.InsightJobTaskActionDTO;
import com.alibaba.emas.motu.insight.type.InsightDim;
import com.alibaba.emas.motu.insight.type.InsightMetric;
import com.alibaba.emas.mtl4.services.insight.entity.InsightRule;
import com.alibaba.emas.mtl4.services.insight.repository.model.InsightRuleDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface InsightRuleMapper {
    @Mapping(target = "scheduleProcessId", ignore = true)
    @Mapping(target = "insightRuleParams", ignore = true)
    @Mapping(target = "insightJobId", ignore = true)
    InsightRule convert(InsightRuleDO source);


    List<InsightRule> convert(List<InsightRuleDO> insightRuleDOS);


    InsightRuleDO convert(InsightRule insightRule);

    default InsightJobAddCommand toAddCommand(InsightRule insightRule, String operator) {
        InsightJobAddCommand addCommand = new InsightJobAddCommand();
        addCommand.setOperator(operator);
        addCommand.setDescription(insightRule.getDesc());
        addCommand.setAdmins(Collections.singleton(insightRule.getCreator()));
        addCommand.setRuleConfigs(toRuleConfigs(insightRule));
        addCommand.setDataConfig(toDataConfig(insightRule));
        addCommand.setScheduleConfig(toScheduleConfig(insightRule));
        addCommand.setEngineType(insightRule.getInfo().getInsightJobEngineType());
        addCommand.setCustomType(insightRule.getInfo().getInsightJobCustomType());
        return addCommand;
    }

    default InsightJobScheduleConfigDTO toScheduleConfig(InsightRule insightRule) {
        InsightJobScheduleConfigDTO scheduleConfigDTO = new InsightJobScheduleConfigDTO();
        if (insightRule.fetchInsightJobCronExpression() != null) {
            scheduleConfigDTO.setScheduleType("CRON");
            scheduleConfigDTO.setScheduleExpression(insightRule.fetchInsightJobCronExpression());
        } else {
            scheduleConfigDTO.setScheduleType("FIX_RATE");
            scheduleConfigDTO.setScheduleExpression(insightRule.fetchInsightJobPeriod().toString());
        }
        return scheduleConfigDTO;
    }

    default InsightJobDataConfigDTO toDataConfig(InsightRule insightRule) {
        InsightJobDataConfigDTO dataConfigDTO = new InsightJobDataConfigDTO();
        dataConfigDTO.setDataSourceId(insightRule.fetchInsightJobDataSourceId());
        dataConfigDTO.setDims(insightRule.fetchAllInsightRuleParams().stream()
                .map(insightRuleParam -> new InsightDim(insightRuleParam.getIdentifier(),
                        insightRuleParam.getValues(), insightRuleParam.isAll())).collect(Collectors.toList()));
        //
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(insightRule.getMetrics())) {
            dataConfigDTO.setMetrics(insightRule.getMetrics().stream()
                    .map(insightRuleMetric -> new InsightMetric(insightRuleMetric.getIdentifier()))
                    .collect(Collectors.toList()));
        }
        dataConfigDTO.setTop(insightRule.fetchInsightJobTop());
        return dataConfigDTO;
    }

    default List<InsightJobRuleConfigDTO> toRuleConfigs(InsightRule insightRule) {
        List<InsightJobRuleConfigDTO> ruleConfigDTOS = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(insightRule.getItems())) {
            insightRule.getItems().forEach(insightRuleItem -> {
                InsightJobTaskActionDTO actionDTO = new InsightJobTaskActionDTO();
                actionDTO.setLevel(insightRuleItem.getSeverity());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(insightRule.getHandlers())) {
                    actionDTO.setNotifiers(insightRule.getHandlers());
                } else {
                    actionDTO.setNotifiers(Collections.singleton(insightRule.getCreator()));
                }
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(insightRule.getWebhooks())) {
                    actionDTO.setWebhooks(insightRule.getWebhooks());
                }
                actionDTO.setType("MUST_FIX");
                ruleConfigDTOS.add(new InsightJobRuleConfigDTO(new InsightJobRuleDTO(insightRuleItem.getRuleStr()),
                        Collections.singletonList(InsightJobActionDTO.from(actionDTO))));
            });
        }
        return ruleConfigDTOS;
    }
}
