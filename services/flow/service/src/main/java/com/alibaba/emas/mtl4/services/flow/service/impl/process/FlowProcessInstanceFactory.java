package com.alibaba.emas.mtl4.services.flow.service.impl.process;

import java.util.List;
import java.util.stream.Collectors;

import com.alibaba.emas.mtl4.services.flow.model.process.FlowProcessInstance;
import com.alibaba.emas.mtl4.services.flow.model.process.domain.FlowProcessSnapshotDO;
import com.alibaba.emas.mtl4.services.flow.model.process.exception.FlowProcessInstanceException;
import com.alibaba.emas.mtl4.services.flow.model.process.exception.domain.FlowProcessInstanceExceptionDO;
import com.alibaba.emas.mtl4.services.flow.model.process.snapshot.FlowProcessSnapshot;
import com.alibaba.emas.mtl4.services.flow.model.process.task.FlowTaskInstance;
import com.alibaba.emas.mtl4.services.flow.repository.FlowProcessInstanceExceptionRepository;
import com.alibaba.emas.mtl4.services.flow.repository.FlowProcessSnapshotRepository;

import org.apache.commons.collections.CollectionUtils;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricActivityInstanceQuery;
import org.flowable.engine.history.HistoricProcessInstance;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class FlowProcessInstanceFactory {
    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private FlowProcessInstanceExceptionRepository flowProcessInstanceExceptionRepository;

    @Autowired
    private FlowProcessSnapshotRepository flowProcessSnapshotRepository;

    public FlowProcessInstance convert2FlowProcessInstance(HistoricProcessInstance processInstance,
        Long flowProcessId) {
        FlowProcessInstance instance = new FlowProcessInstance();
        instance.setProcessInstanceId(processInstance.getId());
        instance.setProcessDefinitionId(processInstance.getProcessDefinitionId());
        instance.setProcessDefinitionKey(processInstance.getProcessDefinitionKey());
        instance.setProcessDefinitionName(processInstance.getProcessDefinitionName());
        instance.setProcessDefinitionVersion(processInstance.getProcessDefinitionVersion());
        instance.setDeploymentId(processInstance.getDeploymentId());
        instance.setStartTime(processInstance.getStartTime());
        instance.setStartActivityId(processInstance.getStartActivityId());
        instance.setEndTime(processInstance.getEndTime());
        instance.setEndActivityId(processInstance.getEndActivityId());
        instance.setDurationInMillis(processInstance.getDurationInMillis());
        instance.setProcessVariables(processInstance.getProcessVariables());
        if (null != processInstance.getProcessDefinitionVersion() && null != flowProcessId) {
            FlowProcessSnapshotDO flowProcessSnapshotDO = flowProcessSnapshotRepository
                .findFirstByFlowProcessIdAndVersion(flowProcessId,
                    processInstance.getProcessDefinitionVersion().longValue());
            instance.setFlowProcessSnapshot(this.convert2BO(flowProcessSnapshotDO));
        }
        List<FlowProcessInstanceExceptionDO> exceptionDOS = flowProcessInstanceExceptionRepository
            .findAllByProcessInstanceId(processInstance.getId());

        instance.setExceptions(exceptionDOS.stream().map(aDo -> {
            FlowProcessInstanceException bo = new FlowProcessInstanceException();
            BeanUtils.copyProperties(aDo, bo);
            return bo;
        }).collect(Collectors.toList()));
        HistoricActivityInstanceQuery activityInstanceQuery = processEngine.getHistoryService()
            .createHistoricActivityInstanceQuery();
        activityInstanceQuery.processInstanceId(processInstance.getId());
        List<HistoricActivityInstance> activityInstances = activityInstanceQuery.list();
        if (CollectionUtils.isNotEmpty(activityInstances)) {
            instance.setTaskInstances(activityInstances.stream()
                .filter(activityInstance -> activityInstance.getActivityType().equals("serviceTask")
                    || activityInstance.getActivityType().equals("startEvent"))
                .map(activityInstance -> {
                    FlowTaskInstance flowTaskInstance = new FlowTaskInstance();
                    flowTaskInstance.setProcessInstanceId(activityInstance.getProcessInstanceId());
                    flowTaskInstance.setActivityId(activityInstance.getActivityId());
                    flowTaskInstance.setActivityType(activityInstance.getActivityType());
                    flowTaskInstance.setExecutionId(activityInstance.getExecutionId());
                    flowTaskInstance.setId(activityInstance.getId());
                    flowTaskInstance.setTransactionOrder(activityInstance.getTransactionOrder());
                    flowTaskInstance.setProcessDefinitionId(activityInstance.getProcessDefinitionId());
                    flowTaskInstance.setEndTime(activityInstance.getEndTime());
                    flowTaskInstance.setProcessInstanceId(activityInstance.getProcessInstanceId());
                    flowTaskInstance.setStartTime(activityInstance.getStartTime());
                    flowTaskInstance.setDurationInMillis(activityInstance.getDurationInMillis());
                    flowTaskInstance.setActivityName(activityInstance.getActivityName());
                    return flowTaskInstance;
                }).collect(Collectors.toList()));
        }

        instance.completeStatus();
        return instance;
    }

    private FlowProcessSnapshot convert2BO(FlowProcessSnapshotDO flowProcessSnapshotDO) {
        FlowProcessSnapshot flowProcessSnapshot = new FlowProcessSnapshot();
        BeanUtils.copyProperties(flowProcessSnapshotDO, flowProcessSnapshot);
        return flowProcessSnapshot;
    }
}
