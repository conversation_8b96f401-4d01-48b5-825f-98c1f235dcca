package com.alibaba.emas.mtl4.services.flow.service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.alibaba.emas.mtl4.commons.utils.IpUtils;
import com.alibaba.emas.mtl4.commons.utils.monitor.MonitorClient;
import com.alibaba.emas.mtl4.commons.utils.monitor.MonitorItem;
import com.alibaba.emas.mtl4.services.flow.model.common.FlowConstants;
import com.alibaba.emas.mtl4.services.flow.model.monitor.FlowMonitorConstants;
import com.alibaba.emas.mtl4.services.flow.model.process.exception.domain.FlowProcessInstanceExceptionDO;
import com.alibaba.emas.mtl4.services.flow.repository.FlowProcessInstanceExceptionRepository;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEventType;
import org.flowable.common.engine.api.delegate.event.FlowableEventDispatcher;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandConfig;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.ProcessEngine;
import org.flowable.job.api.Job;
import org.flowable.job.api.JobInfo;
import org.flowable.job.service.InternalJobCompatibilityManager;
import org.flowable.job.service.JobServiceConfiguration;
import org.flowable.job.service.event.impl.FlowableJobEventBuilder;
import org.flowable.job.service.impl.asyncexecutor.AsyncRunnableExecutionExceptionHandler;
import org.flowable.job.service.impl.asyncexecutor.FailedJobCommandFactory;
import org.flowable.job.service.impl.persistence.entity.AbstractRuntimeJobEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import static com.alibaba.emas.mtl4.services.flow.model.common.FlowConstants.NO_OUTGOING_SEQUENCE_EXCEPTION;
import static com.alibaba.emas.mtl4.services.flow.model.monitor.FlowMonitorConstants.FLOW_PROCESS_ERROR_COUNT;
import static com.alibaba.emas.mtl4.services.flow.model.process.util.FlowUtils.fetchFlowProcessId;

@Component
public class CustomAsyncRunnableExecutionExceptionHandler implements AsyncRunnableExecutionExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger("AsyncRunnableExecutionExceptionHandler");
    private final MonitorClient monitorClient = new MonitorClient(FlowMonitorConstants.FLOW_MONITOR);
    private ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(10);

    @Lazy
    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private FlowProcessInstanceExceptionRepository flowProcessInstanceExceptionRepository;

    @Override
    public boolean handleException(final JobServiceConfiguration jobServiceConfiguration, final JobInfo job,
        final Throwable exception) {
        jobServiceConfiguration.getCommandExecutor().execute(new Command<Void>() {

            @Override
            public Void execute(CommandContext commandContext) {
                Job detailJob = processEngine.getManagementService().createJobQuery().jobId(job.getId()).singleResult();
                if (null != detailJob && null != exception) {
                    FlowProcessInstanceExceptionDO exceptionDO = new FlowProcessInstanceExceptionDO();
                    StringWriter sw = new StringWriter();
                    PrintWriter pw = new PrintWriter(sw);
                    exception.printStackTrace(pw);
                    String exceptionString = sw.toString();

                    if (exceptionString.contains(NO_OUTGOING_SEQUENCE_EXCEPTION)) {
                        deleteProcessInstance(detailJob.getProcessInstanceId(), NO_OUTGOING_SEQUENCE_EXCEPTION);
                    } else { //如果是NO_OUTGOING_SEQUENCE_EXCEPTION类型的异常就不进行保存了
                        exceptionDO.setException(exceptionString);
                        exceptionDO.setExceptionSummary(exception.getMessage());
                        exceptionDO.setElementId(detailJob.getElementId());
                        exceptionDO.setElementName(detailJob.getElementName());
                        exceptionDO.setGmtCreate(new Date());
                        exceptionDO.setJobId(detailJob.getId());
                        exceptionDO.setProcessDefinitionId(detailJob.getProcessDefinitionId());
                        exceptionDO.setProcessInstanceId(detailJob.getProcessInstanceId());
                        exceptionDO.setIp(IpUtils.getCurrentIp());

                        flowProcessInstanceExceptionRepository.save(exceptionDO);

                        monitorClient.recordFlowMonitorItem(MonitorItem.builder()
                            .monitor(FLOW_PROCESS_ERROR_COUNT)
                            .dim1(fetchFlowProcessId(StringUtils.substringBefore(detailJob.getProcessDefinitionId(), ":")))
                            .metric1(1L)
                            .build());

                        String message = "Flow Job " + job.getId() + "," + detailJob.getProcessInstanceId() + "," + job.getRetries() + " failed";
                        LOGGER.error(message, exception);

                        //deleteProcessInstance(detailJob.getProcessInstanceId(), exception.getMessage());
                    }
                } else {
                    String message = "Flow Job " + job.getId() + " failed";
                    LOGGER.error(message, exception);
                }

                if (job instanceof AbstractRuntimeJobEntity) {
                    AbstractRuntimeJobEntity runtimeJob = (AbstractRuntimeJobEntity)job;
                    InternalJobCompatibilityManager internalJobCompatibilityManager
                        = jobServiceConfiguration.getInternalJobCompatibilityManager();
                    if (internalJobCompatibilityManager != null && internalJobCompatibilityManager.isFlowable5Job(
                        runtimeJob)) {
                        internalJobCompatibilityManager.handleFailedV5Job(runtimeJob, exception);
                        return null;
                    }
                }

                CommandConfig commandConfig = jobServiceConfiguration.getCommandExecutor().getDefaultConfig()
                    .transactionRequiresNew();
                FailedJobCommandFactory failedJobCommandFactory = jobServiceConfiguration.getFailedJobCommandFactory();
                Command<Object> cmd = failedJobCommandFactory.getCommand(job.getId(), exception);

                LOGGER.info("Using FailedJobCommandFactory '{}' and command of type '{}'",
                    failedJobCommandFactory.getClass(), cmd.getClass());
                jobServiceConfiguration.getCommandExecutor().execute(commandConfig, cmd);

                // Dispatch an event, indicating job execution failed in a
                // try-catch block, to prevent the original exception to be swallowed
                FlowableEventDispatcher eventDispatcher = jobServiceConfiguration.getEventDispatcher();
                if (eventDispatcher != null && eventDispatcher.isEnabled()) {
                    try {
                        eventDispatcher.dispatchEvent(FlowableJobEventBuilder.createEntityExceptionEvent(
                                FlowableEngineEventType.JOB_EXECUTION_FAILURE, job, exception),
                            jobServiceConfiguration.getEngineName());
                    } catch (Throwable ignore) {
                        LOGGER.warn("Exception occurred while dispatching job failure event, ignoring.", ignore);
                    }
                }
                return null;
            }
        });

        return true;
    }

    public void deleteProcessInstance(String processInstanceId, String reason) {
        scheduledExecutorService.schedule(() -> {
            try {
                LOGGER.info(">>>deleteProcessInstance:{}", processInstanceId);
                processEngine.getRuntimeService().deleteProcessInstance(processInstanceId, reason);
            } catch (Exception e) {
                LOGGER.error("DeleteInstanceRecordFromRu error.", e);
            }
        }, 1, TimeUnit.MINUTES);
    }
}
