package com.alibaba.emas.mtl4.services.flow.service.test.data;

import java.io.File;
import java.util.List;

import com.alibaba.emas.mtl4.services.dev.versionPlan.model.VersionPlanBO;
import com.alibaba.fastjson.JSON;

import org.apache.commons.io.FileUtils;

public class VersionPlanGenerator {
    public static List<VersionPlanBO> genVersionPlans() {
        return JSON.parseArray(genData("version_plans_1.json"), VersionPlanBO.class);
    }

    private static String genData(String resource) {
        try {
            ClassLoader classLoader = VersionPlanGenerator.class.getClassLoader();
            File file = new File(classLoader.getResource(resource).getFile());
            return FileUtils.readFileToString(file, "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
