package com.alibaba.emas.mtl4.services.flow.application;

import com.taobao.pandora.boot.PandoraBootstrap;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.alibaba.emas.mtl4"})
@EnableAspectJAutoProxy(exposeProxy=true)
public class FlowApplication {

    public static void main(String[] args) {
        PandoraBootstrap.run(args);
        SpringApplication.run(FlowApplication.class, args);
        PandoraBootstrap.markStartupAndWait();
    }
}