package com.alibaba.emas.mtl4.services.flow.model.process.exception;

import java.util.Date;

import lombok.Data;

/**
 * 工作流实例异常
 */
@Data
public class FlowProcessInstanceException {
    //id
    private Long id;

    //创建时间
    private Date gmtCreate;

    //工作流实例id
    private String processInstanceId;

    //工作流定义id
    private String processDefinitionId;

    //flowable任务id
    private String jobId;

    //flowable元素id
    private String elementId;

    //flowable元素name
    private String elementName;

    //异常
    private String exception;

    private String exceptionSummary;

    private String ip;
}
