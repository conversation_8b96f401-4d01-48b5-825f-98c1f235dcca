package com.alibaba.emas.mtl4.services.flow.model.work.start;

import java.util.List;

import com.alibaba.emas.mtl4.services.flow.model.process.task.FlowTaskParamDefinition;
import com.alibaba.emas.mtl4.services.flow.model.work.WorkflowConstants;

import lombok.Data;

@Data
public class IdentityTrigger extends AppServiceTrigger {
    {
        type = WorkflowConstants.IdentityTrigger;
    }

    /**
     * 触发器唯一标志（英文）
     */
    private String identity;

    /**
     * 触发器输出定义
     */
    private List<FlowTaskParamDefinition> outputDefs;

    @Override
    public boolean equals(Object o) {
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
