package com.alibaba.emas.mtl4.services.flow.model.request;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.emas.mtl4.services.flow.model.annotation.FlowRequestParam;
import lombok.Data;
import static com.alibaba.emas.mtl4.services.flow.model.common.FlowDefinitionInfo.ALTER_SHEET_ID;
import static com.alibaba.emas.mtl4.services.flow.model.common.FlowDefinitionInfo.EXECUTOR;
import static com.alibaba.emas.mtl4.services.flow.model.common.FlowDefinitionInfo.MODULE_IDS;

@Data
public class BuildAlterSheetModulePackageRequest {
    @FlowRequestParam(info = ALTER_SHEET_ID)
    private Long alterSheetId;

    @FlowRequestParam(info = MODULE_IDS)
    private List<Long> moduleIds;

    @FlowRequestParam(info = EXECUTOR)
    private String executor;

    @FlowRequestParam(name = "额外构建参数", required = false)
    private Map<String, String> otherBuildParamsMap;
}
