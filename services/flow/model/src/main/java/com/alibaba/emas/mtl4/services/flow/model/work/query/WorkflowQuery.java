package com.alibaba.emas.mtl4.services.flow.model.work.query;

import java.util.List;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowQuery {

    private List<Long> ids;

    private List<String> scopes;

    private String scope;

    private Long spaceId;

    private Long templateId;

    private String status;

    private String stage;

    private String keyword;

    private Long stepId;

    private String creator;

    private Set<String> creators;

    private Long gmtCreateStart;

    private String mainEntityType;

    private Long mainEntityId;

    private Long mainEntityAppId;

    private String mainEntityKeyword;

    private String mainEntityStatus;

    private List<String> mainEntityStatuses;

    private String mainEntityUuid;

    private List<String> mainEntityUuids;

    private List<Long> mainEntityIds;

    private Boolean withScope;

    private Long sourceAlterSheetId;

    private Boolean hasVersionPlan;

    private Long versionPlanId;

    private Boolean showExperiment;
}
