package com.alibaba.emas.mtl4.services.flow.repository;

import java.util.List;
import java.util.Set;

import com.alibaba.emas.mtl4.services.flow.repository.domain.WorkflowStepDO;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface WorkflowStepRepository extends JpaRepository<WorkflowStepDO, Long>,
    JpaSpecificationExecutor<WorkflowStepDO> {

    @Query(value = "select step.id from `emas_mtl4_workflow_step` step join `emas_mtl4_workflow` workflow on json_contains(workflow.step_ids, cast(step.id as char))", nativeQuery = true)
    List<Long> findAllWorkflowUsedSteps();

    @Query(value = "select step.id from `emas_mtl4_workflow_step` step join `emas_mtl4_workflow_template` template on json_contains(template.step_ids, cast(step.id as char))", nativeQuery = true)
    List<Long> findAllWorkflowTemplateUsedSteps();

    List<WorkflowStepDO> findAllByIdNotIn(Set<Long> ids);

}
