package com.alibaba.emas.mtl4.services.flow.repository;

import java.util.List;

import com.alibaba.emas.mtl4.services.flow.model.process.domain.FlowProcessDO;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FlowProcessRepository extends JpaRepository<FlowProcessDO, Long>,
    JpaSpecificationExecutor<FlowProcessDO> {
    @Query(value = "SELECT *"
        + "FROM `emas_mtl4_flow_process` "
        + "WHERE MATCH(start_event_message_identities) AGAINST(:startEventMessageIdentity IN BOOLEAN MODE) "
        + "AND `status` = 'ACTIVE'"
        + "AND `type` = 'NORMAL'", nativeQuery = true)
    List<FlowProcessDO> findAllByStartEventMessageIdentity(@Param("startEventMessageIdentity") String startEventMessageIdentity);

    @Query(value = "SELECT DISTINCT(JSON_UNQUOTE(JSON_EXTRACT(`extra_info`, '$.templateValues.ALTER_SHEET_ID'))) "
        + "FROM `emas_mtl4_flow_process` "
        + "WHERE JSON_UNQUOTE(JSON_EXTRACT(`extra_info`, '$.templateValues.ALTER_SHEET_ID')) IS NOT NULL AND visible = false", nativeQuery = true)
    List<Long> findAlterSheetIdsHasAutomaticTasks();

    @Query(value = "SELECT DISTINCT(JSON_UNQUOTE(JSON_EXTRACT(`extra_info`, '$.templateValues.INTEGRATE_AREA_ID'))) "
        + "FROM `emas_mtl4_flow_process` "
        + "WHERE JSON_UNQUOTE(JSON_EXTRACT(`extra_info`, '$.templateValues.INTEGRATE_AREA_ID')) IS NOT NULL AND visible = false", nativeQuery = true)
    List<Long> findIntegrateAreaIdsHasAutomaticTasks();
}
