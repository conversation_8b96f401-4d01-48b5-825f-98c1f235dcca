management:
  server:
    port: 7008
  endpoint:
    enabled: false
    info:
      enabled: true
    health:
      enabled: true
    env:
      post:
        enabled: false
    heapdump:
      enabled: false

server:
  port: 7007
  error:
    whitelabel:
      enabled: false
  servlet:
    contextPath: /agent

spring:
  application:
    name: mtl4-agent

swagger2:
  enable: true

emas:
  mtl4:
    host:
      address: https://mtl4.alibaba-inc.com
    agent:
      job_timeout_minutes: 120
      pull_job_fixed_rate: 2000
      debug: false
      group: mtl-default-agents
      version: 1.0.0
      register_file: ${HOME}/.emas/registerResult_online.json
      docker_support: false
      runner:
        only: false
        type: process
        uploadType: origin
        pipeline_job_instance_id: 20005119
        work_root_dir: ${HOME}/.emas/build/
        data_file_name: data
        jar_path: ${HOME}/siyuan-mtl4-agent-online
        jar_name: mtl4-services-worker-agent-1.0.0-SNAPSHOT.jar
      host_os:
      host_address:
      host_name:
      ##public_key 和 auth_key 需要配对出现
      public_key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtE/8IfkWtqG9nFRlXMhkZWhCx40jQXzJkG4wDVhxBuUdTDnVeypmhpZtjAmfbisLAu2LXtrXnNIYZD17hAFYua7eMi71o+O44rxuh7kn+udHm6cNll76EiPUbsX9swTZuFf/DqGKGIStwezz2yXPWO3IDVtG3I4NbM3+B02NAfZthpgb6Y/745IYKH69uFvs1l+cQ33YPTXjR7cFE+n81U/UaHMYaodkUFrENxyCdbUMh3BQm7JEEWuViKMwS1SmCsETi7ZytJ8m7DPyGE9nOrN2Pha4QhAKYKVTcLgiJZLkg1tdNPUB5j5z+a8xcGwBxXjPpra0DuvcqZMC+Y0w/wIDAQAB
      auth_key: vSuKueB5RiSDHnH8a71hyQ
    plugin:
      setup:
        root:
          dir: ${HOME}/.emas/plugin