management:
  server:
    port: 7012
  endpoint:
    enabled: false
    info:
      enabled: true
    health:
      enabled: true
    env:
      post:
        enabled: false
    heapdump:
      enabled: false

server:
  port: 7011
  error:
    whitelabel:
      enabled: false
  servlet:
    contextPath: /agent

spring:
  application:
    name: mtl4-agent

swagger2:
  enable: true

emas:
  mtl4:
    host:
      address: https://mtl4.alibaba-inc.com
    agent:
      job_timeout_minutes: 120
      pull_job_fixed_rate: 2000
      debug: false
      group: androidMac
      version: 1.0.0
      register_file: ${HOME}/.emas/registerResult_online_android_mac.json
      docker_support: false
      runner:
        only: false
        type: process
        uploadType: origin
        pipeline_job_instance_id: 20005119
        work_root_dir: ${HOME}/.emas/build/
        data_file_name: data
        jar_path: ${HOME}/siyuan-mtl4-agent-online-android-mac
        jar_name: mtl4-services-worker-agent-1.0.0-SNAPSHOT.jar
      host_os:
      host_address:
      host_name:
      ##public_key 和 auth_key 需要配对出现
      public_key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiL4+RWbB4mIdszPO/xi7wRWC0nrctl87/FM4VTgbR3h+Ayj1UNLGlHJ4Wo2YqpWFhZScJ/MxqgwD0Z18eFUociDNpivCLwVj6KxIFG1H72O+SChzht2Tjm/pj5Bf5UfM7aVNcpMEMa63ZBFIVXVcIM5HMmHzzKV2nzXmLGaFnKUuMRBbNYVfdwZHxbPVygI2HG2UJXrTJfCho4SmmV30BRY4Ho5gorF39hUE7oB30At1TDhmcDFppsWB1CXUym2t8OiDsw2EfHiGQ2SRtfQ6bZ+DkGDp0xCwZXZH2IGdGwSYF4sxnV2MvnOpfuI1y4FXq+8PbF9ILf3LkftcqaN5QwIDAQAB
      auth_key: 5BF4IZ8dSzaQpiHWNr7AuQ
    plugin:
      setup:
        root:
          dir: ${HOME}/.emas/plugin