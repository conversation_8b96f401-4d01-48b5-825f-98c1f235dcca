#!/usr/bin/env bash
function getIp {
ifconfig -a|grep inet|grep -v 127.0.0.1 | grep -v 192.168 |grep -v inet6|awk '{print $2; exit}'|tr -d "addr:" | grep  -o -E '[0123456789]+\.[0123456789]+\.[0123456789]+\.[0123456789]+'
}

function getGemVersion {
if [ `uname` = "Darwin" ]
then
	which gem 2>&1>/dev/null && gem --version || echo "-"
fi
}

function getBundlerVersion {
if [ `uname` = "Darwin" ]
then
	which bundle 2>&1>/dev/null && bundle --version | awk '{print $3}' || echo "-"
fi
}

function getCurrentJDKVersion {
which java 2>&1>/dev/null && java -version 2>&1 | awk '/version/ {print $3}' | sed "s/\"//g" || echo "-" 
} 

function getXcprettyVersion {
if [ `uname` = "Darwin" ]
then
	which xcpretty 2>&1>/dev/null && xcpretty -v || echo "-" 
fi
}

function getXcodeSelectVersion {
if [ `uname` = "Darwin" ]
then
	which xcode-select 2>&1>/dev/null && xcode-select -v | awk '{print $3}' | sed "s/\.//g" || echo "-"
fi
}

function getiOSSdkVersion {
if [ `uname` = "Darwin" ]
then
	which xcodebuild 2>&1>/dev/null && xcodebuild -sdk -version 2>/dev/null | grep iPhoneOS | grep iOS | awk '{print $4}' || echo "-"
fi
}

function getXcodeVersion {
if [ `uname` = "Darwin" ]
then
	which xcodebuild 2>&1>/dev/null && xcodebuild -version | awk '{if ($1 == "Xcode"){print $2; exit}}' || echo "-"
fi
}

function getRvmVersion {
if [ `uname` = "Darwin" ]
then
	which rvm 2>&1>/dev/null && rvm -v | awk '{print $2}' || echo "-"
fi
}

function getRubyVersion {
if [ `uname` = "Darwin" ]
then
	which ruby 2>&1>/dev/null && ruby -v | awk '{print $2}' || echo "-"
fi
}

function getNodeJsVersion {
which node 2>&1>/dev/null && node -v || echo "-"
}

function getCocoapodsVersion {
if [ `uname` = "Darwin" ]
then
	which pod 2>&1>/dev/null && pod --version || echo "-"
fi
}

function getPythonVersion {
echo -e "import platform\npython_version=platform.python_version()\nprint (python_version)" > /tmp/check-python-version.py
python /tmp/check-python-version.py || echo "-"
}

function echoAndroid {
currentJDKVersion=`getCurrentJDKVersion`
nodeJsVersion=`getNodeJsVersion`
pythonVersion=`getPythonVersion`
echo "currentJDKVersion::$currentJDKVersion"
echo "nodeJsVersion::$nodeJsVersion"
echo "pythonVersion::$pythonVersion"
}

function echoiOS {
bundlerVersion=`getBundlerVersion` 
currentJDKVersion=`getCurrentJDKVersion` 
xcprettyVersion=`getXcprettyVersion` 
xcodeSelectVersion=`getXcodeSelectVersion` 
xcodeVersion=`getXcodeVersion` 
iOSSdkVersion=`getiOSSdkVersion`
rvmVersion=`getRvmVersion` 
rubyVersion=`getRubyVersion` 
cocoapodsVersion=`getCocoapodsVersion`
nodeJsVersion=`getNodeJsVersion`
pythonVersion=`getPythonVersion`
echo "bundlerVersion::$bundlerVersion"
echo "currentJDKVersion::$currentJDKVersion"
echo "xcprettyVersion::$xcprettyVersion"
echo "xcodeSelectVersion::$xcodeSelectVersion"
# 线上 hard code 11.2.1 给手淘，先把机器直接设为11.2.1 摩天轮4完全上线之后删除
#echo "xcodeVersion::11.2.1"
echo "xcodeVersion::$xcodeVersion"
echo "iOSSdkVersion::$iOSSdkVersion"
echo "rvmVersion::$rvmVersion"
echo "rubyVersion::$rubyVersion"
echo "nodeJsVersion::$nodeJsVersion"
echo "cocoapodsVersion::$cocoapodsVersion"
echo "pythonVersion::$pythonVersion"
}

ip=`getIp`
platform=`uname`
echo "ip::$ip"
echo "platform::$platform"
if [ `uname` = "Darwin" ]
then
	echoiOS
else
	echoAndroid
fi
