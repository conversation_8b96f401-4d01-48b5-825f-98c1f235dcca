#!/usr/bin/env bash
#若机器上没有该镜像（50G）会花蛮久时间去pull
image=$1
#image="reg.docker.alibaba-inc.com/siyuan/android_deploy_docker"
uid=$(id -u "${USER}")
repo=${image}${uid}
docker image pull ${image}
docker image inspect $repo 2>&1>/dev/null
if [ $? != 0 ]
then
	echo "FROM ${image}" > Dockerfile
    echo "USER root" >> Dockerfile
	echo "RUN usermod -u ${uid} admin" >> Dockerfile
	echo "RUN mkdir -p /home/<USER>/home/<USER>" >> Dockerfile
	echo "WORKDIR /home/<USER>/" >> Dockerfile
	echo "USER admin" >> Dockerfile
	docker build -t ${repo} .
fi
