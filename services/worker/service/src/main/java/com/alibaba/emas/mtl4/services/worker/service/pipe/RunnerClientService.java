package com.alibaba.emas.mtl4.services.worker.service.pipe;

import com.alibaba.emas.mtl4.commons.pipes.RunStatus;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.core.message.job.PipelineJobInstance;
import com.alibaba.emas.mtl4.services.worker.event.JobFinishEvent;
import com.alibaba.emas.mtl4.services.worker.event.JobFinishEventPublisher;
import com.alibaba.emas.mtl4.services.worker.service.JobStatusService;
import com.alibaba.emas.mtl4.services.worker.service.pipe.impl.OriginJobRunner;
import com.alibaba.emas.mtl4.services.worker.service.pipe.util.PipeUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.Charset;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@ConditionalOnProperty(value = "emas.mtl4.agent.runner.only", havingValue = "true")
public class RunnerClientService {
    @Value("${emas.mtl4.agent.runner.pipeline_job_instance_id:}")
    private String pipelineJobInstanceId;

    @Autowired
    private JobFinishEventPublisher jobFinishEventPublisher;

    @Autowired
    private JobStatusService jobStatusService;
    @Autowired
    private OriginJobRunner originJobRunner;

    @Value("${emas.mtl4.agent.runner.data_file_name:data}")
    private String dataFileName;

    @EventListener
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // run job when app fully started
        PipelineJobInstance pipelineJobInstance = null;
        try {
            log.info("run job: {}", pipelineJobInstanceId);
            pipelineJobInstance = restorePipelineJobInstance(pipelineJobInstanceId);
            jobStatusService.onJobRunnerStarted(pipelineJobInstance);
            originJobRunner.runJob(pipelineJobInstance);
        } catch (Exception e) {
            log.error("error when run job {}.", pipelineJobInstanceId, e);
        } finally {
            log.info("job finished: {}", pipelineJobInstanceId);
            jobStatusService.onJobRunnerFinished(pipelineJobInstance);
            if (pipelineJobInstance != null &&
                    (RunStatus.isSuccess(pipelineJobInstance.getJobExecuteStatus()) ||
                            RunStatus.isCancelled(pipelineJobInstance.getJobExecuteStatus()))) {
                jobFinishEventPublisher.publishEvent(0);
            } else {
                jobFinishEventPublisher.publishEvent(1);
            }
        }
    }

    private PipelineJobInstance restorePipelineJobInstance(String buildId) throws Exception {
        File dataFile = new File(PipeUtil.generatePipeSpacePath(buildId), dataFileName);
        Assert.isTrue(dataFile.exists());
        String dataJson = FileUtils.readFileToString(dataFile, Charset.defaultCharset());
        return JSON.parseObject(dataJson, PipelineJobInstance.class);
    }

    @EventListener
    public void onApplicationEvent(JobFinishEvent event) {
        stopAgent(event.getReturnValue());
    }

    private void stopAgent(Integer returnValue) {
        System.exit(returnValue);
    }
}