package com.alibaba.emas.mtl4.services.worker.utils;

import com.alibaba.emas.mtl4.commons.utils.WebResult;
import com.alibaba.fastjson.JSON;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.config.SocketConfig;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;

import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public final class HttpClientUtils {

    private static final int COMMON_TIME_OUT = 60 * 1000;

    private static final CloseableHttpClient httpClient = HttpClients
            .custom()
            .setUserAgent("AWP Http Client/1.0")
            .setDefaultRequestConfig(RequestConfig.custom()
                    .setConnectionRequestTimeout(COMMON_TIME_OUT)
                    .setConnectTimeout(COMMON_TIME_OUT)
                    .setSocketTimeout(COMMON_TIME_OUT)
                    .build())
            .setDefaultSocketConfig(SocketConfig.custom()
                    .setSoTimeout(COMMON_TIME_OUT)
                    .build())
            .disableCookieManagement()
            .build();

    private HttpClientUtils() {
    }

    public static HttpGet get(final String url,
                              final Map<String, String> urlParams,
                              final Map<String, String> headers) throws IOException, URISyntaxException {

        final URI uri = addUrlParams(url, urlParams);
        final HttpGet httpGet = new HttpGet(uri);
        addHeaders(httpGet, headers);

        return httpGet;
    }

    public static HttpPost post(final String url,
                                final Map<String, String> urlParams,
                                final Map<String, String> headers,
                                final Map<String, String> formParams) throws IOException, URISyntaxException {
        final URI uri = addUrlParams(url, urlParams);
        final HttpPost httpPost = new HttpPost(uri);
        addHeaders(httpPost, headers);

        if (formParams != null && !formParams.isEmpty()) {
            List<NameValuePair> paramsList = new ArrayList<>(formParams.size());
            for (Map.Entry<String, String> entry : formParams.entrySet()) {
                paramsList.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }

            HttpEntity postEntity = new UrlEncodedFormEntity(paramsList, Charset.forName("utf-8"));
            httpPost.setEntity(postEntity);
        }

        return httpPost;
    }

    public static HttpPost postString(final String url,
                                      final Map<String, String> urlParams,
                                      final Map<String, String> headers,
                                      final String bodyString) throws IOException, URISyntaxException {
        final URI uri = addUrlParams(url, urlParams);
        final HttpPost httpPost = new HttpPost(uri);
        addHeaders(httpPost, headers);

        if (bodyString != null) {
            StringEntity stringEntity = new StringEntity(bodyString);
            httpPost.setEntity(stringEntity);
        }

        return httpPost;
    }

    public static byte[] bytesResponse(final HttpUriRequest request) throws IOException {
        final CloseableHttpResponse response = httpClient.execute(request);
        InputStream inputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            final int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                throw new RuntimeException(String.format("bad HTTP status code %s", statusCode));
            }

            final HttpEntity httpEntity = response.getEntity();
            if (httpEntity == null) {
                return null;
            }

            final int bufferSize;
            final long contentLength = httpEntity.getContentLength();
            if (contentLength <= 0) {
                bufferSize = 1024;
            } else if (contentLength > Integer.MAX_VALUE) {
                throw new RuntimeException(String.format("data too large, size: %s", contentLength));
            } else {
                bufferSize = (int) contentLength;
            }

            inputStream = httpEntity.getContent();
            byteArrayOutputStream = new ByteArrayOutputStream(bufferSize);

            int data;
            while ((data = inputStream.read()) != -1) {
                byteArrayOutputStream.write(data);
            }

            return byteArrayOutputStream.toByteArray();
        } finally {
            close(byteArrayOutputStream);
            close(inputStream);
            close(response);
        }
    }

    public static String stringResponse(final HttpUriRequest request, final Charset charset) throws IOException {
        final byte[] bytes = bytesResponse(request);
        if (bytes == null) {
            return null;
        }

        return new String(bytes, charset != null ? charset : StandardCharsets.UTF_8);
    }

    private static URI addUrlParams(final String url,
                                    final Map<String, String> urlParams) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder(url);

        if (urlParams != null && !urlParams.isEmpty()) {
            for (Map.Entry<String, String> entry : urlParams.entrySet()) {
                uriBuilder.addParameter(entry.getKey(), entry.getValue());
            }
        }

        return uriBuilder.build();
    }

    private static void addHeaders(final HttpUriRequest request,
                                   final Map<String, String> headers) {
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                request.addHeader(entry.getKey(), entry.getValue());
            }
        }
    }

    private static void close(final Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception ignored) {
            }
        }
    }
}
