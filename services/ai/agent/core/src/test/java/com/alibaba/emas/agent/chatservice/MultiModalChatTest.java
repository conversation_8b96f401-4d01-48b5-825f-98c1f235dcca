package com.alibaba.emas.agent.chatservice;

import com.alibaba.emas.agent.base.Kernel;
import com.alibaba.emas.agent.base.orchestration.FunctionResult;
import com.alibaba.emas.agent.base.semanticfunctions.KernelFunction;
import com.alibaba.emas.agent.base.semanticfunctions.KernelFunctionArguments;
import com.alibaba.emas.agent.base.semanticfunctions.KernelFunctionYaml;
import com.alibaba.emas.agent.base.services.chatcompletion.ChatCompletionService;
import com.alibaba.emas.agent.base.services.chatcompletion.ChatHistory;
import com.alibaba.emas.agent.base.services.chatcompletion.ChatMessageContent;
import com.alibaba.emas.agent.base.services.chatcompletion.message.ChatMessageImageContent;
import com.alibaba.emas.agent.ext.kernel.model.ModelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.*;
import java.nio.file.Paths;
import java.util.List;

/**
 * 多模态测试.
 *
 * <AUTHOR>
 * @date 2024/10/31
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class MultiModalChatTest {

    @Test
    public void imageContentChat() {
        ChatHistory chatHistory = getImageChatHistory();
        ChatCompletionService chatCompletionService = ModelEnum.getChatCompletionService(ModelEnum.GPT4O_0806);
        List<ChatMessageContent<?>> messageContentList = chatCompletionService.getChatMessageContentsAsync(chatHistory, null, null)
                .block();
        String content = messageContentList.get(0).getContent();
        Assert.assertTrue(StringUtils.contains(content, "ideaLAB"));
        log.info(content);
    }

    @Test
    public void imageContentChatV2() {
        ChatHistory chatHistory = getImageChatHistory1();
        ChatCompletionService chatCompletionService = new IdealabChatCompletionV2(ModelEnum.getLatestGptModelId());
        List<ChatMessageContent<?>> messageContentList = chatCompletionService.getChatMessageContentsAsync(chatHistory, null, null)
                .block();
        String content = messageContentList.get(0).getContent();
        Assert.assertTrue(StringUtils.contains(content, "ideaLAB"));
        log.info(content);
    }

    @Test
    public void imageContentKernelFunction() throws IOException {
        ChatHistory chatHistory = getImageChatHistory();
        Kernel kernel = Kernel.builder()
                .withAIService(ChatCompletionService.class, ModelEnum.getChatCompletionService(ModelEnum.GPT4O_0806))
                .build();
        KernelFunction<?> kernelFunction =  KernelFunctionYaml.fromYaml(Paths.get("agent/default.prompt.yml"));
        FunctionResult<?> functionResult = kernelFunction.invokeAsync(kernel)
                .withArguments(KernelFunctionArguments.builder()
                        .withVariable("messages", chatHistory)
                        .build())
                .block();
        String content = functionResult.getResult().toString();
        Assert.assertTrue(StringUtils.contains(content, "ideaLAB"));
        log.info(content);
    }

    @Test
    public void imageContentKernelFunctionV2() {
        ChatHistory chatHistory = getImageChatHistory();
        Kernel kernel = Kernel.builder()
                .withAIService(ChatCompletionService.class, ModelEnum.getChatCompletionService(ModelEnum.GPT4O_0806))
                .build();
        ChatCompletionService chatCompletionService = ModelEnum.getChatCompletionService(ModelEnum.GPT4O_0806);
        List<ChatMessageContent<?>> messageContentList = chatCompletionService.getChatMessageContentsAsync(chatHistory, kernel, null)
                .block();
        String content = messageContentList.get(0).getContent();
        Assert.assertTrue(StringUtils.contains(content, "ideaLAB"));
        log.info(content);
    }

    public static ChatHistory getImageChatHistory1() {
        try (InputStream inputStream = new FileInputStream(new File("/Users/<USER>/Downloads/idealab2.png"))) {
            byte[] image = IOUtils.toByteArray(inputStream);
            ChatHistory chatHistory = new ChatHistory("You look at images and answer questions about them");
            // First user message
            chatHistory.addUserMessage("描述一下这张图");
            chatHistory.addMessage(
                    ChatMessageImageContent.builder()
                            .withImage("png", image)
                            .build());
            return chatHistory;
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static ChatHistory getImageChatHistory() {
        ChatHistory chatHistory = new ChatHistory("You look at images and answer questions about them");
        // First user message
        chatHistory.addUserMessage("描述一下这张图");
        chatHistory.addMessage(
                ChatMessageImageContent.builder()
                        .withImageUrl("https://idealab-platform.oss-accelerate.aliyuncs.com/20231125/33808fdb-10ad-428c-8a76-11532ad93b15_idealab2.png?Expires=4102329600&OSSAccessKeyId=LTAI5tFJF3QLwHzEmkhLs9dB&Signature=YcbZrM98pHRRMd%2BGFaI2OiFf8Z8%3D")
                        .build());
        return chatHistory;
    }

}
