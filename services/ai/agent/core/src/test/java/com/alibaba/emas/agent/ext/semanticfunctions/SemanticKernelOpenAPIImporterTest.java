package com.alibaba.emas.agent.ext.semanticfunctions;

import com.alibaba.emas.agent.base.plugin.KernelPlugin;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;


/**
 * .
 *
 * <AUTHOR>
 * @date 2024/8/16
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class SemanticKernelOpenAPIImporterTest {

    @Test
    @Ignore
    public void fromSchema() {
        String path = "/Users/<USER>/Downloads/openapi_test.json";
//        path = "/Users/<USER>/dev/gitRepo/my/semantic-kernel-java/samples/semantickernel-sample-plugins/semantickernel-openapi-plugin/src/test/resources/adoptium.yaml";
        try (InputStream inputStream = new FileInputStream(path)) {
            String content = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            KernelPlugin plugin = SemanticKernelOpenAPIImporter
                    .builder()
                    .withPluginName("petstore")
                    .withSchema(content)
                    .withServer("http://localhost:8090/api/v3")
                    .build();

            log.info("111");
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testJsonSchema() {
        String json = getSwaggerJsonExample();
        KernelPlugin plugin = SemanticKernelOpenAPIImporter
                .builder()
                .withPluginName("petstore")
                .withSchema(json)
                .withServer("http://localhost:8090/api/v3")
                .build();
        Assertions.assertNotNull(plugin);
    }

    @Test
    public void testYamlSchema() {
        String yaml = getYamlJsonExample();
        KernelPlugin plugin = SemanticKernelOpenAPIImporter
                .builder()
                .withPluginName("petstore")
                .withSchema(yaml)
                .withServer("http://localhost:8090/api/v3")
                .build();
        Assertions.assertNotNull(plugin);
    }

    private String getSwaggerJsonExample() {
        return """
                {
                  "openapi": "3.0.0",
                  "info": {
                    "title": "示例 API",
                    "version": "1.0.0",
                    "description": "这是一个简单的示例 API"
                  },
                  "paths": {
                    "/hello": {
                      "get": {
                        "summary": "获取问候",
                        "operationId": "hello",
                        "responses": {
                          "200": {
                            "description": "成功获取问候",
                            "content": {
                              "application/json": {
                                "schema": {
                                  "type": "object",
                                  "properties": {
                                    "message": {
                                      "type": "string"
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    },
                    "/goodbye": {
                      "get": {
                        "summary": "获取告别",
                        "operationId": "goodbye",
                        "responses": {
                          "200": {
                            "description": "成功获取告别",
                            "content": {
                              "application/json": {
                                "schema": {
                                  "type": "object",
                                  "properties": {
                                    "message": {
                                      "type": "string"
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
        """;
    }

    private String getYamlJsonExample() {
        return """
                openapi: 3.0.0
                info:
                  title: 示例 API
                  version: 1.0.0
                  description: 这是一个简单的示例 API
                paths:
                  /hello:
                    get:
                      summary: 获取问候
                      operationId: hello,
                      responses:
                        '200':
                          description: 成功获取问候
                          content:
                            application/json:
                              schema:
                                type: object
                                properties:
                                  message:
                                    type: string
                  /goodbye:
                    get:
                      summary: 获取告别
                      operationId: goodbye
                      responses:
                        '200':
                          description: 成功获取告别
                          content:
                            application/json:
                              schema:
                                type: object
                                properties:
                                  message:
                                    type: string
        """;
    }

}