package com.alibaba.emas.agent.base.hooks;

import com.alibaba.emas.agent.base.semanticfunctions.KernelFunction;
import com.alibaba.emas.agent.base.semanticfunctions.KernelFunctionArguments;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

import javax.annotation.Nullable;
import java.util.Optional;

/**
 * Represents a KernelHookEvent that is raised after a tool choice is invoking.
 *
 * <AUTHOR>
 * @date 2024/3/6
 */
public class ToolCallingEvent<T> implements KernelHookEvent {

    private final KernelFunction<T> function;
    private final KernelFunctionArguments arguments;

    /**
     * Creates a new instance of the ToolCallingEvent class.
     *
     * @param function  The function that is being invoked
     * @param arguments The arguments that are being passed to the function
     */
    public ToolCallingEvent(KernelFunction<T> function,
                                 @Nullable KernelFunctionArguments arguments) {
        this.function = function;
        this.arguments = KernelFunctionArguments.builder()
                .withVariables(arguments)
                .withArguments(Optional.ofNullable(arguments).map(KernelFunctionArguments::getArguments).orElse(null))
                .build();
    }

    /**
     * Gets the function that is being invoked.
     *
     * @return the function
     */
    public KernelFunction<T> getFunction() {
        return function;
    }

    /**
     * Gets the arguments that are being passed to the function.
     *
     * @return the arguments
     */
    @SuppressFBWarnings("EI_EXPOSE_REP")
    public KernelFunctionArguments getArguments() {
        return arguments;
    }

}
