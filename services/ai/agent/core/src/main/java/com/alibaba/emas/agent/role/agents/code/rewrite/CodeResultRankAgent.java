package com.alibaba.emas.agent.role.agents.code.rewrite;

import com.alibaba.emas.agent.base.contextvariables.ContextVariable;
import com.alibaba.emas.agent.base.orchestration.FunctionResult;
import com.alibaba.emas.agent.base.semanticfunctions.KernelFunctionArguments;
import com.alibaba.emas.agent.base.services.chatcompletion.AuthorRole;
import com.alibaba.emas.agent.base.services.chatcompletion.ChatMessageContent;
import com.alibaba.emas.agent.base.services.chatcompletion.ObjectChatMessageContent;
import com.alibaba.emas.agent.ext.ouputparser.YamlOutputParser;
import com.alibaba.emas.agent.role.agents.Agent;
import com.alibaba.emas.agent.role.agents.ConversableAgent;
import com.alibaba.emas.agent.role.agents.code.rewrite.model.CodeRewriteRankRequest;
import com.alibaba.emas.agent.role.agents.code.rewrite.model.CodeRewriteRankResult;
import com.alibaba.emas.agent.role.model.ReplyResult;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 代码结果排名agent.
 *
 * <AUTHOR>
 * @date 2024/6/11
 */
@Slf4j
public class CodeResultRankAgent extends ConversableAgent {

    private static final int MAX_RETRY_COUNT = 10;

    private static final String RESPONSE_INSTRUCTIONS = """
            The output must be a YAML object equivalent to type $PRReview, according to the following Pydantic definitions:
            =====
                        
            class Result(BaseModel):
                option: int = Field(description="The selected rewrite result number")
                reason: str = Field(description="reasons for choosing this rewrite result")
                        
            =====
                        
            Example output:
            ```yaml
            Result:
              option: |
                ...
              reason: |
                ...
            ```
                        
            Answer should be a valid YAML, and nothing else. Each YAML output MUST be after a newline, with proper indent, and block scalar indicator ('|')
            
            """;

    /**
     * 排名权重.
     */
    private Double rankWrights;

    public CodeResultRankAgent(Builder builder) {
        super(builder);
        this.rankWrights = builder.rankWrights;
        if (Objects.isNull(outputParser)) {
            outputParser = new YamlOutputParser<>(CodeRewriteRankResult.class,
                    new String[]{"codeRewriteResult", "input"});
        }
        this.registerReplyFunc(this::runChat, 0);
    }

    private ReplyResult runChat(Agent agent, List<ChatMessageContent<?>> messageContentList) {
        ObjectChatMessageContent<?, CodeRewriteRankRequest> messageContent = (ObjectChatMessageContent<?,
                CodeRewriteRankRequest>) lastMessage(messageContentList);

        CodeRewriteRankRequest rankRequest = messageContent.getParams();

        Exception lastException = null;
        int i = 0;
        while (i++ < MAX_RETRY_COUNT) {

            KernelFunctionArguments arguments = rankRequest.getFunctionArguments();
            arguments.put(VAR_KEY_OUTPUT_INSTRUCTIONS, ContextVariable.ofGlobalType(outputParser.getFormatInstructions()));

            try {
                FunctionResult<?> result = kernel.invokeAsync(chatFunction)
                        .withArguments(arguments)
                        .withPromptExecutionSettings(promptExecutionSettings)
                        .block();

                Assert.notNull(result, "返回结果为空");
                String resultContent = Objects.toString(result.getResult());
                if (StringUtils.isBlank(resultContent)) {
                    continue;
                }

                CodeRewriteRankResult codeRewriteRankResult = (CodeRewriteRankResult) outputParser.parse(resultContent);
                codeRewriteRankResult.setCodeRewriteResult(rankRequest.getCodeRewriteResultList()
                        .get(codeRewriteRankResult.getOption()))
                        .setScore(this.rankWrights);

                ObjectChatMessageContent<?, CodeRewriteRankResult> chatMessageContent = new ObjectChatMessageContent<>(
                        AuthorRole.ASSISTANT, codeRewriteRankResult);
                return new ReplyResult(true, Lists.newArrayList(chatMessageContent));
            } catch (Exception e) {
                log.error("尝试排名失败，但依然在重试过程中，不打印错误堆栈");
                lastException = e;
            }
        }
        log.error(">>>经过{}次重试，对改写结果进行排名依然失败，上下文：【{}】", MAX_RETRY_COUNT, rankRequest, lastException);
        return new ReplyResult(true, Lists.newArrayList());
    }


    /**
     * 构建器.
     *
     * @return 构建器
     * @throws IOException 异常
     */
    public static Builder builder() throws IOException {
        return new Builder();
    }

    public static class Builder extends ConversableAgent.Builder<Builder> {

        /**
         * 排名权重.
         */
        private Double rankWrights = 1.0;

        @Override
        public CodeResultRankAgent build() {
            return new CodeResultRankAgent(this);
        }

        public CodeResultRankAgent.Builder rankWrights(Double rankWrights) {
            this.rankWrights = rankWrights;
            return this;
        }

    }

}
