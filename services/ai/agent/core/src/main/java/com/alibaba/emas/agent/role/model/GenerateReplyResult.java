package com.alibaba.emas.agent.role.model;

import com.alibaba.emas.agent.base.services.chatcompletion.ChatMessageContent;
import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用于封装生成回复的结果，包含回复内容和上下文变量.
 *
 * <AUTHOR>
 * @since 2025/2/17
 */
@Data
@Builder
public class GenerateReplyResult {

    /**
     * 回复消息列表
     */
    private List<ChatMessageContent<?>> replies;

    /**
     * 需要添加到会话上下文的变量
     */
    private Map<String, Object> contextVariables;

    /**
     * 需要添加到特定 Agent 的上下文变量
     * key: agent identifier
     * value: agent variables
     */
    private Map<String, Map<String, Object>> agentVariables;

    public static GenerateReplyResult of(List<ChatMessageContent<?>> replies) {
        return GenerateReplyResult.builder()
                .replies(replies)
                .contextVariables(new HashMap<>())
                .agentVariables(new HashMap<>())
                .build();
    }

}
