package com.alibaba.emas.agent.base.contextvariables.converters;


import com.alibaba.emas.agent.base.contextvariables.ContextVariable;
import com.alibaba.emas.agent.base.contextvariables.ContextVariableTypeConverter;

import javax.annotation.Nullable;

import static com.alibaba.emas.agent.base.contextvariables.ContextVariableTypes.convert;

/**
 * A {@link ContextVariableTypeConverter} for {@code java.lang.String} variables. Use
 * {@code ContextVariableTypes.getGlobalVariableTypeForClass(String.class)} to get an instance of
 * this class.
 *
 * @see ContextVariableTypes#getGlobalVariableTypeForClass(Class)
 */
public class StringVariableContextVariableTypeConverter extends
        ContextVariableTypeConverter<String> {

    /**
     * Creates a new instance of the {@link StringVariableContextVariableTypeConverter} class.
     */
    public StringVariableContextVariableTypeConverter() {
        super(
            String.class,
            StringVariableContextVariableTypeConverter::convertToString,
            ContextVariableTypeConverter::escapeXmlString,
            s -> s);
    }

    @Nullable
    public static String convertToString(@Nullable Object s) {
        String converted = convert(s, String.class);
        if (converted != null) {
            return converted;
        }

        if (s instanceof ContextVariable) {
            s = ((ContextVariable<?>) s).getValue();
        }

        if (s != null) {
            String str = s.toString();
            // ignore if this looks like an object reference
            if (!str.matches(".*@[a-f0-9]+$")) {
                return str;
            }
        }
        return null;
    }
}
