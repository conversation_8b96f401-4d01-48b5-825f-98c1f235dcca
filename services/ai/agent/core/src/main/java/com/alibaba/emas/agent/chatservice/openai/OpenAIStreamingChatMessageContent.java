// Copyright (c) Microsoft. All rights reserved.
package com.alibaba.emas.agent.chatservice.openai;

import com.alibaba.emas.agent.base.orchestration.FunctionResultMetadata;
import com.alibaba.emas.agent.base.services.chatcompletion.AuthorRole;
import com.alibaba.emas.agent.base.services.chatcompletion.OpenAIChatMessageContent;
import com.alibaba.emas.agent.base.services.chatcompletion.StreamingChatContent;
import com.alibaba.emas.agent.chatservice.OpenAIFunctionToolCall;
import com.alibaba.emas.mtl4.services.ai.api.model.ChatResponseToolCall;

import javax.annotation.Nullable;
import java.nio.charset.Charset;
import java.util.List;

public class OpenAIStreamingChatMessageContent<T> extends OpenAIChatMessageContent<T> implements
        StreamingChatContent<T> {

    private final String id;

    public OpenAIStreamingChatMessageContent(
        String id,
        AuthorRole authorRole,
        String content,
        @Nullable String modelId,
        @Nullable T innerContent,
        @Nullable Charset encoding,
        @Nullable FunctionResultMetadata metadata,
        @Nullable List<ChatResponseToolCall> toolCall) {
        super(
            authorRole,
            content,
            modelId,
            innerContent,
            encoding,
            metadata,
            toolCall);

        this.id = id;
    }

    @Override
    public String getId() {
        return id;
    }
}
