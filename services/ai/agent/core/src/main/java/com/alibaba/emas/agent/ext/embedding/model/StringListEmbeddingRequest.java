package com.alibaba.emas.agent.ext.embedding.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * string array embedding request.
 *
 * <AUTHOR>
 * @date 2024/8/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StringListEmbeddingRequest extends EmbeddingBaseRequest {

    /**
     * input.
     */
    @JsonProperty("input")
    @JSONField(name = "input")
    private List<String> input;

}
