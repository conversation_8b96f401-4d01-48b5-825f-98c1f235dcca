package com.alibaba.emas.agent.base.hooks;

import com.alibaba.emas.agent.base.hooks.KernelHook.*;

import javax.annotation.Nullable;
import java.util.*;
import java.util.function.Function;

/**
 * Represents a collection of hooks that can be used to intercept and modify events in the kernel.
 */
public class KernelHooks {

    private final Map<String, KernelHook<?>> hooks;

    /**
     * Creates a new instance of the {@link KernelHooks} class.
     */
    public KernelHooks() {
        this.hooks = new HashMap<>();
    }

    /**
     * Creates a copy of the {@link KernelHooks}.
     *
     * @param kernelHooks the hooks to copy
     */
    public KernelHooks(@Nullable KernelHooks kernelHooks) {
        this();
        if (kernelHooks != null) {
            this.hooks.putAll(kernelHooks.getHooks());
        }
    }

    /**
     * Creates a new instance of the {@link KernelHooks} class from the given hooks.
     *
     * @param hooks the hooks to add
     */
    public KernelHooks(Map<String, KernelHook<?>> hooks) {
        this();
        this.hooks.putAll(hooks);
    }

    /**
     * Creates an unmodifiable copy of this {@link KernelHooks}.
     *
     * @return an unmodifiable copy of this {@link KernelHooks}
     */
    public UnmodifiableKernelHooks unmodifiableClone() {
        return new UnmodifiableKernelHooks(this);
    }

    /**
     * Gets the hooks in this collection.
     *
     * @return an unmodifiable map of the hooks
     */
    protected Map<String, KernelHook<?>> getHooks() {
        return Collections.unmodifiableMap(hooks);
    }

    /**
     * Add a {@link FunctionInvokingHook} to the collection of hooks.
     *
     * @param function the function to add
     * @return the key of the hook in the collection
     */
    public String addFunctionInvokingHook(
            Function<FunctionInvokingEvent<?>, FunctionInvokingEvent<?>> function) {
        return addHook((FunctionInvokingHook) function::apply);
    }

    /**
     * Add a {@link FunctionInvokedHook} to the collection of hooks.
     *
     * @param function the function to add
     * @return the key of the hook in the collection
     */
    public String addFunctionInvokedHook(
            Function<FunctionInvokedEvent<?>, FunctionInvokedEvent<?>> function) {
        return addHook((FunctionInvokedHook) function::apply);
    }

    /**
     * Add a {@link PreChatCompletionHook} to the collection of hooks.
     *
     * @param function the function to add
     * @return the key of the hook in the collection
     */
    public String addPreChatCompletionHook(
            Function<PreChatCompletionEvent, PreChatCompletionEvent> function) {
        return addHook((PreChatCompletionHook) function::apply);
    }

    /**
     * Add a {@link PreToolCallHook} to the collection of hooks.
     *
     * @param function the function to add
     * @return the key of the hook in the collection
     */
    public String addPreToolCallHook(
            Function<PreToolCallEvent, PreToolCallEvent> function) {
        return addHook((PreToolCallHook) function::apply);
    }

    /**
     * Add a {@link PostChatCompletionEvent} to the collection of hooks.
     *
     * @param function the function to add
     * @return the key of the hook in the collection
     */
    public String addPostChatCompletionHook(
            Function<PostChatCompletionEvent, PostChatCompletionEvent> function) {
        return addHook((PostChatCompletionHook) function::apply);
    }

    /**
     * Add a {@link PromptRenderedHook} to the collection of hooks.
     *
     * @param function the function to add
     * @return the key of the hook in the collection
     */
    public String addPromptRenderedHook(
            Function<PromptRenderedEvent, PromptRenderedEvent> function) {
        return addHook((PromptRenderedHook) function::apply);
    }

    /**
     * Add a {@link PromptRenderingHook} to the collection of hooks.
     *
     * @param function the function to add
     * @return the key of the hook in the collection
     */
    public String addPromptRenderingHook(
            Function<PromptRenderingEvent, PromptRenderingEvent> function) {
        return addHook((PromptRenderingHook) function::apply);
    }

    /**
     * Executes the hooks in this collection that accept the event.
     *
     * @param event the event to execute the hooks on
     * @param <T>   the type of the event
     * @return the event after the hooks have been executed
     */
    @SuppressWarnings("unchecked")
    public <T extends KernelHookEvent> T executeHooks(T event) {
        return this.hooks.values()
                .stream()
                .filter(hook -> hook.test(event))
                .sorted(Comparator.comparingInt(KernelHook::getPriority))
                .reduce(event, (event2, hook) -> {
                    // unchecked cast
                    return ((KernelHook<T>) hook).apply(event2);
                }, (a, b) -> {
                    throw new UnsupportedOperationException("No merging for hooks");
                });
    }

    /**
     * Add a {@link KernelHook} to the collection of hooks.
     *
     * @param hook the hook to add
     * @return the key of the hook in the collection
     */
    public String addHook(KernelHook<?> hook) {
        return addHook(UUID.randomUUID().toString(), hook);
    }

    /**
     * Add a {@link KernelHook} to the collection of hooks.
     *
     * @param hookName the key of the hook in the collection
     * @param hook     the hook to add
     * @return the key of the hook in the collection
     */
    public String addHook(String hookName, KernelHook<?> hook) {
        hooks.put(hookName, hook);
        return hookName;
    }

    /**
     * Remove a hook from the collection of hooks.
     *
     * @param hookName the key of the hook in the collection
     * @return the removed hook, or {@code null} if the hook was not found
     */
    public KernelHook<?> removeHook(String hookName) {
        return hooks.remove(hookName);
    }

    /**
     * Appends the given hooks to this collection.
     *
     * @param kernelHooks the hooks to append
     * @return this instance of the {@link KernelHooks} class
     */
    public KernelHooks addHooks(@Nullable KernelHooks kernelHooks) {
        if (kernelHooks == null) {
            return this;
        }
        hooks.putAll(kernelHooks.getHooks());

        return this;
    }

    /**
     * Determines if this collection of hooks is empty.
     *
     * @return {@code true} if the collection is empty, otherwise {@code false}
     */
    public boolean isEmpty() {
        return hooks.isEmpty();
    }

    /**
     * Builds the list of hooks to be invoked for the given context, by merging the hooks in this
     * collection with the hooks in the context. Duplicate hooks in b will override hooks in a.
     *
     * @param a hooks to merge
     * @param b hooks to merge
     * @return the list of hooks to be invoked
     */
    public static KernelHooks merge(@Nullable KernelHooks a, @Nullable KernelHooks b) {
        KernelHooks hooks = a;
        if (hooks == null) {
            hooks = new KernelHooks();
        }

        if (b == null) {
            return hooks;
        } else if (hooks.isEmpty()) {
            return b;
        } else {
            HashMap<String, KernelHook<?>> merged = new HashMap<>(hooks.getHooks());
            merged.putAll(b.getHooks());
            return new KernelHooks(merged);
        }
    }

    /**
     * A wrapper for KernelHooks that disables mutating methods.
     */
    public static class UnmodifiableKernelHooks extends KernelHooks {

        private UnmodifiableKernelHooks(KernelHooks kernelHooks) {
            super(kernelHooks);
        }

        @Override
        public String addFunctionInvokingHook(
                Function<FunctionInvokingEvent<?>, FunctionInvokingEvent<?>> function) {
            throw new UnsupportedOperationException("unmodifiable instance of KernelHooks");
        }

        @Override
        public String addFunctionInvokedHook(
                Function<FunctionInvokedEvent<?>, FunctionInvokedEvent<?>> function) {
            throw new UnsupportedOperationException("unmodifiable instance of KernelHooks");
        }

        @Override
        public String addPreChatCompletionHook(
                Function<PreChatCompletionEvent, PreChatCompletionEvent> function) {
            throw new UnsupportedOperationException("unmodifiable instance of KernelHooks");
        }

        @Override
        public String addPostChatCompletionHook(
                Function<PostChatCompletionEvent, PostChatCompletionEvent> function) {
            throw new UnsupportedOperationException("unmodifiable instance of KernelHooks");
        }

        @Override
        public String addPromptRenderedHook(
                Function<PromptRenderedEvent, PromptRenderedEvent> function) {
            throw new UnsupportedOperationException("unmodifiable instance of KernelHooks");
        }

        @Override
        public String addPromptRenderingHook(
                Function<PromptRenderingEvent, PromptRenderingEvent> function) {
            throw new UnsupportedOperationException("unmodifiable instance of KernelHooks");
        }

        @Override
        public String addHook(KernelHook<?> hook) {
            throw new UnsupportedOperationException("unmodifiable instance of KernelHooks");
        }

        @Override
        public String addHook(String hookName, KernelHook<?> hook) {
            throw new UnsupportedOperationException("unmodifiable instance of KernelHooks");
        }

        @Override
        public KernelHooks addHooks(@Nullable KernelHooks kernelHooks) {
            throw new UnsupportedOperationException("unmodifiable instance of KernelHooks");
        }

        @Override
        public KernelHook<?> removeHook(String hookName) {
            throw new UnsupportedOperationException("unmodifiable instance of KernelHooks");
        }
    }

}