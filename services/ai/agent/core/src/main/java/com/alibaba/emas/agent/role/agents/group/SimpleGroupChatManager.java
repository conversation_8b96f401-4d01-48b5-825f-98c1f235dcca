package com.alibaba.emas.agent.role.agents.group;

import com.alibaba.emas.agent.base.services.chatcompletion.AuthorRole;
import com.alibaba.emas.agent.base.services.chatcompletion.ChatMessageContent;
import com.alibaba.emas.agent.role.agents.Agent;
import com.alibaba.emas.agent.role.agents.ConversableAgent;
import com.alibaba.emas.agent.role.exceptions.AgentException;
import com.alibaba.emas.agent.role.model.HumanInputMode;
import com.alibaba.emas.agent.role.model.ReplyResult;
import com.alibaba.emas.agent.role.utils.Constants;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * group chat manager.
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
@Slf4j
@Getter
public class SimpleGroupChatManager extends GroupChatManager {

    /**
     * build
     *
     * @param builder builder
     */
    public SimpleGroupChatManager(Builder builder) {
        super(builder);
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder extends GroupChatManager.Builder<SimpleGroupChatManager.Builder> {

        protected Builder() {
            super();
            this.name = "chat_manager";
            this.maxConsecutiveAutoReply = Constants.DEFAULT_GROUP_CHAT_MAX_REPLY_TIMES;
            this.systemMessage = "Group chat manager.";
            this.humanInputMode = HumanInputMode.NEVER;
        }

        @Override
        public SimpleGroupChatManager build() {
            return new SimpleGroupChatManager(this);
        }
    }

}
