package com.alibaba.emas.agent.base.semanticfunctions;

import com.alibaba.emas.agent.base.exceptions.SKException;
import com.alibaba.emas.agent.base.implementation.templateengine.tokenizer.DefaultPromptTemplate;
import com.alibaba.emas.agent.base.templateengine.handlebars.HandlebarsPromptTemplate;
import com.alibaba.emas.agent.ext.templateengine.text.TextPromptTemplate;

import javax.annotation.Nonnull;
import java.util.Locale;

import static com.alibaba.emas.agent.base.semanticfunctions.HandlebarsPromptTemplateFactory.HANDLEBARS_TEMPLATE_FORMAT;
import static com.alibaba.emas.agent.base.semanticfunctions.PromptTemplateConfig.SEMANTIC_KERNEL_TEMPLATE_FORMAT;
import static com.alibaba.emas.agent.base.semanticfunctions.PromptTemplateConfig.TEXT_TEMPLATE_FORMAT;

/**
 * Factory for creating prompt templates. This factory creates the appropriate prompt template based
 * on the template format.
 */
public class KernelPromptTemplateFactory implements PromptTemplateFactory {

    @Override
    public PromptTemplate tryCreate(@Nonnull PromptTemplateConfig templateConfig) {
        if (templateConfig.getTemplate() == null) {
            throw new SKException(
                String.format("No prompt template was provided for the prompt %s.",
                    templateConfig.getName()));
        }

        switch (templateConfig.getTemplateFormat().toLowerCase(Locale.ROOT)) {
            case SEMANTIC_KERNEL_TEMPLATE_FORMAT:
                return DefaultPromptTemplate.build(templateConfig);
            case HANDLEBARS_TEMPLATE_FORMAT:
                return new HandlebarsPromptTemplate(templateConfig);
            case TEXT_TEMPLATE_FORMAT:
                return new TextPromptTemplate(templateConfig);
            default:
                throw new UnknownTemplateFormatException(templateConfig.getTemplateFormat());
        }
    }
}
