package com.alibaba.emas.agent.base.implementation.templateengine.tokenizer.blocks;

import com.alibaba.emas.agent.base.contextvariables.ContextVariableTypes;
import com.alibaba.emas.agent.base.semanticfunctions.KernelFunctionArguments;

import javax.annotation.Nullable;

/**
 * Interface of static blocks that don't need async IO to be rendered.
 */
public interface TextRendering {

    /**
     * Render the block using only the given variables.
     *
     * @param variables Optional variables used to render the block
     * @return Rendered content
     */
    @Nullable
    String render(ContextVariableTypes types, @Nullable KernelFunctionArguments variables);
}
