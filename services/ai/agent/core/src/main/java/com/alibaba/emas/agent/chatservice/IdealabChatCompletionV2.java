package com.alibaba.emas.agent.chatservice;

import com.alibaba.emas.agent.base.Kernel;
import com.alibaba.emas.agent.base.hooks.PostChatCompletionEvent;
import com.alibaba.emas.agent.base.orchestration.InvocationContext;
import com.alibaba.emas.agent.base.services.chatcompletion.*;
import com.alibaba.emas.agent.chatservice.exceptions.FunctionInvocationError;
import com.alibaba.emas.agent.chatservice.openai.OpenAIStreamingChatMessageContent;
import com.alibaba.emas.agent.chatservice.utils.ChatUtils;
import com.alibaba.emas.agent.role.utils.DateUtils;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.services.ai.api.model.ChatResponseToolCall;
import com.alibaba.emas.mtl4.services.ai.model.AzureChatRequest;
import com.alibaba.emas.mtl4.services.ai.model.message.ChatRequestAssistantMessage;
import com.alibaba.emas.mtl4.services.ai.model.message.ChatRequestMessage;
import com.alibaba.emas.mtl4.services.ai.model.message.ChatRequestSystemMessage;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.idealab.client.api.IdealabApi;
import com.alibaba.idealab.client.api.model.chat.IdealabChatRequest;
import com.alibaba.idealab.client.api.model.chat.IdealabChatResponse;
import com.alibaba.idealab.client.api.model.chat.ToolDefinition;
import com.azure.ai.openai.models.*;
import com.azure.core.util.BinaryData;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.jetbrains.annotations.Nullable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.emas.agent.role.utils.JsonUtils.prettyFormat;

/**
 * idealab chat completion v2.
 *
 * <AUTHOR>
 * @date 2024/10/17
 */
@Slf4j
public class IdealabChatCompletionV2 implements ChatCompletionService, ChatStreamService {

    protected static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static int REQUEST_RATE_LIMIT_SLEEP_MILLS = 500;

    protected static final int MAX_RETRY_COUNT = 10;

    IdealabApi idealabApi = new IdealabApi("8e25c3cf1b8f06af9f33bcb8eb4c06c2", "https://idealab.alibaba-inc.com");

    private String modelId;

    public IdealabChatCompletionV2(String modelId) {
        this.modelId = modelId;
    }

    public IdealabChatCompletionV2(String modelId, String serviceId) {
        this.modelId = modelId;
    }

    static {
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SimpleModule module = new SimpleModule();
        module.addDeserializer(ToolDefinition.class, new ToolDefinitionDeserializer());
        OBJECT_MAPPER.registerModule(module);
    }

    @Override
    public Mono<List<ChatMessageContent<?>>> getChatMessageContentsAsync(
            ChatHistory chatHistory,
            @Nullable Kernel kernel,
            @Nullable InvocationContext invocationContext) {
        List<ChatRequestMessage> chatMessages = ChatUtils.getChatMessages(chatHistory);
        List<OpenAIFunction> functions = ChatUtils.getFunctions(kernel);
        int autoInvokeAttempts = ChatUtils.getAutoInvokeAttempts(MAXIMUM_INFLIGHT_AUTO_INVOKES, invocationContext);
        return internalAiStudioChatMessageContentsAsync(chatMessages, kernel, functions, invocationContext,
                autoInvokeAttempts);
    }

    @Override
    public Mono<List<ChatMessageContent<?>>> getChatMessageContentsAsync(
            String prompt,
            @Nullable Kernel kernel,
            @Nullable InvocationContext invocationContext) {
        ParsedAiStudioPrompt parsedAiStudioPrompt = XMLPromptAiStudioParser.parse(prompt);
        List<ChatRequestMessage> chatRequestMessages = parsedAiStudioPrompt.getChatRequestMessages();
        List<OpenAIFunction> functions = ChatUtils.getFunctions(kernel);
        int autoInvokeAttempts = ChatUtils.getAutoInvokeAttempts(MAXIMUM_INFLIGHT_AUTO_INVOKES, invocationContext);
        return internalAiStudioChatMessageContentsAsync(chatRequestMessages, kernel, functions, invocationContext,
                autoInvokeAttempts);
    }

    @Override
    public Flux<StreamingChatContent<?>> getStreamingChatMessageContentsAsync(
            ChatHistory chatHistory,
            @Nullable Kernel kernel,
            @Nullable InvocationContext invocationContext) {
        List<ChatRequestMessage> chatRequestMessages = ChatUtils.getChatRequestMessages(chatHistory);
        IdealabChatRequest chatRequest = getChatRequest(chatRequestMessages, kernel, invocationContext);
        return getStreamingChatContentFlux(chatRequest);
    }

    @Override
    public Flux<StreamingChatContent<?>> getStreamingChatMessageContentsAsync(
            String prompt,
            @Nullable Kernel kernel,
            @Nullable InvocationContext invocationContext) {
        ParsedAiStudioPrompt parsedAiStudioPrompt = XMLPromptAiStudioParser.parse(prompt);
        List<ChatRequestMessage> chatRequestMessages = parsedAiStudioPrompt.getChatRequestMessages();
        IdealabChatRequest chatRequest = getChatRequest(chatRequestMessages, kernel, invocationContext);
        return getStreamingChatContentFlux(chatRequest);
    }

    @Override
    public Flux<IdealabChatResponse> streamChat(String prompt, Kernel kernel, InvocationContext invocationContext) {
        ParsedAiStudioPrompt parsedAiStudioPrompt = XMLPromptAiStudioParser.parse(prompt);
        List<ChatRequestMessage> chatRequestMessages = parsedAiStudioPrompt.getChatRequestMessages();
        // 处理消息长度
        chatRequestMessages = ChatUtils.getPromptChatRequestMessages(chatRequestMessages, invocationContext, this.modelId);
        IdealabChatRequest chatRequest = getChatRequest(chatRequestMessages, kernel, invocationContext);
        return idealabApi.chatStream(chatRequest);
    }

    @Override
    public Flux<IdealabChatResponse> streamChat(ChatHistory chatHistory, Kernel kernel, InvocationContext invocationContext) {
        List<ChatRequestMessage> chatRequestMessages = ChatUtils.getChatRequestMessages(chatHistory);
        IdealabChatRequest chatRequest = getChatRequest(chatRequestMessages, kernel, invocationContext);
        return idealabApi.chatStream(chatRequest);
    }

    @Nullable
    @Override
    public String getModelId() {
        return modelId;
    }

    @Nullable
    @Override
    public String getServiceId() {
        return modelId;
    }


    protected Mono<List<ChatMessageContent<?>>> internalAiStudioChatMessageContentsAsync(
            List<ChatRequestMessage> chatRequestMessages,
            @Nullable Kernel kernel,
            List<OpenAIFunction> functions,
            @Nullable InvocationContext context,
            int autoInvokeAttempts) {
        IdealabChatRequest chatRequest = getChatRequest(chatRequestMessages, kernel, functions, context,
                autoInvokeAttempts);
        ChatCompletions chatCompletions = null;

        int i = 0;
        while (i++ < MAX_RETRY_COUNT) {
            try {
                IdealabChatResponse chatResponse = idealabApi.chat(chatRequest);
                chatCompletions = BinaryData.fromObject(chatResponse).toObject(ChatCompletions.class);
                break;
            } catch (Exception e) {
                log.error("大模型请求失败， error: ", e);
                try {
                    Thread.sleep((long) (i % 5) * REQUEST_RATE_LIMIT_SLEEP_MILLS);
                } catch (InterruptedException ex) {
                    log.error("Thread sleep interrupted: ", ex);
                }
            }
        }
        Assert.notNull(chatCompletions);

        prettyPrint(chatCompletions);

        InvocationContext invocationContext = Optional.ofNullable(context)
                .orElse(new InvocationContext());
        Mono<List<? extends ChatMessageContent>> result = Mono.just(chatCompletions)
                .flatMap(completions -> {
                    List<ChatResponseMessage> responseMessages = completions
                            .getChoices()
                            .stream()
                            .map(ChatChoice::getMessage)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    // execute post chat completion hook
                    ChatUtils.executeHook(invocationContext, new PostChatCompletionEvent(completions));

                    if (autoInvokeAttempts == 0 || responseMessages.size() != 1) {
                        return ChatUtils.getChatMessageContentsAsync(completions, this.modelId);
                    }
                    // Or if there are no tool calls to be done
                    ChatResponseMessage response = responseMessages.get(0);
                    List<ChatCompletionsToolCall> toolCalls = response.getToolCalls();
                    if (toolCalls == null || toolCalls.isEmpty()) {
                        return ChatUtils.getChatMessageContentsAsync(completions, this.modelId);
                    }

                    ChatRequestAssistantMessage requestMessage = new ChatRequestAssistantMessage(
                            response.getContent());
                    List<ChatResponseToolCall> toolCallList = ChatUtils.getChatResponseToolCalls(toolCalls);
                    requestMessage.setToolCalls(toolCallList);

                    chatRequestMessages.add(requestMessage);
                    return Flux
                            .fromIterable(toolCalls)
                            .reduce(
                                    Mono.just(chatRequestMessages),
                                    (requestMessages, toolCall) -> {
                                        if (toolCall instanceof ChatCompletionsFunctionToolCall) {
                                            return ChatUtils.performToolCall(kernel, invocationContext, requestMessages,
                                                    toolCall);
                                        }

                                        return requestMessages;
                                    })
                            .flatMap(it -> it)
                            .flatMap(msgs -> internalAiStudioChatMessageContentsAsync(msgs, kernel, functions,
                                    invocationContext, autoInvokeAttempts - 1))
                            .onErrorResume(e -> {
                                log.warn("Tool invocation attempt failed: ", e);

                                // If FunctionInvocationError occurred and there are still attempts left, retry, else exit
                                if (autoInvokeAttempts > 0) {
                                    List<ChatRequestMessage> currentMessages = chatRequestMessages;
                                    if (e instanceof FunctionInvocationError) {
                                        currentMessages = ((FunctionInvocationError) e).getMessages();
                                    }
                                    return internalAiStudioChatMessageContentsAsync(currentMessages, kernel,
                                            functions,
                                            invocationContext, autoInvokeAttempts - 1);
                                } else {
                                    return Mono.error(e);
                                }
                            })
                            ;
                });

        return result.map(op -> (List<ChatMessageContent<?>>) op);
    }

    private Flux<StreamingChatContent<?>> getStreamingChatContentFlux(IdealabChatRequest chatRequest) {
        Flux<IdealabChatResponse> responses = idealabApi.chatStream(chatRequest);
        return responses.flatMap(idealabChatResponse -> Flux.fromIterable(idealabChatResponse.getChoices())
                .map(message -> {
                    AuthorRole role = message.getDelta().getRole() == null
                            ? AuthorRole.ASSISTANT
                            : AuthorRole.valueOf(message.getDelta().getRole().toString()
                            .toUpperCase(Locale.ROOT));

                    return new OpenAIStreamingChatMessageContent<>(
                            idealabChatResponse.getId(),
                            role,
                            message.getDelta().getContent(),
                            getModelId(),
                            null,
                            null,
                            null,
                            Arrays.asList());
                }));
    }

    private IdealabChatRequest getChatRequest(List<ChatRequestMessage> chatRequestMessages,
                                              @Nullable Kernel kernel,
                                              @Nullable InvocationContext context) {
        List<OpenAIFunction> functions = ChatUtils.getFunctions(kernel);
        int autoInvokeAttempts = ChatUtils.getAutoInvokeAttempts(MAXIMUM_INFLIGHT_AUTO_INVOKES, context);
        return getChatRequest(chatRequestMessages, kernel, functions, context, autoInvokeAttempts);
    }

    private IdealabChatRequest getChatRequest(
            List<ChatRequestMessage> chatRequestMessages,
            @Nullable Kernel kernel,
            List<OpenAIFunction> functions,
            @Nullable InvocationContext context,
            int autoInvokeAttempts) {
        AzureChatRequest azureChatRequest = ChatUtils.getAzureChatRequest(chatRequestMessages, kernel, functions,
                context, autoInvokeAttempts, modelId);

        // 缓存工具定义，claude功能
        if (null != context
            && null != context.getPromptExecutionSettings()
            && context.getPromptExecutionSettings().isCacheTools()
            && CollectionUtils.isNotEmpty(azureChatRequest.getTools())
        ) {
            int toolSize = azureChatRequest.getTools().size();
            JSONObject cacheControl = new JSONObject();
            cacheControl.put("type", "ephemeral");
            azureChatRequest.getTools().get(toolSize - 1).getFunction().setCache_control(cacheControl);
        }

        // 缓存系统提示信息，claude功能
        if (null != context
            && null != context.getPromptExecutionSettings()
            && context.getPromptExecutionSettings().isCacheSystemPrompt()
            && CollectionUtils.isNotEmpty(azureChatRequest.getMessages())
        ) {
            List<JSONObject> systemMessages = azureChatRequest.getMessages()
                .stream()
                .filter(chatRequestMessage -> chatRequestMessage instanceof ChatRequestSystemMessage)
                .map(chatRequestMessage -> (ChatRequestSystemMessage) chatRequestMessage)
                .map(chatRequestSystemMessage -> {
                    JSONObject systemMessageJO = new JSONObject();
                    systemMessageJO.put("type", "text");
                    systemMessageJO.put("text", chatRequestSystemMessage.getContent());
                    JSONObject cacheControl = new JSONObject();
                    cacheControl.put("type", "ephemeral");
                    systemMessageJO.put("cache_control", cacheControl);
                    return systemMessageJO;
                }).toList();

            azureChatRequest.setExtendParams(ImmutableMap.of("system", systemMessages));

            azureChatRequest.setMessages(azureChatRequest.getMessages()
                .stream()
                .filter(chatRequestMessage -> !(chatRequestMessage instanceof ChatRequestSystemMessage))
                .collect(Collectors.toList()));
        }

        try {
            IdealabChatRequest request = OBJECT_MAPPER.readValue(OBJECT_MAPPER.writeValueAsString(azureChatRequest), IdealabChatRequest.class);
            //if (null != context
            //    && null != context.getPromptExecutionSettings()
            //    && context.getPromptExecutionSettings().isCodeExecutionTool()) {
            //    request.setHeaders(ImmutableMap.of("anthropic-beta", "code-execution-2025-05-22"));
            //    //CustomToolDefinition codeExecutionToolDefinition = new CustomToolDefinition();
            //    //codeExecutionToolDefinition.setType("code_execution_20250522");
            //    //codeExecutionToolDefinition.setName("code_execution");
            //    //List<ToolDefinition> toolDefinitions = request.getTools();
            //    //if (CollectionUtils.isEmpty(toolDefinitions)) {
            //    //    toolDefinitions = new ArrayList<>();
            //    //}
            //    //toolDefinitions.add(codeExecutionToolDefinition);
            //    //request.setTools(toolDefinitions);
            //}

            if (null != context
                && null != context.getPromptExecutionSettings()
                && context.getPromptExecutionSettings().isEnableThinking()) {
                request.setExtendParams(ImmutableMap.of("thinking", ImmutableMap.of("type", "enabled",
                    "budget_tokens", context.getPromptExecutionSettings().getThinkingBudget())));
            }
            return request;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * ToolDefinition 序列化
     */
    public static class ToolDefinitionDeserializer extends JsonDeserializer<ToolDefinition> {
        @Override
        public ToolDefinition deserialize(JsonParser p, DeserializationContext ctx) throws IOException {
            ObjectMapper mapper = (ObjectMapper) p.getCodec();
            JsonNode node = mapper.readTree(p);

            // 根据 type 进行反序列化
            String type = node.get("type").asText();
            if ("function".equals(type)) {
                return mapper.treeToValue(node, ToolDefinition.FunctionToolDefinition.class);
            }
            throw new IllegalArgumentException("Unknown type: " + type);
        }
    }

    public void prettyPrint(ChatCompletions chatCompletions) {
        try {
            chatCompletions.getChoices().forEach(choice -> {
                ChatResponseMessage message = choice.getMessage();
                if (StringUtils.isNotBlank(message.getContent())) {
                    log.info(">>>content:\n{}", markdownToHtml(message.getContent()));
                }
                if (CollectionUtils.isNotEmpty(message.getToolCalls())) {
                    message.getToolCalls().forEach(toolCall -> {
                        if (toolCall instanceof ChatCompletionsFunctionToolCall functionToolCall) {
                            log.info(">>>toolCall\nname: {}\n{}", functionToolCall.getFunction().getName(),
                                prettyFormat(functionToolCall.getFunction().getArguments()));
                        }
                    });
                }
            });
        } catch (Exception e) {
            log.error("prettyPrint error", e);
        }
    }

    public String markdownToHtml(String markdown) {
        Parser parser = Parser.builder().build();
        HtmlRenderer renderer = HtmlRenderer.builder().build();
        var document = parser.parse(markdown);
        return renderer.render(document);
    }

}
