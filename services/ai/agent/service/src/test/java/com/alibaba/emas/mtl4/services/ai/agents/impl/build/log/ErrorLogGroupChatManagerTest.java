package com.alibaba.emas.mtl4.services.ai.agents.impl.build.log;


import com.alibaba.emas.agent.base.Kernel;
import com.alibaba.emas.agent.base.orchestration.PromptExecutionSettings;
import com.alibaba.emas.agent.base.semanticfunctions.HandlebarsPromptTemplateFactory;
import com.alibaba.emas.agent.base.semanticfunctions.KernelFunction;
import com.alibaba.emas.agent.base.semanticfunctions.KernelFunctionYaml;
import com.alibaba.emas.agent.base.services.chatcompletion.AuthorRole;
import com.alibaba.emas.agent.base.services.chatcompletion.ChatCompletionService;
import com.alibaba.emas.agent.base.services.chatcompletion.ChatMessageContent;
import com.alibaba.emas.agent.base.services.chatcompletion.ObjectChatMessageContent;
import com.alibaba.emas.agent.chatservice.IdealabChatCompletion;
import com.alibaba.emas.agent.ext.kernel.model.ModelEnum;
import com.alibaba.emas.agent.ext.ouputparser.YamlOutputParser;
import com.alibaba.emas.agent.role.agents.ConversableAgent;
import com.alibaba.emas.agent.role.agents.build.log.ErrorLogDetectionAgent;
import com.alibaba.emas.agent.role.agents.build.log.ErrorLogGroupChat;
import com.alibaba.emas.agent.role.agents.build.log.ErrorLogGroupChatManager;
import com.alibaba.emas.agent.role.agents.build.log.ErrorLogRankAgent;
import com.alibaba.emas.agent.role.agents.build.log.model.*;
import com.alibaba.emas.agent.role.agents.contrib.KeyMessageEndAgent;
import com.alibaba.emas.agent.role.utils.PromptUtils;
import com.alibaba.emas.mtl4.commons.utils.StringKit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Pattern;

import static com.alibaba.emas.agent.role.agents.build.log.ErrorLogGroupChatManager.mergeConsecutiveLines;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class ErrorLogGroupChatManagerTest {

    ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testRunChat() {
        try {
            ErrorLogGroupChatManager errorLogGroupChatManager = getManager();

            ConversableAgent userProxyAgent = getUserAgent();
            ErrorLogAnalyzeRequest request = getIOSErrorLogAnalyzeRequest();
//            ErrorLogAnalyzeRequest request = getAndroidErrorLogAnalyzeRequest();
            ChatMessageContent<?> messageContent = new ObjectChatMessageContent<>(AuthorRole.ASSISTANT, request);

            List<ChatMessageContent<?>> replies = errorLogGroupChatManager.generateReply(userProxyAgent, Lists.newArrayList(messageContent));

            log.info("replies:{}", replies);

        } catch (IOException e) {
            log.error("error log group chat manager init failed", e);
        }
    }

    @Test
    public void testAnalyzeAndroidLog() {
        try {
            ErrorLogGroupChatManager errorLogGroupChatManager = getManager();

            ConversableAgent userProxyAgent = getUserAgent();
//            ErrorLogAnalyzeRequest request = getIOSErrorLogAnalyzeRequest();
            long startTime = System.currentTimeMillis();
            ErrorLogAnalyzeRequest request = getAndroidErrorLogAnalyzeRequest();
            ChatMessageContent<?> messageContent = new ObjectChatMessageContent<>(AuthorRole.ASSISTANT, request);

            List<ChatMessageContent<?>> replies = errorLogGroupChatManager.generateReply(userProxyAgent, Lists.newArrayList(messageContent));
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            log.info("executionTime:{}ms", executionTime);

//            log.info("replies:{}", replies);

        } catch (IOException e) {
            log.error("error log group chat manager init failed", e);
        }
    }


    private ErrorLogGroupChatManager getManager() throws IOException {
        String rankPrompt = getRankPrompt();
        log.info("rankPrompt:{}", objectMapper.writeValueAsString(rankPrompt));
        ErrorLogGroupChat groupChat = ErrorLogGroupChat.builder()
                .extractionAgentList(getErrorLogDetectionAgents(3))
                .rankAgent(getErrorLogRankAgent("error-log-detection-agent-1",
                        ModelEnum.QWEN2_5_72B_INSTRUCT, rankPrompt))
                .build();

        ChatCompletionService chatCompletionService = new IdealabChatCompletion(ModelEnum.QWEN2_5_72B_INSTRUCT.getModelId(), ModelEnum.QWEN2_5_72B_INSTRUCT.getModelId());

        Kernel kernel = Kernel.builder()
                .withAIService(ChatCompletionService.class, chatCompletionService)
                .build();

        KernelFunction<?> chatFunction = KernelFunctionYaml.fromYaml(Paths.get("agent/default.prompt.yml"));

        return ErrorLogGroupChatManager.builder()
                .groupChat(groupChat)
                .name("error-log-group-chat")
                .chatFunction(chatFunction)
                .identifier(StringKit.format("error-log-group-chat-{}", StringKit.generateUuid()))
                .kernel(kernel)
                .build();
    }

    private ConversableAgent getUserAgent() {
        return KeyMessageEndAgent.builder()
                .name("user-agent")
                .identifier(StringKit.format("user-proxy-{}", StringKit.generateUuid()))
                .isTerminationMsg(chatMessageContent -> true)
                .build();
    }

    private ErrorLogAnalyzeRequest getIOSErrorLogAnalyzeRequest() {
        String requestJson = """
                {
                      "maxChunkLength": 35000,
                      "maxLineLength": 1000,
                      "maxRawLogReturnNum": 3,
                      "isFilteringLongLine": true,
                      "errorLogDownloadUrl": "https://mtl4.alibaba-inc.com/scheduler/jobs/8286668/tasks/f9b1f5afd38647ad815c67c5bcdb0f35/logDownload",
                      "patterns": [
                        "^\\\\s{4}(cd|setenv|(?:[\\\\w/:\\\\\\\\\\\\s\\\\-.]+?/)?[\\\\w\\\\-]+)\\\\s(.*)$",
                        "^Compile[\\\\w]+\\\\s.+?\\\\s((?:\\\\\\\\.|[^ ])+/((?:\\\\\\\\.|[^ ])+\\\\.(?:m|mm|c|cc|cpp|cxx|swift)))\\\\s.*",
                        "^CompileXIB\\\\s(.*/(.*\\\\.xib))",
                        "CpHeader\\\\s(.*\\\\.h)\\\\s(.*\\\\.h)",
                        "^CopyPlistFile\\\\s(.*\\\\.plist)\\\\s(.*\\\\.plist)",
                        "^CopyStringsFile.*/(.*.strings)",
                        "^CpResource\\\\s(.*)\\\\s/",
                        "^\\\\s*Executed",
                        "^Libtool.*/(.*\\\\.a)",
                        "^Ld /?.*/(.*?) (.*) (.*)$",
                        "^\\\\*\\\\*\\\\s(.*)\\\\sSUCCEEDED\\\\s\\\\*\\\\*",
                        "^PhaseScriptExecution\\\\s((\\\\\\\\\\\\ |\\\\S)*)\\\\s",
                        "^ProcessPCH\\\\s.*\\\\s(.*.pch)",
                        "^ProcessInfoPlistFile\\\\s.*\\\\.plist\\\\s(.*/+(.*\\\\.plist))",
                        "^Touch\\\\s(.*/(.+))",
                        "^(ld: )warning: (.*)",
                        "^warning:\\\\s(.*)$",
                        "^(INFO|note)(:?)\\\\s(.*)$",
                        "^\\\\s{2}A\\\\s[a-zA-Z0-9\\\\_\\\\-]+$",
                        "^(Copy|SymLink|CopyPNGFile|CpHeader)\\\\s(/[\\\\s/\\\\^]?[a-zA-Z0-9\\\\.\\\\_\\\\-\\\\s\\\\@]+)+\\\\s+(\\\\.\\\\./)*([/\\\\w\\\\.\\\\-\\\\(\\\\)\\\\s\\\\@]+)\\\\s+\\\\(in target '(.+)' from project '(.+)'\\\\)$",
                        "^(\\\\s{4})?export\\\\s([A-Z0-9]+\\\\_?)+\\\\\\\\?=(.*)$",
                        "^(WriteAuxiliaryFile|MkDir|Touch|ExtractAPI|Libtool|(SetOwnerAndGroup\\\\syuanzhan:staff)|RegisterExecutionPolicyException|ConvertSDKDBToSymbolGraph|CompileMetalFile|MetalLink|CompileAssetCatalog|SwiftCompile normal arm64).*\\\\s\\\\(in target '(.+)' from project '(.+)'\\\\)$",
                        "^CreateBuildDirectory\\\\s(/([\\\\.\\\\_]?[a-zA-Z0-9\\\\s\\\\-\\\\.\\\\_\\\\+]+))+$",
                        "^\\\\s*$",
                        "^/([\\\\s/\\\\^]?[a-zA-Z0-9\\\\.\\\\-\\\\_])+\\\\.xcodeproj: warning: The iOS(\\\\sSimulator)? deployment target 'IPHONEOS_DEPLOYMENT_TARGET'\\\\sis(.*)\\\\(in target '(.+)' from project '(.+)'\\\\)$",
                        "^(?:[\\\\w\\\\-\\\\.\\\\+]+\\\\.framework|Headers)(?:/[\\\\w\\\\-\\\\.\\\\+]+)*/?$",
                        "^(->\\\\s)?Installing\\\\s[A-Za-z0-9\\\\_\\\\-]+\\\\s\\\\(((\\\\.)?[0-9]+)+(\\\\-[A-Za-z0-9\\\\.]+)*\\\\)$",
                        "^\\\\s{2}>\\\\sCopying\\\\s[A-za-z\\\\-0-9]+\\\\sfrom\\\\s\\\\`(.*)\\\\`\\\\sto\\\\s\\\\`(.*)\\\\`$",
                        "^Integrating\\\\starget\\\\s\\\\`[A-za-z\\\\-]+\\\\`$",
                        "^\\\\s{2}-\\\\sWriting\\\\sXcode\\\\sproject\\\\sfile\\\\sto\\\\s\\\\`([/\\\\.]?[A-za-z\\\\-]+)+\\\\`$",
                        "^\\\\s{8}➜\\\\s(Explicit|Implicit) dependency on target\\\\s\\\\'[A-Za-z0-9\\\\_\\\\-\\\\.]+\\\\'\\\\sin project \\\\'[A-Za-z0-9\\\\_\\\\-]+\\\\'( via options \\\\'(.*)\\\\' in build setting \\\\'(.*)\\\\')?",
                        "^\\\\s*[\\\\^\\\\:\\\\;\\\\}\\\\]\\\\(\\\\)/\\\\<\\\\>]{0,10}$",
                        "^([\\\\s~]*)(\\\\^|void|key|(\\\\_?[a-zA-Z\\\\-\\\\>\\\\_]+))?((~|\\\\s)*)?$",
                        "^In file included from .*?:\\\\d*:$",
                        "^In file included from\\\\s(/([\\\\.\\\\_]?[a-zA-Z0-9\\\\s\\\\-\\\\.\\\\_\\\\+]+))+:[0-9]+:$",
                        "^\\\\d+\\\\s(warning(s)?( and \\\\d+ error(s)?)?|errors?) generated.$",
                        "^  - Installing files into [A-Za-z0-9\\\\_\\\\-]+ project$",
                        "^  - Creating [A-Za-z0-9\\\\_\\\\-]+ project$",
                        "^sent\\\\s[0-9,]+\\\\sbytes  received\\\\s[0-9,]+\\\\sbytes  [0-9,]+\\\\.[0-9]{2} bytes/sec$",
                        "^(specs|mirror)\\\\.internal > (Relative path( couldn't be)? downloaded:|Redirecting from) http(s)?:.*(\\\\d+|/|txt|yml|json)$",
                        "^Copied\\\\s(/([\\\\.\\\\_]?[a-zA-Z0-9\\\\s\\\\-\\\\.\\\\_\\\\+]+))+\\\\sto\\\\s(/([\\\\.\\\\_]?[a-zA-Z0-9\\\\s\\\\-\\\\.\\\\_\\\\+]+))+$",
                        "^SetMode u\\\\+w,go-w,a\\\\+rX\\\\s(/([\\\\.\\\\_]?[a-zA-Z0-9\\\\s\\\\-\\\\.\\\\_\\\\+]+))+\\\\s\\\\(in target '(.+)' from project '(.+)'\\\\)$",
                        "^total size is [0-9,]+\\\\s{2}speedup is [0-9,]+.[0-9]{2}$",
                        "^(strip: warning:|clang: warning:|Warning:|libtool: warning) (.*)$",
                        "^rsync --delete -av(?:\\\\s+--filter\\\\s+(?:P\\\\s+\\\\.\\\\*\\\\.\\\\?\\\\?\\\\?\\\\?\\\\?\\\\?|\\"-\\\\s+(?:CVS|\\\\.svn|\\\\.git|\\\\.hg)/\\")|\\\\s+--links)*\\\\s+\\"[^\\"]+\\"\\\\s+\\"[^\\"]+\\"$",
                        "^(While building module|In module) '[^']+' imported from [^:]+:\\\\d+:?$",
                        "^\\\\s{2}adding:\\\\s([^(]+)(\\\\s\\\\((deflated|stored)\\\\s\\\\d{1,2}\\\\%\\\\))$",
                        "^(\\t)?CompileC\\\\s+(\\\\S+\\\\.o)\\\\s+(\\\\S+\\\\.(?:m|mm|c|cc|cpp|cxx|swift|S))\\\\s+\\\\S+\\\\s+\\\\S+\\\\s+\\\\S+\\\\s+\\\\S+\\\\s+\\\\(in target '(.+)' from project '(.+)'\\\\)$",
                        "^CompileDocumentation\\\\s\\\\(in target '(.+)' from project '(.+)'\\\\)$",
                        "^\\\\+{1,4}\\\\s.+$",
                        "^(?:->\\\\s+)?Fetching podspec for `[^`]+`(?: from `[^`]+`)?(?:\\\\s+https://pod\\\\.mtl\\\\.alibaba-inc\\\\.com/pod-cdn/alibaba-specs/Specs/[a-zA-Z0-9/\\\\.-]+\\\\.podspec\\\\.json)?$",
                        "^(?:<module-includes>):\\\\d+:\\\\d+:\\\\s+note:\\\\s+in file included from\\\\s+(?:<module-includes>):\\\\d+:$",
                        "^WriteResultFile:\\\\s+(.+)$",
                        "^\\\\s{6}\\\\-\\\\sGenerating\\\\s+(module\\\\smap\\\\sfile|umbrella\\\\sheader|Info\\\\.plist\\\\sfile|dummy\\\\ssource)\\\\s+at\\\\s+`([^`]+)`$",
                        "^(Build settings|User defaults) from command line:$",
                        "^Selected xcframework slice .*$",
                        "^resolve plugin config\\\\.\\\\s{2}configName:\\\\s(\\\\w+),\\\\sconfigValue:\\\\s*(.*)$",
                        "^Build description signature:\\\\s[0-9a-z]+$",
                        "^\\\\s{2}\\\\$ /usr/bin/xcrun xcodebuild (build -project|-create-xcframework -framework) .*$",
                        "^Build settings from configuration file \\\\'(.*)/build.xcconfig\\\\':$",
                        "^Build description path: (.*)/[0-9a-z\\\\-\\\\_]+.xcbuild(data)?$",
                        "^#import (\\"|\\\\<)(.*\\\\..*)(\\"|\\\\>)$",
                        "^\\\\s{2}(◉|◯) Downloading:\\\\s\\\\[[█░]+\\\\]\\\\[\\\\d+/\\\\d+\\\\](\\\\[\\\\d+\\\\])?\\\\s\\\\d+\\\\.\\\\d{0,2}(GB|MB|KB)(/s)?(\\\\s\\\\w+)?\\\\s*$",
                        "^\\\\s{4}Expanding:\\\\s\\\\[\\\\d+/\\\\d+\\\\]\\\\'[^\\\\']+\\\\'\\\\.{3}(?:.*?(?:total file count:\\\\d+))?$",
                        "^CreateUniversalBinary\\\\s/.+?(?:\\\\.framework/\\\\w+)\\\\snormal\\\\sarm64\\\\\\\\\\\\sx86_64\\\\s\\\\(in target \\\\'.*?\\\\' from project \\\\'.*?\\\\'\\\\)$",
                        "^(\\\\[M?IRPass\\\\] (fix func|gen for): ){0,2}(\\\\$s\\\\w+){1,2}((\\\\[M?IRPass\\\\] (fix func|gen for): ){0,2}(\\\\$s\\\\w+)?)$",
                        "^\\\\[TrampolineGen\\\\] (MIR Pass start|IRPass run: \\\\w+\\\\.\\\\w+ with targetTriple: .*)$",
                        "^>>> _?\\\\$s[0-9A-Za-z\\\\_]+$",
                        "^\\\\s*\\\\{ platform:(?:macOS|iOS( Simulator)?), (?:arch:(arm64|x86_64),\\\\s)?(?:variant:[\\\\w\\\\s\\\\[\\\\],]+,\\\\s)?(?:id:[\\\\w\\\\-:]+(,\\\\s)?)?(?:OS:\\\\d+\\\\.\\\\d+(,\\\\s)?)?(?:name:[\\\\w\\\\s().-]+(,\\\\s)?)?(\\\\s)?\\\\}$",
                        "^\\\\[\\\\d+/\\\\d+\\\\] (OBJCXX|CXX|CC|OBJC|LIBTOOL-STATIC|STAMP) obj(/[\\\\w-]+)+\\\\.(o|a|stamp)$",
                        "^@(property|interface) .*$",
                        "^(\\\\s{2}inflating|\\\\s{3}creating):\\\\s/Users/<USER>/\\\\.emas/.*$",
                        "^\\\\s{2}URLSession:\\\\s\\\\w+(\\\\.[\\\\w\\\\-\\\\+]+)*\\\\shttp.*$",
                        "^copying\\\\s.*\\\\.h$",
                        "^SwiftDriver(\\\\\\\\)?\\\\s(\\\\S+(\\\\\\\\)?)(\\\\s\\\\S+)+\\\\s(\\\\w+(\\\\.\\\\w+)+)\\\\s\\\\(in target '(.+)' from project '(.+)'\\\\)$",
                        "^\\\\[\\\\d+\\\\/\\\\d+\\\\] [A-Z]+ obj\\\\/[\\\\w\\\\/.\\\\-]+\\\\.o$",
                        "^\\\\[emas_tmp_script_\\\\d+_mtl_script.sh\\\\] INFO: .*$",
                        "^\\\\s+(inflating|extracting): (\\\\/)?[\\\\w\\\\/.\\\\-\\\\s]+(\\\\.[a-z]+)?\\\\s+$",
                        "^\\\\s+creating: \\\\/[\\\\w\\\\/.\\\\-\\\\s]+\\\\/$",
                        "^\\\\[TBLTO\\\\] (skip |handle )?M?IR (func|fix): .*$",
                        "^[a-zA-Z0-9]+(_[a-z0-9]+){0,3}=?([\\\\w\\\\s\\\\'\\\\-]+)?$",
                        "^[a-z]+(\\\\.[\\\\w]+)+: \\\\d+$",
                        "^\\\\d{4}-\\\\d{2}-\\\\d{2} \\\\d\\\\d:\\\\d\\\\d:\\\\d\\\\d\\\\.\\\\d+(\\\\+\\\\d{4})? [a-zA-Z\\\\-]+\\\\[\\\\d{5}:\\\\d+\\\\](?!.*\\\\berror\\\\b).*$",
                        "^  \\\\/[\\\\w\\\\/.\\\\-\\\\s]+\\\\/[\\\\w\\\\-]+(\\\\.[a-z0-9]+)? -> \\\\w+.\\\\w+$",
                        "^Test (Case|Suite) .*$",
                        "^▸ (Compiling|Copying|) [\\\\w\\\\+]+\\\\.[a-z]+$",
                        "^Print: Entry, \\".*\\", Does Not Exist$",
                        "^uniapi generate ability \\\\w+\\\\.\\\\.\\\\.$",
                        "^\\tmodified:   [\\\\w\\\\/.\\\\-\\\\s]+\\\\/\\\\w+\\\\.[a-z]+$",
                        "^[\\\\w\\\\/.\\\\-\\\\s\\\\+]+\\\\/\\\\w+\\\\.o+$",
                        "^Transform(ing)?\\\\s(?:(artifact|file)\\\\s+)?((\\\\S+)(\\\\s+\\\\((.+?)\\\\))?|\\\\/[\\\\w\\\\/.\\\\-]+)\\\\s+with\\\\s+(\\\\w+Transform)",
                        "^Cached resource https?:(.*) is up-to-date \\\\(lastModified: null\\\\)\\\\.",
                        "^Downloading\\\\s(\\\\S+)\\\\s+to\\\\s+(\\\\S+)",
                        "^w:\\\\s+(.+):\\\\s+\\\\((\\\\d+),\\\\s*(\\\\d+)\\\\):\\\\s+(.+)$",
                        "^file or directory '([^']+)', not found$",
                        "^> Task :([\\\\w+\\\\-]+)(:[\\\\w\\\\-]+)? ?(NO-SOURCE|UP-TO-DATE|FROM-CACHE|FAILED)?$",
                        "^(i|w):(.*)",
                        "^Skipping task ':[\\\\w\\\\-]+(:\\\\w+)?' as it has no (actions|source files and no previous output files)\\\\.$",
                        "^Caching disabled for ((\\\\w+)Transform:|task) (.*) because:$",
                        "^\\\\u279c (Explicit|Implicit) dependency on target \\\\'[A-Za-z0-9_\\\\-\\\\.]+\\\\' in project \\\\'[A-Za-z0-9_\\\\-]+\\\\'( via options \\\\'(.*)\\\\' in build setting \\\\'(.*)\\\\')?",
                        "^Task \\\\':([\\\\w\\\\-]+)(:(\\\\w+))?\\\\' is not up-to-date because:$",
                        "^:[\\\\w\\\\-]+(:\\\\w+)?( (UP-TO-DATE|SKIPPED|\\\\(Thread.*\\\\) started.|\\\\(Thread.*\\\\) completed. Took \\\\d+.\\\\d+ secs.))?$",
                        "^(builtin-create-build-directory|write-file|\\\\/bin\\\\/sh -c) (\\\\/[A-Za-z0-9_.\\\\-]+)+(\\\\/?[A-Za-z0-9_.\\\\-]+\\\\.[A-Za-z0-9]+)?\\\\/?$",
                        "^([A-Z0-9_]+) ?=(.*)$",
                        "Deployment target for.*has been raised to.*for more details"
                      ],
                      "multiLinePatternsList": [
                      {
                        "patterns": [
                           "(?:\\\\t)?\\\\s*at\\\\s+([\\\\w.$<>/]+)\\\\(([^():]+|Unknown Source)(?::(\\\\d+))?\\\\)$"
                        ],
                        "maxLineNum": 2
                      }],
                      "skipLinePatterns": {
                        "((/)?.+/(.*):.*:.*):\\\\s(warning|note):\\\\s(.*)$": 2
                      },
                      "extra": {}
                    }
                """;

        JSONObject test = JSON.parseObject(requestJson);

        ErrorLogAnalyzeRequest request = JSON.parseObject(requestJson, ErrorLogAnalyzeRequest.class);
//        Map<Pattern, Integer> skipPatterns = Maps.newHashMapWithExpectedSize(1);
//        skipPatterns.put(Pattern.compile("((\\/)?.+\\/(.*):.*:.*):\\s(warning|note):\\s(.*)$") ,2);
//        request.setSkipLinePatterns(skipPatterns);
        return request;
    }


    // Android
    private ErrorLogAnalyzeRequest getAndroidErrorLogAnalyzeRequest() {

        String requestJson = """
                 {
                        "maxChunkLength": 40000,
                        "maxLineLength": 1000,
                        "maxRawLogReturnNum": 3,
                        "isFilteringLongLine": true,
                        "errorLogDownloadUrl": "https://mtl4.alibaba-inc.com/scheduler/jobs/8464983/tasks/494bb6a085ed444c8e8e5cf5726c5b2b/logDownload",
                        "patterns": [
                          "^Transform(ing)?\\\\s(?:(artifact|file)\\\\s+)?((\\\\S+)(\\\\s+\\\\((.+?)\\\\))?|\\\\/[\\\\w\\\\/.\\\\-]+)\\\\s+with\\\\s+(\\\\w+Transform)",
                          "^Parsing\\\\s(.+package\\\\.xml)",
                          "^$",
                          "^Skipping\\\\s(.*)\\\\sas\\\\sit\\\\sis\\\\sup-to-date\\\\.$",
                          "^Cached resource https?:(.*) is up-to-date \\\\(lastModified: null\\\\)\\\\.",
                          "^Creating\\\\sconfiguration\\\\s[a-zA-Z0-9.-]+$",
                          "^AAPT2\\\\saapt2-([0-9]+\\\\.?)+-[0-9]+-linux Daemon #[0-9]+:\\\\s(starting|shutdown)",
                          "^\\\\s{2}No\\\\shistory\\\\sis\\\\savailable\\\\.$",
                          "^BUILD\\\\sFAILED\\\\sin\\\\s([0-9]+m)?\\\\s[0-9]+s$",
                          "^(Dexing)\\\\s\\\\'(\\\\/[a-zA-z0-9\\\\.\\\\-\\\\_]+)+\\\\'\\\\sto\\\\s\\\\'file:(\\\\/[a-zA-Z0-9\\\\.\\\\-\\\\_]+)+(\\\\/)?\\\\'$",
                          "^(Dexing|Jar input)\\\\s(\\\\/[a-zA-z0-9\\\\.\\\\-\\\\_]+)+$",
                          "^Found locally available resource with matching checksum:\\\\s\\\\[http:\\\\/\\\\/(.*)\\\\]$",
                          "^\\\\[[= ]{39}\\\\]\\\\s\\\\d{2}%\\\\sUnzipping...(.*)$",
                          "^Downloading\\\\s(\\\\S+)\\\\s+to\\\\s+(\\\\S+)",
                          "^Compiling( (xml|XML))? (file|table) (.*)\\\\sto\\\\s(.*)$",
                          "^Compiling image file (.*)\\\\.png$",
                          "^(((\\\\w+)Transform):|Task) (.*) is not up-to-date because:$",
                          "^Caching disabled for ((\\\\w+)Transform:|task) (.*) because:$",
                          "^(Loaded cache entry for AarToClassTransform|Build cache key for AarToClassTransform): (.*) (is|with cache key) (\\\\w+)$",
                          "^Removed @Metadata annotation from (.*)\\\\.(\\\\w+)$",
                          "^((\\\\/)?.+\\\\/(.*):.*:.*):\\\\s(warning|note):\\\\s(.*)$",
                          "^> Task :([\\\\w+\\\\-]+)(:[\\\\w\\\\-]+)? ?(NO-SOURCE|UP-TO-DATE|FROM-CACHE|FAILED)?$",
                          "^Task \\\\':(\\\\w+):(\\\\w+)\\\\' is not up-to-date because:$",
                          "^\\\\s*(?:((\\"[a-zA-Z/\\\\_]+\\"\\\\:\\\\s)?[\\\\[{])|[^(\\\\[{)]+[}\\\\],])$",
                          "^\\\\s+\\"[\\\\w\\\\[\\\\]\\\\=,\\\\.\\\\(\\\\)/\\\\s\\\\-\\\\;\\\\~\\\\:]+\\"(,|: \\".*\\")?$",
                          "^\\\\w+Transform \\\\(Thread(.*)\\\\) (started\\\\.|(completed\\\\. Took ([\\\\d\\\\.]+) secs\\\\.))$",
                          "^\\\\s{8}\\\\/home\\\\/<USER>\\\\/\\\\.emas\\\\/build\\\\/\\\\d+\\\\/workspace\\\\/build(\\\\/[\\\\w\\\\-\\\\.]+)+\\\\\\\\$",
                          "^Task ':\\\\w+' is not up-to-date because:",
                          "^(> )?Task :[\\\\w\\\\-]+(:\\\\w+)? ((in [\\\\w\\\\-]+ (Starting|Finished))|([a-z]+ )?class loader hash: \\\\w+){0,1}$",
                          ":\\\\w+ \\\\(Thread\\\\[Execution worker for(.*)\\\\]\\\\)",
                          "^Generating PNG: \\\\[(.*)\\\\.png\\\\] from \\\\[(.*\\\\.xml)\\\\]$",
                          "^:\\\\w+ \\\\(Thread\\\\[Execution worker for(.*)\\\\]\\\\) (started\\\\.|completed\\\\. Took [\\\\d\\\\.]+ secs\\\\.)$",
                          "^All input files are considered out-of-date for incremental task ':\\\\w+'$",
                          "^Caching disabled for task ':\\\\w+(:\\\\w+)?' because:$",
                          "^\\\\/home\\\\/<USER>\\\\/.gradle\\\\/(.*)\\\\.\\\\w+:\\\\d+: AAPT: (warn|error): (.*)\\\\.$",
                          "^> Transform artifact [\\\\w\\\\-\\\\d\\\\.]+\\\\.aar \\\\((.*)\\\\) with \\\\w+Transform$",
                          "^w:\\\\s+(.+):\\\\s+\\\\((\\\\d+),\\\\s*(\\\\d+)\\\\):\\\\s+(.+)$",
                          "^file or directory '([^']+)', not found$",
                          "^==============================templateFile (\\\\/[\\\\w\\\\/.\\\\-]+) ==$",
                          "^mainDX=\\\\/[\\\\w\\\\/.\\\\-]+\\\\.dx$",
                          "\\\\w+ 模板存在",
                          "^(INFO|note|Note|i:)(:?)\\\\s(.*)$",
                          "^All input files are considered out-of-date for incremental task ':\\\\w+'.$",
                          "^(Warning|w|INFO|WARNING):(.*)$",
                          "^\\\\s*[\\\\^\\\\:\\\\;\\\\}\\\\]\\\\(\\\\)\\\\/\\\\<\\\\>]{0,10}$",
                          "^Executing transform \\\\w+Transform -> \\\\w+Transform (-> \\\\w+Transform )?on artifact (.*)\\\\.[a-z]+ \\\\(.*\\\\)$",
                          "^\\\\[\\\\w+Task\\\\]\\\\s+\\\\[(modify|copy|rename)\\\\]\\\\s+(修改(前 >>|后 <<) (file|name|source):|file|source = )(.*)$",
                          "^(        |\\\\.\\\\.)/[\\\\w/.\\\\-]+\\\\.[a-z]+(\\\\\\\\)?$",
                          "^Stored cache entry for \\\\w+Transform: /[\\\\w/.\\\\-]+\\\\.aar with cache key [\\\\w\\\\d]+$",
                          "^(Build cache key|(Stored|Loaded) cache entry) for task \\\\':.*\\\\' (is|with cache key) [\\\\w\\\\d]+$",
                          "^\\\\t{0,2}(ADDED|MERGED) from \\\\/[\\\\w\\\\/.\\\\-]+\\\\.xml:\\\\d+:\\\\d+-(\\\\d+:)?\\\\d+$",
                          "^\\\\d+($|\\\\s*((<(\\\\/)?.*(\\\\/)?>)|<[\\\\w\\\\-]+|-->\\\\/[\\\\w\\\\/.\\\\-]+\\\\.xml:\\\\d+:\\\\d+-(\\\\d+:)?\\\\d+|(android|tool):\\\\w+=\\".*\\"( \\\\/>)?))$",
                          "^\\\\d+-->(\\\\[.*\\\\] )?\\\\/[\\\\w\\\\/.\\\\-]+\\\\.[a-z]{1,8}(:\\\\d+:\\\\d+-\\\\d+(:\\\\d+)?)?$",
                          "^Skipping task ':[\\\\w\\\\-]+(:\\\\w+)?' as it has no (actions|source files and no previous output files)\\\\.$",
                          "^(Merging|Loading) \\\\w+ manifest \\\\/[\\\\w\\\\/.\\\\-]+\\\\.xml$",
                          "^Merging [\\\\w#-\\\\.]+ with lower \\\\[.*\\\\] \\\\w+Manifest.xml:\\\\d+:\\\\d+-(\\\\d+:)\\\\d+$",
                          "^MERGED from \\\\[.*\\\\] \\\\/[\\\\w\\\\/.\\\\-]+\\\\.xml:\\\\d+:\\\\d+-(\\\\d+:)?\\\\d+$",
                          "^\\\\+{1,4}\\\\s.+$",
                          "^Download http:\\\\/\\\\/[a-z\\\\-]+\\\\.alibaba-inc\\\\.com\\\\/[\\\\w\\\\/.\\\\-]+\\\\.[a-z]+$",
                          "^\\\\w+Classpath( -> project :\\\\w+){0,2}( -> (\\\\w+(\\\\.\\\\w+)+:[\\\\w\\\\-_]+:\\\\d+(\\\\.\\\\d+)+))+$",
                          "^:\\\\w+( UP-TO-DATE)?$",
                          "^addFolder\\\\(\\\\/[\\\\w\\\\/.\\\\-]+, [\\\\w\\\\/.\\\\-]+\\\\)(: entry [\\\\w\\\\/.\\\\-]+[\\\\w$]+\\\\.class)?$",
                          "^Resource missing. \\\\[HTTP (GET|HEAD): http(s)?:\\\\/\\\\/[a-zA-Z0-9.-]+(?:\\\\/[^\\\\s]*)?\\\\]$",
                          "^:[\\\\w-]+(:\\\\w+)?( (UP-TO-DATE|SKIPPED|\\\\(Thread.*\\\\) started.|\\\\(Thread.*\\\\) completed. Took \\\\d+.\\\\d+ secs.))?$",
                          "^\\\\s{11}Dependency path '\\\\w+(\\\\.\\\\w+)+:[\\\\w-]+(:[\\\\w\\\\.\\\\-]+)'( --> '\\\\w+(\\\\.\\\\w+)+:[\\\\w-]+(:[\\\\w\\\\.\\\\-]+)')+$",
                          "^\\\\{(?:[^{}]|\\"(?:\\\\\\\\.|[^\\"\\\\\\\\])*\\")+?\\\\}$",
                          "^\\\\s{4}C(\\\\+\\\\+)? (flags \\\\(\\\\w+\\\\)|Compiler):(.*)$",
                          "^\\\\/[\\\\w\\\\/.\\\\-]+CMakeLists\\\\.txt : C\\\\/C\\\\+\\\\+ [a-z]+\\\\|[\\\\w\\\\-]+ : (.*)$",
                          "^\\\\[\\\\d+\\\\/\\\\d+\\\\] Building C(XX)? object CMakeFiles\\\\/[\\\\w\\\\/.\\\\-]+\\\\.o$",
                          "^Executing task ':[\\\\w\\\\-]+(:\\\\w+)?' \\\\(up-to-date check took [\\\\d\\\\.]+ secs\\\\) due to:$",
                          "^Putting task artifact state for task ':[\\\\w\\\\-]+(:\\\\w+)?' into context took [\\\\d\\\\.]+ secs\\\\.$",
                          "^Reading library jar \\\\[\\\\/[\\\\w\\\\/.\\\\-]+\\\\.jar\\\\] (\\\\(filtered\\\\))?$",
                          "^>>> 配置 (class|source) dir(s)? : (\\\\[)?\\\\/[\\\\w\\\\/.\\\\-]+(\\\\])?$",
                          "^> Configure project :\\\\w+$",
                          "^Evaluating project ':\\\\w+' using build file '\\\\/[\\\\w\\\\/.\\\\-]+'\\\\.$",
                          "^resolve plugin config.  configName: .*$",
                          "^\\\\[emas_.*\\\\.sh\\\\] INFO: .*$",
                          "^Observed package id \\\\'.*\\\\' in inconsistent location '.*' \\\\(Expected '.*'\\\\)$",
                          "^Already observed package id \\\\'.*\\\\' in \\\\'.*\\\\'\\\\. Skipping duplicate at \\\\'.*\\\\'$",
                          "^\\\\s*\\\\d+K( \\\\.+)+ +\\\\d+% +\\\\d+(\\\\.\\\\d+)?(M|K) \\\\d+s$",
                        "^\\\\s+(inflating|extracting): (\\\\/)?[\\\\w\\\\/.\\\\-\\\\s\\\\+]+(\\\\.[a-z]+)?\\\\s+$",
                        "^\\\\s+creating: (\\\\/)?[\\\\w\\\\/.\\\\-\\\\s+]+\\\\/$"
                        ],
                        "multiLinePatternsList": [
                          {
                            "patterns": [
                              "\\tat\\\\s([a-zA-Z]+)((\\\\.|\\\\$|/)+[a-zA-Z0-9]+)+\\\\((([a-zA-Z0-9]+).([a-zA-Z]+)(:[0-9]+)?)|(Unknown\\\\sSource)\\\\)"
                            ],
                            "maxLineNum": 2
                          }
                        ],
                        "skipLinePatterns": {},
                        "extra": {}
                  }
                """;

        JSONObject test = JSON.parseObject(requestJson);

        ErrorLogAnalyzeRequest request = JSON.parseObject(requestJson, ErrorLogAnalyzeRequest.class);
//        Map<Pattern, Integer> skipPatterns = Maps.newHashMapWithExpectedSize(1);
//        skipPatterns.put(Pattern.compile("((\\/)?.+\\/(.*):.*:.*):\\s(warning|note):\\s(.*)$") ,2);
//        request.setSkipLinePatterns(skipPatterns);
        return request;

    }

    private List<ErrorLogDetectionAgent> getErrorLogDetectionAgents(int num) {

        String detectionPrompt = getDetectionPrompt();
        try {
            log.info("detectionPrompt:{}", objectMapper.writeValueAsString(detectionPrompt));
        } catch (IOException e) {
            log.error("detection prompt init failed", e);
        }

        List<ErrorLogDetectionAgent> errorLogDetectionAgents = Lists.newArrayList();
        for (int i = 0; i < num; i++) {
            try {
                errorLogDetectionAgents.add(getErrorLogDetectionAgent(StringKit.format("error-log-detection-agent-{}", i),
                        ModelEnum.QWEN2_5_72B_INSTRUCT, detectionPrompt));
            } catch (IOException e) {
                log.error("error log detection agent init failed", e);
            }
        }
        return errorLogDetectionAgents;
    }

    private static ErrorLogDetectionAgent getErrorLogDetectionAgent(String name, ModelEnum modelEnum, String prompt) throws IOException {
        String settingsStr = StringKit.format("{\"model_id\":\"{}\",\"temperature\":0.2,\"top_p\":0.2,\"max_tokens\":\"6144\",\"frequency_penalty\":1}", modelEnum.getModelId());

        PromptExecutionSettings executionSettings = PromptUtils.getPromptExecutionSettings(settingsStr);
        KernelFunction<?> chatFunction = KernelFunction.createFromPrompt(prompt)
                .withTemplateFormat(HandlebarsPromptTemplateFactory.HANDLEBARS_TEMPLATE_FORMAT)
                .build();

        ChatCompletionService chatCompletionService;
        try {
            chatCompletionService = modelEnum.getCapacityProvider().getCompletionClazz()
                    .getDeclaredConstructor(String.class, String.class).newInstance(modelEnum.getModelId(), modelEnum.getModelId());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        Kernel openAiBuildKernel = Kernel.builder()
                .withAIService(ChatCompletionService.class, chatCompletionService)
                .build();

        ErrorLogDetectionAgent errorLogDetectionAgent = ErrorLogDetectionAgent.builder()
                .name(name)
                .outputParser(new YamlOutputParser<>(ErrorLogDetectionResult.class, new String[]{"input"}))
                .identifier(StringKit.format("error-log-detection-agent-{}", StringKit.generateUuid()))
                .kernel(openAiBuildKernel)
                .chatFunction(chatFunction)
                .promptExecutionSettings(executionSettings)
                .build();

        return errorLogDetectionAgent;
    }

    private static ErrorLogRankAgent getErrorLogRankAgent(String name, ModelEnum modelEnum, String prompt) throws IOException {
        String settingsStr = StringKit.format("{\"model_id\":\"{}\",\"temperature\":0.2,\"top_p\":0.2,\"max_tokens\":\"6144\",\"frequency_penalty\":1}", modelEnum.getModelId());

        PromptExecutionSettings executionSettings = PromptUtils.getPromptExecutionSettings(settingsStr);
        KernelFunction<?> chatFunction = KernelFunction.createFromPrompt(prompt)
                .withTemplateFormat(HandlebarsPromptTemplateFactory.HANDLEBARS_TEMPLATE_FORMAT)
                .build();

        ChatCompletionService chatCompletionService;
        try {
            chatCompletionService = modelEnum.getCapacityProvider().getCompletionClazz()
                    .getDeclaredConstructor(String.class, String.class).newInstance(modelEnum.getModelId(), modelEnum.getModelId());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        Kernel openAiBuildKernel = Kernel.builder()
                .withAIService(ChatCompletionService.class, chatCompletionService)
                .build();

        ErrorLogRankAgent errorLogRankAgent = ErrorLogRankAgent.builder()
                .name(name)
                .outputParser(new YamlOutputParser<>(ErrorLogRankResult.class, new String[]{"input"}))
                .identifier(StringKit.format("error-log-rank-agent-{}", StringKit.generateUuid()))
                .kernel(openAiBuildKernel)
                .chatFunction(chatFunction)
                .promptExecutionSettings(executionSettings)
                .build();

        return errorLogRankAgent;
    }

    private String getDownloadUrl() {
//        return "https://mtl4.alibaba-inc.com/scheduler/jobs/7087131/tasks/51459528f1e845578dd733b5ef0c2aec/logDownload";
//        return "https://mtl4.alibaba-inc.com/scheduler/jobs/7087282/tasks/51459528f1e845578dd733b5ef0c2aec/logDownload";
        return "https://mtl4.alibaba-inc.com/scheduler/jobs/7092988/tasks/16e71f2f34824c1691cac758e52e9864/logDownload";
//        return "https://mtl4.alibaba-inc.com/scheduler/jobs/7092218/tasks/51459528f1e845578dd733b5ef0c2aec/logDownload";
//        return "https://mtl4.alibaba-inc.com/scheduler/jobs/7089488/tasks/51459528f1e845578dd733b5ef0c2aec/logDownload";
//        return "https://mtl4.alibaba-inc.com/scheduler/jobs/7090928/tasks/1a90324e61af4411a1d4d893ef38f5c0/logDownload";
//        return "https://mtl4.alibaba-inc.com/scheduler/jobs/7087131/tasks/51459528f1e845578dd733b5ef0c2aec/logDownload";
//        return "https://mtl4.alibaba-inc.com/scheduler/jobs/7086064/tasks/9f44de0375474f2bbb99ff4c8c08123a/logDownload";
    }

    private static String getRankPrompt() {
        return """
                ## 角色
                你是一位杰出的客户端运维专家，熟悉iOS和Android双端的日志分析，请分析从日志中筛选出的多个错误项，分析错误项是否有足够详细的报错信息，反映出构建错误的原因，基于此给每个错误项评价错误等级。

                ### 技能
                1. 日志分析：你能深入解读客户端构建过程中的日志信息，识别并评估每条错误信息的重要性及其对构建失败的直接贡献度。
                2. 错误评级：基于详细的错误信息，你能够给出错误的评级，区分高评级（核心问题，具体且直接反映构建问题）、低评级（普通问题，不具体，需要进一步排查）以及可忽略错误（如警告、过时信息等）。
                3. 顺序关联识别：你有能力识别错误信息之间的逻辑关系与时间序列，从而判断哪个是直接导致构建失败的核心错误。
                4. 汇总与建议：在分析结束后，你将提供一份错误概览，突出主要错误类型与关键信息。

                ### 错误评级标准
                #### 高评级标准（此类错误信息中错误信息具体直接，能够直接看出错误的原因，对用户而言直观明了）：
                带有具体信息的错误，如涉及到文件、产物、版本等等，能够反应出构建中出现的明确问题，同时可能存在 Caused by/error/failure/fatal/Exception/[!]/e 等错误关键词
                #### 高评级错误样例
                  a. Caused by: org.gradle.api.resources.MissingResourceException: Could not read script '/Users/<USER>//mtl_mini_publish.gradle' as it does not exist.
                  b. error: no type or protocol named 'ILiveIdleTaskManager'
                  c. Variant 'sourcesElements' capability com.taobao.nano.compose:gradle-plugin:*******-SNAPSHOT declares a component for use during runtime, and its dependencies declared externally:
                  d. Task 'assemable' not found in root project 'workspace'. Some candidates are: 'assemble'.
                 \s
                #### 低评级标准（此类错误信息中错误信息不够具体或没有，不能根据报错直接看出错误的原因，仍然需要看更多的日志，对用户而言不够直观）：
                1. warning信息，提示性的过期、废弃、超时等不会阻塞正常流程的日志
                2. 缺少具体错误内容的报错信息，总结性的报错信息，对用户进行错误诊断没有帮助，如：
                  a. 总结性报错，不具体：Task ':xxxx' failed due to compilation errors;
                  b. 同样是总结性报错，对用户而言这个报错仍然不清楚错在哪里：Execution failed for task ':compileDebugJavaWithJavac'. Execution failed for task ':powermsg:publishMavenPublicationToMavenRepository'.
                  c. 根项目报错，该错误仍然无法看出来问题：A problem occurred evaluating root project 'trade_rate'.
                  d. 提示依赖报错，但这个错误缺少具体的依赖相关信息：Could not resolve all task dependencies for configuration ':classpath'.
                  e. xcodeproj相关报错，该错误尽管提及了Xcodeproj生成失败，但造成失败的原因太过于宽泛，无法直接判断具体原因: Xcodeproj Generation Failed.
                3. 固定性的报错提示，对于排查问题没有帮助，如：
                  a. FAILURE: Build failed with an exception.
                  b. AATP error
                  c. ERR! Build failed
                  d. What went wrong:

                #### 错误评级案例
                评级的核心在于，针对本次构建失败，哪些错误有充足的报错信息，能够直接反应构建的问题，而不是还需要通过别的日志进行进一步的排查，以下是结合样例进行的说明总结供你参考。
                注意错误信息的顺序，前后顺序之间存在一定的关联性，比如：
                案例1:
                ---
                190 - [!] There is a circular dependency between LiveRoomSDK and AliTnode
                ...
                210 - /Users/<USER>/.emas/build/41699973/workspace/SourcePackages/Xcode/TBShop/install.rb:21:in `run': Xcodeproj Generation Failed. (RuntimeError)
                ---
                这个片段中，是由于190行爆出的循环依赖导致的构建失败；210行是前序依赖问题所导致的Xcodeproj生成失败，同时Xcodeproj生成失败这个错误不够具体；因此190为2级，210行为1级

                案例2:
                ---
                300 - /home/<USER>/.emas/build/41622110/workspace/sdk/src/anet/channel/status/NetworkFullLowLatency.java:128: error: cannot find symbol
                ...
                352 - org.gradle.api.tasks.TaskExecutionException: Execution failed for task ':awcn:compileJava'.
                353 - 	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.executeActions(ExecuteActionsTaskExecuter.java:84)
                354 - Caused by: org.gradle.api.internal.tasks.compile.CompilationFailedException: Compilation failed; see the compiler error output for details.
                355 - 	at org.gradle.api.internal.tasks.compile.JdkJavaCompiler.execute(JdkJavaCompiler.java:48)
                ---
                这个片段中，是由于300行爆出的符号缺失问题导致的构建失败，352行及之后是整体的构建失败总结报错，不能直接反馈报错原因，因此300行为2级，352-355行为1级

                ## 筛选出的错误项
                ```
                {{{detectedErrorItems}}}
                ```

                ## 限制
                1. 你仅限于分析提供的日志片段，而不能访问实际的代码仓库或其他辅助性资料。
                2. 分析报告将严格遵循评级标准，不对超出范围的错误类型进行评论或假设。
                3. 在输出中，每个错误的评级等级（0-2，对应可忽略错误、普通错误、核心错误）必须与日志原始行号匹配，以保持信息的一致性和追溯性。其中低评级的错误最多1级，只有符合高评级标准的错误才能给予2级。
                4. 注意核心错误的个数，不超过4个，不少于1个，同类型错误中尽量只有1个核心错误。
                5. 最后对整体错误情况使用中文进行简要总结与分析，包括主要错误的类型情况和涉及的错误内容。

                {{{instructions}}}
                                        """;
    }


    private static String getDetectionPrompt() {
        return """
                        你是一位客户端运维专家，熟悉iOS和Android双端的客户端日志。'#'号内是一份完整日志中的提取出的精简日志，可能包含多项错误，找出其中能够进行错误原因分析的错误项。
                        好的错误项应该能够反馈出真实的构建错误原因，具有分析价值，这些需要被找出
                        以下是对好的错误项进行总结：
                        1. 聚焦在带有具体信息的错误，如涉及到文件、产物、版本号等等，如:
                          a. Caused by: xxx: artifact file does not exist: 'xxx.aar/xml'
                          b. error: no type or protocol named 'xxxx'
                        2. 留意 Caused by/error/failure/fatal/Exception/[!] 等标识，附近可能有详细描述的报错信息，同时 Caused by: 行之后，往往有更详细的报错信息描述，请留意

                        以下是不好的错误项目表征：
                        1. 避开 warning/note 等语义中包含过期、废弃、超时等提示性日志；
                        2. 流程性的固定的报错信息，如:
                          a. FAILURE: Build failed with an exception.
                          b. AATP error.
                          c. ERR! Build failed.
                          d. plugin run status: FAILED, output: null
                          e. * What went wrong:
                        3. 错误提示的粒度过大，错误信息不具体，参考意义较弱，如：
                          a. Task ':xxxx' failed due to compilation errors
                          b. Execution failed for task ':publishMavenPublicationToMavenRepository'.
                          c. Could not resolve all task dependencies for configuration ':classpath'.
                          d. A problem occurred configuring root project 'workspace'.

                        应该被找出的内容样例：
                        以下是一个循环依赖错误，包括了关于循环依赖的错误信息，同时包含了涉及到循环依赖的相关类文件
                        ---
                        1 - org.gradle.api.CircularReferenceException: Circular dependency between the following tasks:
                        3 - :ugc-component-rate:compileReleaseAidl
                        3 - \\--- :ugc-core:compileReleaseAidl
                        4 -      \\--- :ugc-component-rate:compileReleaseAidl (*)
                        ---
                        以下是2个符号缺失错误，包含了符号缺失的报错信息，以及文件路径，找不到的符号
                        ---
                        11 - /home/<USER>/.emas/build/39052959/src/main/java/com/jsbridge/LivePrivateApiImpl.java:301: error: cannot find symbol
                        --
                        3 - [!] Unable to find a specification for `ThemisService (= 2.16.11-SNAPSHOT)` depended upon by `ThemisCapability/ThemisService`
                        ---
                        以下是一个符号缺失错误，包含了符号缺失的报错信息，同时在后续行中进一步说明了构建版本的异常和java版本相关
                        ---
                        1 - Caused by: org.gradle.internal.component.NoMatchingGraphVariantsException: No matching variant of com.taobao.nano.compose:gradle-plugin:*******-SNAPSHOT:20241031.061502-2 was found. The consumer was configured to find a library for use during runtime, compatible with Java 8, packaged as a jar, and its dependencies declared externally, as well as attribute 'org.gradle.plugin.api-version' with value '8.5' but:
                        2 -   - Variant 'apiElements' capability com.taobao.nano.compose:gradle-plugin:*******-SNAPSHOT declares a library, packaged as a jar, and its dependencies declared externally:
                        3 -       - Incompatible because this component declares a component for use during compile-time, compatible with Java 11 and the consumer needed a component for use during runtime, compatible with Java 8
                        ---

                        精简日志由行号和内容组成：<lineNum> - <content>
                        ###
                        {{{errorLogSplit}}}
                        ###

                        输出要求：
                        1. 遵循上方的总结，找出合适的错误项
                        2. 每个错误项不超过 5 行，连续的错误日志请进行拆分，聚焦在核心的报错内容上，放弃一些无用的提示或者堆栈等
                        3. 错误信息中保留原文中的关键错误信息（原文，不需要翻译，包含错误提示，如：error: xxx）
                        4. 请尽量多的找出不同种类的错误
                        5. 对于同类/相似的错误（错误描述语义相同或者相似），请最多返回 2 个
                        6. 日志行均有其对应的行号，保持行号与错误项的一致性
                        
                        {{{instructions}}}
                """;
    }

    private Pattern[] getAndroidPatterns() {
        Pattern[] ANDROID_PATTERNS = {
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\sTransforming\\s(?:artifact\\s+)?(\\S+)\\s+\\((.+?)\\)\\s+with\\s+(\\w+)"),
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\sParsing\\s(.+package\\.xml)"),
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\s$"),
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\sSkipping\\s[a-zA-Z0-9]+:\\s(\\/[a-zA-Z0-9\\.\\-_]+)+\\sas\\sit\\sis\\sup-to-date\\."),
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\sCached\\sresource\\shttps?:\\/\\/(?:[a-zA-Z0-9.-]+\\.)+[a-zA-Z]{2,}(\\/[a-zA-Z0-9.-]+)+\\sis\\sup-to-date\\s\\(lastModified:\\snull\\)\\."),
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\sCreating\\sconfiguration\\s[a-zA-Z0-9.-]+$"),
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\sAAPT2\\saapt2-([0-9]+\\.?)+-[0-9]+-linux Daemon #[0-9]+:\\s(starting|shutdown)"),
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\s{3}No\\shistory\\sis\\savailable\\.$"),
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\sBUILD\\sFAILED\\sin\\s([0-9]+m)?\\s[0-9]+s$"),
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\sDexing\\s'(\\/\\/[a-zA-z0-9\\.\\-_]+)+'\\sto\\s'file:(\\/\\/[a-zA-Z0-9\\.\\-_]+)+(\\/)?'$"),
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\sfile\\sor\\sdirectory\\s\\'(\\/[a-zA-Z0-9\\.\\-\\_]+)+\\',\\snot\\sfound$"),
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\sTask\\s\\'([a-zA-Z\\:]+)\\' is not up-to-date because:$"),
                Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\s{3}-\\sDownloading\\s(\\S+)\\s+to\\s+(\\S+)")
        };
        return ANDROID_PATTERNS;
    }


    @Test
    public void testMergeConsecutiveLines() {
        // 测试连续的行号
        List<Integer> lines1 = Arrays.asList(1, 2, 3, 4, 7, 8, 9, 12);
        List<LogLineRange> ranges1 = mergeConsecutiveLines(lines1);
        Assertions.assertEquals(3, ranges1.size());
        Assertions.assertEquals(1, ranges1.get(0).getStartLine());
        Assertions.assertEquals(4, ranges1.get(0).getEndLine());
        Assertions.assertEquals(7, ranges1.get(1).getStartLine());
        Assertions.assertEquals(9, ranges1.get(1).getEndLine());
        Assertions.assertEquals(12, ranges1.get(2).getStartLine());
        Assertions.assertEquals(12, ranges1.get(2).getEndLine());

        // 测试单个行号
        List<Integer> lines2 = Arrays.asList(1);
        List<LogLineRange> ranges2 = mergeConsecutiveLines(lines2);
        Assertions.assertEquals(1, ranges2.size());
        Assertions.assertEquals(1, ranges2.get(0).getStartLine());
        Assertions.assertEquals(1, ranges2.get(0).getEndLine());

        // 测试空列表
        List<Integer> lines3 = Collections.emptyList();
        List<LogLineRange> ranges3 = mergeConsecutiveLines(lines3);
        Assertions.assertTrue(ranges3.isEmpty());

        // 测试不连续的行号
        List<Integer> lines4 = Arrays.asList(1, 3, 5, 7);
        List<LogLineRange> ranges4 = mergeConsecutiveLines(lines4);
        Assertions.assertEquals(4, ranges4.size());
        Assertions.assertEquals(1, ranges4.get(0).getStartLine());
        Assertions.assertEquals(1, ranges4.get(0).getEndLine());
        Assertions.assertEquals(3, ranges4.get(1).getEndLine());
        Assertions.assertEquals(3, ranges4.get(1).getStartLine());
        Assertions.assertEquals(5, ranges4.get(2).getEndLine());
        Assertions.assertEquals(5, ranges4.get(2).getStartLine());
        Assertions.assertEquals(7, ranges4.get(3).getEndLine());
        Assertions.assertEquals(7, ranges4.get(3).getStartLine());

    }

    @Test
    public void testDetectedItemSortAndSelect() {
        List<ErrorLogDetectedItem> detectionList = new ArrayList<>();
        ErrorLogDetectedItem item1 = new ErrorLogDetectedItem();
        item1.setStartLine(10);
        item1.setEndLine(15);
        item1.setMessage("This is a test message 1");
        detectionList.add(item1);

        ErrorLogDetectedItem item2 = new ErrorLogDetectedItem();
        item2.setStartLine(20);
        item2.setEndLine(25);
        item2.setMessage("This is a test message 2");
        detectionList.add(item2);

        Random random = new Random();
        for (int i = 0; i < 50; i++) {
            ErrorLogDetectedItem item = new ErrorLogDetectedItem();
            item.setStartLine(random.nextInt(200));
            item.setEndLine(random.nextInt(200));
            item.setMessage("This is a random message " + i);
            detectionList.add(item);
        }

        // 按 startLine 降序排序并选择前30个项目
        List<ErrorLogDetectedItem> top30Items = detectionList.stream()
                .sorted(Comparator.comparingInt(ErrorLogDetectedItem::getStartLine).reversed())
                .limit(30)
                .toList();

        // 输出结果
        System.out.println("具有最大起始行号的前 30 个条目: " + top30Items);
    }

}