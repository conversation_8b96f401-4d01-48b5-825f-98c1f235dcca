package com.alibaba.emas.mtl4.services.ai.agents.serve.agents.code.plantuml;

import com.alibaba.emas.agent.base.services.chatcompletion.ChatMessageContent;
import com.alibaba.emas.agent.ext.kernel.model.ModelEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * .
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class CodeDiagramAgentV2Test {

    @Test
    public void test() {
        CodeDiagramAgentV2 codeDiagramAgentV2 = CodeDiagramAgentV2.builder()
                .chatFunction(CodeDiagramAgentTest.prompt())
                .modelId(ModelEnum.getLatestQwenModelId())
                .build();
        Map<String, Object> params = CodeDiagramAgentTest.getParams();
        List<ChatMessageContent<?>> messageContents = codeDiagramAgentV2.generateReply(null, params);
        log.info(messageContents.get(0).getContent());
    }

}