package com.alibaba.emas.mtl4.services.ai.agents.serve.agents.code;

import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.alibaba.emas.agent.base.services.chatcompletion.AuthorRole;
import com.alibaba.emas.agent.base.services.chatcompletion.ChatMessageContent;
import com.alibaba.emas.agent.ext.context.SessionContext;
import com.alibaba.emas.agent.ext.kernel.model.ModelEnum;
import com.alibaba.emas.agent.role.agents.ConversableAgent;
import com.alibaba.emas.agent.role.agents.SimpleAssistantAgent;
import com.alibaba.emas.mtl4.services.ai.agents.serve.agents.aimi.AimiGroupChatManager;
import com.alibaba.emas.mtl4.services.ai.agents.serve.manager.agent.AgentsManager;
import com.alibaba.emas.mtl4.services.ai.enums.SessionChatPlatform;
import com.alibaba.fastjson.JSON;

import com.google.common.collect.Lists;
import com.taobao.pandora.boot.test.junit4.PandoraBootRunner;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import static com.alibaba.emas.agent.role.utils.JsonUtils.prettyFormat;

@Slf4j
//@RunWith(PandoraBootRunner.class)
public class AimiGroupChatManagerTest {

    public Map<String, List<String>> contentMap = new LinkedHashMap<>() {{
        put("default-agent", Arrays.asList(
            "你是谁?"
        ));
        put("knowledge-agent", Arrays.asList(
            "orange说明文档在哪里"
        ));
        put("mtl-agent", Arrays.asList(
            "查询手机淘宝Android当前版本计划"
        ));
        put("code-agent", Arrays.asList(
            "com.taobao.android.autosize.config.ConfigManager有哪些函数"
        ));
        //put("motu-agent", Arrays.asList(
        //    "应用ID为12278902@android，Crash类型为ANDROID_JAVA_CRASH，聚合ID为faa0aecbee741735d1a3da45035d5a85的crash堆栈是什么"
        //));
        //put("ide-code-agent", Arrays.asList(
        //    "com.taobao.android.autosize.config.ConfigManager有哪些函数"
        //));
    }};

    //String content = "你是谁?";
    //String content = "先从知识库里面查找agoo是做什么的，再通过code-agent告诉我它的代码仓库";
    //String content = "应用ID为12278902@android，Crash类型为ANDROID_JAVA_CRASH，聚合ID为faa0aecbee741735d1a3da45035d5a85的crash堆栈是什么";
    //String content = "获取Crash的对应堆栈并分析可能原因：https://motu.alibaba-inc"
    //    + ".com/#/emas/crash/converge/detail?appId=12278902%40android&l_=%7B%22id%22%3A"
    //    + "%22c9c8e7a55d4e3105720f7bdd7c9ad18b%22%2C%22errorType%22%3A%22ANDROID_JAVA_CRASH%22%2C%22begin%22%3A"
    //    + "%221747040230556%22%2C%22end%22%3A%221747040230556%22%2C%22subject%22%3A%22analysis%22%2C%22version%22%3A"
    //    + "%22%22%2C%22countVersion%22%3A%22%22%2C%22compareVersion%22%3A%22%22%2C%22date%22%3A1747040250581%2C"
    //    + "%22compareDate%22%3A1746953850581%2C%22historyRange%22%3A%5B1746435450582%2C1747040250582%5D%2C"
    //    + "%22precision%22%3A%221%22%2C%22threadName%22%3A%22%22%2C%22isBlackLabel%22%3A%22%22%2C"
    //    + "%22suspectedBlackLabel%22%3A%22%22%7D&fbi=convergeDetail";

    //String content = "agoo是做什么的";
    //String content = "摩天轮上我的相关ios模块的代码库是哪些，并结合code-agent给出这些代码库的主要功能";
    //String content = "摩天轮上我相关的ios模块的代码库有哪些，并分析这些代码仓库的主要功能";
    //String content = "detail-solution/TTDetail_iOS 主要功能有哪些";
    //String content = """
    //    <system>
    //    You are part of a multi-agent system, designed to make agent coordination and execution easy.
    //    Agents uses two primary abstraction: **Agents** and **Handoffs**.
    //    An agent encompasses instructions and tools and can hand off a conversation to another agent when appropriate.
    //    Handoffs are achieved by calling a handoff function, named `transfer_to_other_agent`.
    //    Transfers between agents are handled seamlessly in the background; do not mention or draw attention to these transfers in your conversation with the user.
    //    </system>
    //
    //    <user>
    //    User original input:
    //    摩天轮上我的相关ios模块的代码库有哪些，并分析这些代码仓库的主要功能
    //
    //    Actionable plan:
    //    1.[mtl-agent] 查询用户在摩天轮平台上的iOS相关模块代码库；
    //    2.[code-agent] 分析这些模块代码仓库的主要功能；
    //    </user>
    //
    //    Important: According to the plan, after the current step is completed, handoff to the next agent to enter the next step.
    //    """;
    //String content = """
    //    1.[mtl-agent] 查询用户在摩天轮平台上的iOS相关模块代码库
    //    2.[code-agent] 分析这些代码仓库的主要功能
    //
    //    Important: At the beginning of each step, use the `transfer_to_other_agent` tool to transfer to other agent.
    //    """;
    //String content = """
    //    1.[mtl-agent] 查询用户在摩天轮平台上的iOS相关模块代码库
    //    2.[code-agent] 分析这些代码仓库的主要功能
    //    """;

    //String content = "Weex在MTL 手淘Android里集成过吗";
    //String content = "com.taobao.cus是谁负责的";
    //String content = "手机淘宝Android 今天的版本计划是哪个？";
    //String content = "夏乙，工号075063，负责过哪些模块的开发？";
    //String content = "com.taobao.android.autosize.config.ConfigManager有哪些函数";
    String content = "根据提供的上下文，优化代码\n"
        + "\n"
        + "\n"
        + "\n"
        + "``` .\n"
        + "\n"
        + "```\n"
        + "\n"
        + "```swift Core/Sources/HostApp/GeneralSettings/AimiBinaryServiceManager.swift\n"
        + "import Foundation\n"
        + "import Combine\n"
        + "import Logger\n"
        + "\n"
        + "public enum AimiBinaryServiceStatus {\n"
        + "    case stopped\n"
        + "    case starting\n"
        + "    case running(pid: Int32)\n"
        + "    case stopping\n"
        + "    case error(String)\n"
        + "}\n"
        + "\n"
        + "@MainActor\n"
        + "public class AimiBinaryServiceManager: ObservableObject {\n"
        + "    @Published public var status: AimiBinaryServiceStatus = .stopped\n"
        + "    @Published public var logs: [String] = []\n"
        + "    \n"
        + "    private var process: Process?\n"
        + "    private var statusCheckTimer: Timer?\n"
        + "    private let maxLogLines = 100\n"
        + "    \n"
        + "    public static let shared = AimiBinaryServiceManager()\n"
        + "    \n"
        + "    private init() {}\n"
        + "    \n"
        + "    public func startService() async {\n"
        + "        guard case .stopped = status else {\n"
        + "            addLog(\\\"服务已在运行或正在启动中\\\")\n"
        + "            return\n"
        + "        }\n"
        + "        \n"
        + "        status = .starting\n"
        + "        addLog(\\\"正在启动 aimi-binary 服务...\\\")\n"
        + "        \n"
        + "        // 查询并终止占用3000端口的服务\n"
        + "        await killProcessesOnPort3000()\n"
        + "        \n"
        + "        do {\n"
        + "            // 获取 darwin-arm64 目录路径\n"
        + "            guard let darwinArm64URL = getDarwinArm64Directory() else {\n"
        + "                throw ServiceError.resourceNotFound(\\\"找不到 darwin-arm64 目录\\\")\n"
        + "            }\n"
        + "            \n"
        + "            // 检查 aimi-binary 文件是否存在\n"
        + "            let aimiBinaryURL = darwinArm64URL.appendingPathComponent(\\\"aimi-binary\\\")\n"
        + "            guard FileManager.default.fileExists(atPath: aimiBinaryURL.path) else {\n"
        + "                throw ServiceError.resourceNotFound(\\\"找不到 aimi-binary 文件: \\\\(aimiBinaryURL.path)\\\")\n"
        + "            }\n"
        + "            \n"
        + "            // 确保 aimi-binary 文件有执行权限\n"
        + "            do {\n"
        + "                let attributes = try FileManager.default.attributesOfItem(atPath: aimiBinaryURL.path)\n"
        + "                let permissions = attributes[.posixPermissions] as? NSNumber\n"
        + "                addLog(\\\"aimi-binary 当前权限: \\\\(permissions?.stringValue ?? \\\"未知\\\")\\\")\n"
        + "                \n"
        + "                // 设置执行权限\n"
        + "                try FileManager.default.setAttributes([.posixPermissions: 0o755], ofItemAtPath: aimiBinaryURL.path)\n"
        + "                addLog(\\\"已设置 aimi-binary 执行权限\\\")\n"
        + "            } catch {\n"
        + "                addLog(\\\"设置文件权限失败: \\\\(error)\\\")\n"
        + "            }\n"
        + "            \n"
        + "            // 总是创建或更新 .env 文件\n"
        + "            let envURL = darwinArm64URL.appendingPathComponent(\\\".env\\\")\n"
        + "            try createDefaultEnvFile(at: envURL)\n"
        + "            \n"
        + "            // 创建进程\n"
        + "            let process = Process()\n"
        + "            process.executableURL = URL(fileURLWithPath: \\\"/bin/bash\\\")\n"
        + "            process.arguments = [\\\"-c\\\", \\\"cd '\\\\(darwinArm64URL.path)' && source .env && ./aimi-binary\\\"]\n"
        + "            process.currentDirectoryURL = darwinArm64URL\n"
        + "            \n"
        + "            // 设置环境变量\n"
        + "            var environment = ProcessInfo.processInfo.environment\n"
        + "            environment[\\\"PATH\\\"] = \\\"/usr/local/bin:/usr/bin:/bin\\\"\n"
        + "            process.environment = environment\n"
        + "            \n"
        + "            // 设置输出管道\n"
        + "            let outputPipe = Pipe()\n"
        + "            let errorPipe = Pipe()\n"
        + "            process.standardOutput = outputPipe\n"
        + "            process.standardError = errorPipe\n"
        + "            \n"
        + "            // 监听输出\n"
        + "            outputPipe.fileHandleForReading.readabilityHandler = { [weak self] handle in\n"
        + "                let data = handle.availableData\n"
        + "                if !data.isEmpty, let output = String(data: data, encoding: .utf8) {\n"
        + "                    Task { @MainActor in\n"
        + "                        self?.addLog(\\\"输出: \\\\(output.trimmingCharacters(in: .whitespacesAndNewlines))\\\")\n"
        + "                    }\n"
        + "                }\n"
        + "            }\n"
        + "            \n"
        + "            errorPipe.fileHandleForReading.readabilityHandler = { [weak self] handle in\n"
        + "                let data = handle.availableData\n"
        + "                if !data.isEmpty, let output = String(data: data, encoding: .utf8) {\n"
        + "                    Task { @MainActor in\n"
        + "                        self?.addLog(\\\"错误: \\\\(output.trimmingCharacters(in: .whitespacesAndNewlines))\\\")\n"
        + "                    }\n"
        + "                }\n"
        + "            }\n"
        + "            \n"
        + "            // 设置进程终止处理\n"
        + "            process.terminationHandler = { [weak self] process in\n"
        + "                Task { @MainActor in\n"
        + "                    self?.addLog(\\\"进程已终止，退出码: \\\\(process.terminationStatus)\\\")\n"
        + "                    self?.status = .stopped\n"
        + "                    self?.process = nil\n"
        + "                    self?.stopStatusMonitoring()\n"
        + "                }\n"
        + "            }\n"
        + "            \n"
        + "            // 启动进程\n"
        + "            try process.run()\n"
        + "            self.process = process\n"
        + "            \n"
        + "            // 等待一小段时间确保进程启动\n"
        + "            try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒\n"
        + "            \n"
        + "            if process.isRunning {\n"
        + "                status = .running(pid: process.processIdentifier)\n"
        + "                addLog(\\\"服务启动成功，PID: \\\\(process.processIdentifier)\\\")\n"
        + "                addLog(\\\"请使用'刷新状态'按钮手动更新服务状态\\\")\n"
        + "            } else {\n"
        + "                status = .error(\\\"进程启动失败\\\")\n"
        + "                addLog(\\\"进程启动失败\\\")\n"
        + "            }\n"
        + "            \n"
        + "        } catch {\n"
        + "            status = .error(error.localizedDescription)\n"
        + "            addLog(\\\"启动失败: \\\\(error.localizedDescription)\\\")\n"
        + "        }\n"
        + "    }\n"
        + "    \n"
        + "    public func stopService() async {\n"
        + "        guard let process = process, process.isRunning else {\n"
        + "            addLog(\\\"服务未在运行\\\")\n"
        + "            status = .stopped\n"
        + "            return\n"
        + "        }\n"
        + "        \n"
        + "        status = .stopping\n"
        + "        addLog(\\\"正在停止服务...\\\")\n"
        + "        \n"
        + "        // 尝试优雅关闭\n"
        + "        process.terminate()\n"
        + "        \n"
        + "        // 等待进程结束\n"
        + "        let deadline = Date().addingTimeInterval(5.0) // 5秒超时\n"
        + "        while process.isRunning && Date() < deadline {\n"
        + "            try? await Task.sleep(nanoseconds: 100_000_000) // 100ms\n"
        + "        }\n"
        + "        // print一条日志\n"
        + "        \n"
        + "        // 如果还在运行，强制杀死\n"
        + "        if process.isRunning {\n"
        + "            addLog(\\\"强制终止进程...\\\")\n"
        + "            process.interrupt()\n"
        + "            \n"
        + "            // 再等待一段时间\n"
        + "            let forceDeadline = Date().addingTimeInterval(2.0)\n"
        + "            while process.isRunning && Date() < forceDeadline {\n"
        + "                try? await Task.sleep(nanoseconds: 100_000_000)\n"
        + "            }\n"
        + "            \n"
        + "            if process.isRunning {\n"
        + "                // 使用 kill 命令强制终止\n"
        + "                let killProcess = Process()\n"
        + "                killProcess.executableURL = URL(fileURLWithPath: \\\"/bin/kill\\\")\n"
        + "                killProcess.arguments = [\\\"-9\\\", \\\"\\\\(process.processIdentifier)\\\"]\n"
        + "                try? killProcess.run()\n"
        + "                killProcess.waitUntilExit()\n"
        + "            }\n"
        + "        }\n"
        + "        \n"
        + "        self.process = nil\n"
        + "        status = .stopped\n"
        + "        stopStatusMonitoring()\n"
        + "        addLog(\\\"服务已停止\\\")\n"
        + "    }\n"
        + "    \n"
        + "    /// 查询并终止占用3000端口的aimi-binary进程\n"
        + "    private func killProcessesOnPort3000() async {\n"
        + "        addLog(\\\"正在查询占用3000端口的aimi-binary进程...\\\")\n"
        + "        \n"
        + "        do {\n"
        + "            // 使用 lsof 命令查询占用3000端口的进程\n"
        + "            let lsofProcess = Process()\n"
        + "            lsofProcess.executableURL = URL(fileURLWithPath: \\\"/usr/sbin/lsof\\\")\n"
        + "            lsofProcess.arguments = [\\\"-ti\\\", \\\":3000\\\"]\n"
        + "            \n"
        + "            let outputPipe = Pipe()\n"
        + "            lsofProcess.standardOutput = outputPipe\n"
        + "            lsofProcess.standardError = Pipe() // 忽略错误输出\n"
        + "            \n"
        + "            try lsofProcess.run()\n"
        + "            lsofProcess.waitUntilExit()\n"
        + "            \n"
        + "            let outputData = outputPipe.fileHandleForReading.readDataToEndOfFile()\n"
        + "            let output = String(data: outputData, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines) ?? \\\"\\\"\n"
        + "            \n"
        + "            if output.isEmpty {\n"
        + "                addLog(\\\"没有发现占用3000端口的进程\\\")\n"
        + "                return\n"
        + "            }\n"
        + "            \n"
        + "            // 解析PID列表\n"
        + "            let pids = output.components(separatedBy: .newlines)\n"
        + "                .filter { !$0.trimmingCharacters(in: .whitespaces).isEmpty }\n"
        + "                .compactMap { Int32($0.trimmingCharacters(in: .whitespaces)) }\n"
        + "            \n"
        + "            if pids.isEmpty {\n"
        + "                addLog(\\\"没有有效的PID需要检查\\\")\n"
        + "                return\n"
        + "            }\n"
        + "            \n"
        + "            addLog(\\\"发现 \\\\(pids.count) 个占用3000端口的进程，正在检查进程名称...\\\")\n"
        + "            \n"
        + "            // 筛选出aimi-binary进程\n"
        + "            var aimiBinaryPids: [Int32] = []\n"
        + "            \n"
        + "            for pid in pids {\n"
        + "                if await isAimiBinaryProcess(pid: pid) {\n"
        + "                    aimiBinaryPids.append(pid)\n"
        + "                }\n"
        + "            }\n"
        + "            \n"
        + "            if aimiBinaryPids.isEmpty {\n"
        + "                addLog(\\\"没有发现名为aimi-binary的进程占用3000端口\\\")\n"
        + "                return\n"
        + "            }\n"
        + "            \n"
        + "            addLog(\\\"发现 \\\\(aimiBinaryPids.count) 个aimi-binary进程占用3000端口: \\\\(aimiBinaryPids.map(String.init).joined(separator: \\\", \\\"))\\\")\n"
        + "            \n"
        + "            // 逐个终止aimi-binary进程\n"
        + "            for pid in aimiBinaryPids {\n"
        + "                await killProcess(pid: pid)\n"
        + "            }\n"
        + "            \n"
        + "            addLog(\\\"已完成aimi-binary进程清理\\\")\n"
        + "            \n"
        + "        } catch {\n"
        + "            addLog(\\\"查询3000端口进程失败: \\\\(error.localizedDescription)\\\")\n"
        + "        }\n"
        + "    }\n"
        + "    \n"
        + "    /// 检查指定PID是否为aimi-binary进程\n"
        + "    private func isAimiBinaryProcess(pid: Int32) async -> Bool {\n"
        + "        do {\n"
        + "            let psProcess = Process()\n"
        + "            psProcess.executableURL = URL(fileURLWithPath: \\\"/bin/ps\\\")\n"
        + "            psProcess.arguments = [\\\"-p\\\", \\\"\\\\(pid)\\\", \\\"-o\\\", \\\"comm=\\\"]\n"
        + "            \n"
        + "            let outputPipe = Pipe()\n"
        + "            psProcess.standardOutput = outputPipe\n"
        + "            psProcess.standardError = Pipe()\n"
        + "            \n"
        + "            try psProcess.run()\n"
        + "            psProcess.waitUntilExit()\n"
        + "            \n"
        + "            let outputData = outputPipe.fileHandleForReading.readDataToEndOfFile()\n"
        + "            let processName = String(data: outputData, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines) ?? \\\"\\\"\n"
        + "            \n"
        + "            let isAimiBinary = processName.contains(\\\"aimi-binary\\\")\n"
        + "            \n"
        + "            if isAimiBinary {\n"
        + "                addLog(\\\"PID \\\\(pid) 是aimi-binary进程: \\\\(processName)\\\")\n"
        + "            } else {\n"
        + "                addLog(\\\"PID \\\\(pid) 不是aimi-binary进程: \\\\(processName)，跳过\\\")\n"
        + "            }\n"
        + "            \n"
        + "            return isAimiBinary\n"
        + "            \n"
        + "        } catch {\n"
        + "            addLog(\\\"检查进程 \\\\(pid) 名称失败: \\\\(error.localizedDescription)\\\")\n"
        + "            return false\n"
        + "        }\n"
        + "    }\n"
        + "    \n"
        + "    /// 终止指定PID的进程\n"
        + "    private func killProcess(pid: Int32) async {\n"
        + "        addLog(\\\"正在终止进程 PID: \\\\(pid)\\\")\n"
        + "        \n"
        + "        do {\n"
        + "            // 首先尝试获取进程信息\n"
        + "            let psProcess = Process()\n"
        + "            psProcess.executableURL = URL(fileURLWithPath: \\\"/bin/ps\\\")\n"
        + "            psProcess.arguments = [\\\"-p\\\", \\\"\\\\(pid)\\\", \\\"-o\\\", \\\"pid,ppid,comm\\\"]\n"
        + "            \n"
        + "            let psOutputPipe = Pipe()\n"
        + "            psProcess.standardOutput = psOutputPipe\n"
        + "            psProcess.standardError = Pipe()\n"
        + "            \n"
        + "            try psProcess.run()\n"
        + "            psProcess.waitUntilExit()\n"
        + "            \n"
        + "            let psOutputData = psOutputPipe.fileHandleForReading.readDataToEndOfFile()\n"
        + "            let psOutput = String(data: psOutputData, encoding: .utf8) ?? \\\"\\\"\n"
        + "            \n"
        + "            if !psOutput.isEmpty {\n"
        + "                let lines = psOutput.components(separatedBy: .newlines)\n"
        + "                if lines.count > 1 {\n"
        + "                    addLog(\\\"进程信息: \\\\(lines[1].trimmingCharacters(in: .whitespaces))\\\")\n"
        + "                }\n"
        + "            }\n"
        + "            \n"
        + "            // 尝试优雅终止 (SIGTERM)\n"
        + "            let termProcess = Process()\n"
        + "            termProcess.executableURL = URL(fileURLWithPath: \\\"/bin/kill\\\")\n"
        + "            termProcess.arguments = [\\\"-TERM\\\", \\\"\\\\(pid)\\\"]\n"
        + "            \n"
        + "            try termProcess.run()\n"
        + "            termProcess.waitUntilExit()\n"
        + "            \n"
        + "            if termProcess.terminationStatus == 0 {\n"
        + "                addLog(\\\"已发送SIGTERM信号给进程 \\\\(pid)\\\")\n"
        + "                \n"
        + "                // 等待2秒让进程优雅退出\n"
        + "                try await Task.sleep(nanoseconds: 2_000_000_000)\n"
        + "                \n"
        + "                // 检查进程是否还存在\n"
        + "                let checkProcess = Process()\n"
        + "                checkProcess.executableURL = URL(fileURLWithPath: \\\"/bin/kill\\\")\n"
        + "                checkProcess.arguments = [\\\"-0\\\", \\\"\\\\(pid)\\\"]\n"
        + "                \n"
        + "                try checkProcess.run()\n"
        + "                checkProcess.waitUntilExit()\n"
        + "                \n"
        + "                if checkProcess.terminationStatus == 0 {\n"
        + "                    // 进程仍然存在，使用SIGKILL强制终止\n"
        + "                    addLog(\\\"进程 \\\\(pid) 未响应SIGTERM，使用SIGKILL强制终止\\\")\n"
        + "                    \n"
        + "                    let killProcess = Process()\n"
        + "                    killProcess.executableURL = URL(fileURLWithPath: \\\"/bin/kill\\\")\n"
        + "                    killProcess.arguments = [\\\"-KILL\\\", \\\"\\\\(pid)\\\"]\n"
        + "                    \n"
        + "                    try killProcess.run()\n"
        + "                    killProcess.waitUntilExit()\n"
        + "                    \n"
        + "                    if killProcess.terminationStatus == 0 {\n"
        + "                        addLog(\\\"已强制终止进程 \\\\(pid)\\\")\n"
        + "                    } else {\n"
        + "                        addLog(\\\"强制终止进程 \\\\(pid) 失败\\\")\n"
        + "                    }\n"
        + "                } else {\n"
        + "                    addLog(\\\"进程 \\\\(pid) 已退出\\\")\n"
        + "                }\n"
        + "            } else {\n"
        + "                addLog(\\\"发送SIGTERM信号给进程 \\\\(pid) 失败，可能进程已不存在\\\")\n"
        + "            }\n"
        + "            \n"
        + "        } catch {\n"
        + "            addLog(\\\"终止进程 \\\\(pid) 时发生错误: \\\\(error.localizedDescription)\\\")\n"
        + "        }\n"
        + "    }\n"
        + "    \n"
        + "    private func getDarwinArm64Directory() -> URL? {\n"
        + "        // 方法1: 尝试从 Bundle 中获取完整的 node/bin/darwin-arm64 目录结构\n"
        + "        if let nodeURL = Bundle.main.url(forResource: \\\"node\\\", withExtension: nil) {\n"
        + "            let darwinArm64URL = nodeURL\n"
        + "                .appendingPathComponent(\\\"bin\\\")\n"
        + "                .appendingPathComponent(\\\"darwin-arm64\\\")\n"
        + "            \n"
        + "            if FileManager.default.fileExists(atPath: darwinArm64URL.path) {\n"
        + "                addLog(\\\"找到完整的 darwin-arm64 目录: \\\\(darwinArm64URL.path)\\\")\n"
        + "                return darwinArm64URL\n"
        + "            }\n"
        + "        }\n"
        + "        \n"
        + "        // 方法2: 如果文件被扁平化，创建临时目录并复制文件\n"
        + "        addLog(\\\"未找到完整目录结构，尝试从扁平化资源重建目录...\\\")\n"
        + "        return createTemporaryDarwinArm64Directory()\n"
        + "    }\n"
        + "    \n"
        + "    private func createTemporaryDarwinArm64Directory() -> URL? {\n"
        + "        // 创建临时目录\n"
        + "        let tempURL = FileManager.default.temporaryDirectory\n"
        + "            .appendingPathComponent(\\\"aimi-service-\\\\(UUID().uuidString)\\\")\n"
        + "            .appendingPathComponent(\\\"darwin-arm64\\\")\n"
        + "        \n"
        + "        do {\n"
        + "            // 确保目录存在\n"
        + "            try FileManager.default.createDirectory(at: tempURL, withIntermediateDirectories: true, attributes: nil)\n"
        + "            \n"
        + "            // 需要复制的文件列表\n"
        + "            let requiredFiles = [\\\"aimi-binary\\\", \\\"index.node\\\", \\\"rg\\\", \\\"package.json\\\"]\n"
        + "            var copiedFiles: [String] = []\n"
        + "            \n"
        + "            for fileName in requiredFiles {\n"
        + "                if let sourceURL = Bundle.main.url(forResource: fileName, withExtension: nil) {\n"
        + "                    let destURL = tempURL.appendingPathComponent(fileName)\n"
        + "                    try FileManager.default.copyItem(at: sourceURL, to: destURL)\n"
        + "                    copiedFiles.append(fileName)\n"
        + "                    addLog(\\\"已复制文件: \\\\(fileName)\\\")\n"
        + "                } else {\n"
        + "                    addLog(\\\"警告: 未找到文件 \\\\(fileName)\\\")\n"
        + "                }\n"
        + "            }\n"
        + "            \n"
        + "            // 检查是否有 build 目录中的文件\n"
        + "            if let buildNodeURL = Bundle.main.url(forResource: \\\"node_sqlite3.node\\\", withExtension: nil) {\n"
        + "                let buildDir = tempURL.appendingPathComponent(\\\"build\\\").appendingPathComponent(\\\"Release\\\")\n"
        + "                try FileManager.default.createDirectory(at: buildDir, withIntermediateDirectories: true, attributes: nil)\n"
        + "                \n"
        + "                let destURL = buildDir.appendingPathComponent(\\\"node_sqlite3.node\\\")\n"
        + "                try FileManager.default.copyItem(at: buildNodeURL, to: destURL)\n"
        + "                copiedFiles.append(\\\"build/Release/node_sqlite3.node\\\")\n"
        + "                addLog(\\\"已复制文件: build/Release/node_sqlite3.node\\\")\n"
        + "            }\n"
        + "            \n"
        + "            if !copiedFiles.isEmpty {\n"
        + "                addLog(\\\"成功重建 darwin-arm64 目录，包含文件: \\\\(copiedFiles.joined(separator: \\\", \\\"))\\\")\n"
        + "                addLog(\\\"临时目录路径: \\\\(tempURL.path)\\\")\n"
        + "                return tempURL\n"
        + "            } else {\n"
        + "                addLog(\\\"错误: 未能复制任何必需文件\\\")\n"
        + "                return nil\n"
        + "            }\n"
        + "            \n"
        + "        } catch {\n"
        + "            addLog(\\\"创建临时目录失败: \\\\(error)\\\")\n"
        + "            return nil\n"
        + "        }\n"
        + "    }\n"
        + "    \n"
        + "    private func createDefaultEnvFile(at url: URL) throws {\n"
        + "        let envContent = \\\"\\\"\\\"\n"
        + "        # AIMI Binary Environment Configuration\n"
        + "        # 设置服务端口\n"
        + "        PORT=3000\n"
        + "        \n"
        + "        # 设置主机地址\n"
        + "        HOST=127.0.0.1\n"
        + "        \n"
        + "        # 启用调试模式\n"
        + "        DEBUG=true\n"
        + "        \n"
        + "        # 设置日志级别\n"
        + "        LOG_LEVEL=info\n"
        + "        \n"
        + "        # 其他可能需要的环境变量\n"
        + "        NODE_ENV=DEVELOPMENT\n"
        + "        CONTINUE_DEVELOPMENT=true\n"
        + "        \\\"\\\"\\\"\n"
        + "        \n"
        + "        try envContent.write(to: url, atomically: true, encoding: .utf8)\n"
        + "        addLog(\\\"已创建默认 .env 文件\\\")\n"
        + "    }\n"
        + "    \n"
        + "    private func startStatusMonitoring() {\n"
        + "        // 移除自动轮询监听，避免UI卡顿\n"
        + "        // 改为手动刷新方式\n"
        + "        addLog(\\\"状态监控已启用，请使用手动刷新按钮更新状态\\\")\n"
        + "    }\n"
        + "    \n"
        + "    private func stopStatusMonitoring() {\n"
        + "        statusCheckTimer?.invalidate()\n"
        + "        statusCheckTimer = nil\n"
        + "    }\n"
        + "    \n"
        + "    /// 手动刷新服务状态\n"
        + "    public func refreshServiceStatus() async {\n"
        + "        addLog(\\\"正在手动刷新服务状态...\\\")\n"
        + "        \n"
        + "        guard let process = process else {\n"
        + "            if case .running = status {\n"
        + "                status = .stopped\n"
        + "                addLog(\\\"进程丢失，状态更新为已停止\\\")\n"
        + "            } else {\n"
        + "                addLog(\\\"当前状态: \\\\(statusDescription)\\\")\n"
        + "            }\n"
        + "            return\n"
        + "        }\n"
        + "        \n"
        + "        // 使用非阻塞方式检查进程状态\n"
        + "        if process.isRunning {\n"
        + "            addLog(\\\"服务正在运行，PID: \\\\(process.processIdentifier)\\\")\n"
        + "            if case .running(let currentPid) = status, currentPid != process.processIdentifier {\n"
        + "                status = .running(pid: process.processIdentifier)\n"
        + "                addLog(\\\"更新PID: \\\\(process.processIdentifier)\\\")\n"
        + "            }\n"
        + "        } else {\n"
        + "            addLog(\\\"进程已停止运行\\\")\n"
        + "            status = .stopped\n"
        + "            self.process = nil\n"
        + "            stopStatusMonitoring()\n"
        + "        }\n"
        + "    }\n"
        + "    \n"
        + "    /// 获取状态描述\n"
        + "    private var statusDescription: String {\n"
        + "        switch status {\n"
        + "        case .stopped:\n"
        + "            return \\\"已停止\\\"\n"
        + "        case .starting:\n"
        + "            return \\\"启动中...\\\"\n"
        + "        case .running(let pid):\n"
        + "            return \\\"运行中 (PID: \\\\(pid))\\\"\n"
        + "        case .stopping:\n"
        + "            return \\\"停止中...\\\"\n"
        + "        case .error(let message):\n"
        + "            return \\\"错误: \\\\(message)\\\"\n"
        + "        }\n"
        + "    }\n"
        + "    \n"
        + "    private func checkServiceStatus() {\n"
        + "        // 保留原方法以防其他地方调用，但不再使用定时器\n"
        + "        guard let process = process else {\n"
        + "            if case .running = status {\n"
        + "                status = .stopped\n"
        + "                addLog(\\\"进程丢失，状态更新为已停止\\\")\n"
        + "            }\n"
        + "            return\n"
        + "        }\n"
        + "        \n"
        + "        if !process.isRunning {\n"
        + "            status = .stopped\n"
        + "            self.process = nil\n"
        + "            stopStatusMonitoring()\n"
        + "            addLog(\\\"进程已停止运行\\\")\n"
        + "        }\n"
        + "    }\n"
        + "    \n"
        + "    private func addLog(_ message: String) {\n"
        + "        let timestamp = DateFormatter.logFormatter.string(from: Date())\n"
        + "        let logMessage = \\\"[\\\\(timestamp)] \\\\(message)\\\"\n"
        + "        \n"
        + "        logs.append(logMessage)\n"
        + "        \n"
        + "        // 限制日志行数\n"
        + "        if logs.count > maxLogLines {\n"
        + "            logs.removeFirst(logs.count - maxLogLines)\n"
        + "        }\n"
        + "        \n"
        + "        // 同时输出到系统日志\n"
        + "        Logger.service.info(\\\"\\\\(logMessage)\\\")\n"
        + "    }\n"
        + "    \n"
        + "    public func clearLogs() {\n"
        + "        logs.removeAll()\n"
        + "    }\n"
        + "    \n"
        + "    deinit {\n"
        + "        statusCheckTimer?.invalidate()\n"
        + "        statusCheckTimer = nil\n"
        + "    }\n"
        + "}\n"
        + "\n"
        + "private extension DateFormatter {\n"
        + "    static let logFormatter: DateFormatter = {\n"
        + "        let formatter = DateFormatter()\n"
        + "        formatter.dateFormat = \\\"HH:mm:ss\\\"\n"
        + "        return formatter\n"
        + "    }()\n"
        + "}\n"
        + "\n"
        + "private enum ServiceError: LocalizedError {\n"
        + "    case resourceNotFound(String)\n"
        + "    case processError(String)\n"
        + "    \n"
        + "    var errorDescription: String? {\n"
        + "        switch self {\n"
        + "        case .resourceNotFound(let message):\n"
        + "            return \\\"资源未找到: \\\\(message)\\\"\n"
        + "        case .processError(let message):\n"
        + "            return \\\"进程错误: \\\\(message)\\\"\n"
        + "        }\n"
        + "    }\n"
        + "} \n"
        + "\n"
        + "```\n"
        + "\n";

    @Test
    public void selectAgentTest() {
        //for (int i = 0; i < 10; i++) {
            AimiGroupChatManager aimiGroupChatManager = AimiGroupChatManager.builder().build();
            SessionContext sessionContext = new SessionContext();
            sessionContext.setChatHistory(Lists.newArrayList(new ChatMessageContent<>(AuthorRole.USER, content)));
            ConversableAgent conversableAgent = aimiGroupChatManager.selectAgent(SessionChatPlatform.WEB, sessionContext);
            System.out.println(content + "\n" + conversableAgent.getIdentifier());
        //}
    }

    @Test
    public void selectAgentTestMap() {
        contentMap.forEach((key, value) -> {
            value.forEach(content -> {
                AimiGroupChatManager aimiGroupChatManager = AimiGroupChatManager.builder().build();
                SessionContext sessionContext = new SessionContext();
                sessionContext.setChatHistory(Lists.newArrayList(new ChatMessageContent<>(AuthorRole.USER, content)));
                ConversableAgent conversableAgent = aimiGroupChatManager.selectAgent(SessionChatPlatform.WEB, sessionContext);
                Assert.assertEquals( content + "," +conversableAgent.getIdentifier() + "," + key, conversableAgent.getIdentifier(), key);
            });
        });
    }

    //@Test
    //public void runChatTest() {
    //    AimiGroupChatManager aimiGroupChatManager = AimiGroupChatManager.builder().build();
    //
    //    AgentsManager agentsManager = new AgentsManager();
    //    ConversableAgent userAgent = agentsManager.mockUserProxyAgent();
    //    userAgent.setSessionId("123");
    //
    //    ChatMessageContent<?> userMessage = new ChatMessageContent<>(AuthorRole.USER, content);
    //    aimiGroupChatManager.runChat(userAgent, Lists.newArrayList(userMessage));
    //}

    @Test
    public void runChatStreamingTest() {
        AimiGroupChatManager aimiGroupChatManager = AimiGroupChatManager.builder().build();

        AgentsManager agentsManager = new AgentsManager();
        ConversableAgent userAgent = agentsManager.mockUserProxyAgent();
        userAgent.setSessionId("123");

        SseEmitter sseEmitter = new SseEmitter();

        SessionContext sessionContext = new SessionContext();
        sessionContext.setSseEmitter(sseEmitter);
        sessionContext.setSessionId("123");
        sessionContext.setChatHistory(Lists.newArrayList(new ChatMessageContent<>(AuthorRole.USER, content)));
        //https://pre-aimi.alibaba-inc.com/swagger-ui/index.html#/Agent%E6%8E%A5%E5%8F%A3/queryAgentSsoTicket
        sessionContext.setSsoTicket("bucf047154e53fe4a578a06836a571000a060b00");

        List<ChatMessageContent<?>> messageContents = aimiGroupChatManager.generateStreamingReply(userAgent, sessionContext);
        System.out.println(prettyFormat(JSON.toJSONString(messageContents)));
    }

    @Test
    public void runSingleChatStreamingTest() {
        AimiGroupChatManager aimiGroupChatManager = AimiGroupChatManager.builder().build();

        AgentsManager agentsManager = new AgentsManager();
        ConversableAgent userAgent = agentsManager.mockUserProxyAgent();
        userAgent.setSessionId("123");

        SseEmitter sseEmitter = new SseEmitter();

        SessionContext sessionContext = new SessionContext();
        sessionContext.setSseEmitter(sseEmitter);
        sessionContext.setSessionId("123");
        sessionContext.setChatHistory(Lists.newArrayList(new ChatMessageContent<>(AuthorRole.USER, content)));
        //https://pre-aimi.alibaba-inc.com/swagger-ui/index.html#/Agent%E6%8E%A5%E5%8F%A3/queryAgentSsoTicket
        sessionContext.setSsoTicket("buc6ae04753ecac42f1a006833d52f000a060b00");

        List<ChatMessageContent<?>> messageContents = aimiGroupChatManager.getAgentList().get(2).generateStreamingReply(userAgent, sessionContext);
        System.out.println(prettyFormat(JSON.toJSONString(messageContents)));
    }

    @Test
    public void runQaAgent() {
        ConversableAgent agent = SimpleAssistantAgent.builder()
                .name("knowledge_agent")
                .identifier("knowledge-agent")
                .modelId(ModelEnum.AIMI_CHAT_72B.getModelId())
                .description("终端文档知识相关问题的 Agent，例如SDK使用文档、SDK最佳实践等相关问题")
                .build();
        String question = "orange说明文档在哪里";

        SessionContext sessionContext = new SessionContext();
        sessionContext.setSessionId("123");
        sessionContext.setChatHistory(Lists.newArrayList(new ChatMessageContent<>(AuthorRole.USER, content)));

        List<ChatMessageContent<?>> messageContents = agent.generateStreamingReply(null, sessionContext);
        log.info(prettyFormat(JSON.toJSONString(messageContents)));
    }


}
