package com.alibaba.emas.mtl4.services.ai.agents.serve.config.kernel;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * function type.
 *
 * <AUTHOR>
 * @date 2024/7/11
 */
@Getter
@AllArgsConstructor
public enum KernelFunctionType {

    PROMPT("PROMPT", "prompt"),

    METHOD("METHOD", "method"),

    ;

    /**
     * function type.
     */
    private final String type;

    /**
     * function type description.
     */
    private final String description;

}
