package com.alibaba.emas.mtl4.services.ai.agents.serve.config.converter;

import cn.hutool.core.lang.Pair;
import com.alibaba.emas.agent.ext.mcp.model.BaseMcpServerConfig;
import com.alibaba.emas.agent.ext.mcp.utils.McpFactory;
import com.alibaba.emas.agent.role.utils.Constants;
import com.alibaba.emas.mtl4.commons.utils.StringKit;
import com.alibaba.emas.mtl4.services.ai.agents.serve.config.agent.BaseAgentConfig;
import com.alibaba.emas.mtl4.services.ai.agents.serve.util.ConfigHelper;
import com.alibaba.emas.mtl4.services.ai.api.model.AgentInstanceBO;
import com.github.jknack.handlebars.Context;
import com.github.jknack.handlebars.Handlebars;
import com.github.jknack.handlebars.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * abstract agent config converter.
 *
 * <AUTHOR>
 * @date 2024/7/22
 */
@Slf4j
public abstract class AbstractAgentConfigConverter<T extends BaseAgentConfig> implements AgentConfigConverter<T> {

    @Override
    public T convert(AgentInstanceBO agentInstanceBO) {
        // 调用子类实现的构建器方法
        T config = buildConfig(agentInstanceBO);
        // 设置基础属性
        setBaseProperties(config, agentInstanceBO);
        // 设置特定属性
        setSpecificProperties(config, agentInstanceBO);
        return config;
    }

    /**
     * 创建配置对象
     *
     * @param agentInstanceBO agent instance bo
     * @return config builder
     */
    protected abstract T buildConfig(AgentInstanceBO agentInstanceBO);

    /**
     * 设置特定属性
     *
     * @param config 配置对象
     * @param agentInstanceBO agent instance bo
     */
    protected abstract void setSpecificProperties(T config, AgentInstanceBO agentInstanceBO);

    /**
     * 设置基础属性
     *
     * @param config 配置对象
     * @param agentInstanceBO agent instance bo
     */
    protected void setBaseProperties(T config, AgentInstanceBO agentInstanceBO) {
        config.setName(agentInstanceBO.getName());
        config.setIdentifier(agentInstanceBO.getIdentifier());
        config.setDescription(agentInstanceBO.getDescription());

        String promptTemplate = agentInstanceBO.getPromptTemplate();
        promptTemplate = promptTemplate.replaceAll(Constants.VAR_REPLACE_REGEX, Constants.VAR_REPLACE_REGEX_RESULT);
        promptTemplate = promptTemplate.replaceAll("\\{\\{}}", "\\\\{{}}");

        // 根据用户设置，传入 message template
        if (StringUtils.isNotBlank(promptTemplate)) {
            String messageTemplate = StringKit.format(Constants.CUSTOM_SYSTEM_MESSAGE_TEMPLATE, promptTemplate);
            config.setChatFunction(ConfigHelper.createFromText(messageTemplate));
        } else {
            config.setChatFunction(ConfigHelper.createFromText(Constants.DEFAULT_PROMPT_TEMPLATE));
        }

        if (agentInstanceBO.getPromptExecutionSettings() != null) {
            config.setExecuteSettings(agentInstanceBO.getPromptExecutionSettings());
            config.setKernel(ConfigHelper.createKernelConfig(agentInstanceBO.getPromptExecutionSettings().getModelId()));
        }
        if (CollectionUtils.isNotEmpty(agentInstanceBO.getMcpServerMetas())) {
            List<BaseMcpServerConfig> mcpServerConfigs = agentInstanceBO.getMcpServerMetas()
                    .stream()
                    .map(agentInstanceMcpServer -> {
                        Pair<String, String> baseUrlAndEndpoint = McpFactory.getBaseUrlAndEndpoint(
                                agentInstanceMcpServer.getSseUrl());
                        return McpFactory.createSseConfig(agentInstanceMcpServer.getName(),
                                agentInstanceMcpServer.getDescription(), baseUrlAndEndpoint.getKey(), baseUrlAndEndpoint.getValue());
                    })
                    .collect(Collectors.toList());
            config.setMcpServerConfigs(mcpServerConfigs);
        }
    }

} 