package com.alibaba.emas.mtl4.services.ai.agents.serve.flow.config.analyzer.impl.agent;

import com.alibaba.emas.agent.base.Kernel;
import com.alibaba.emas.agent.base.orchestration.PromptExecutionSettings;
import com.alibaba.emas.agent.base.semanticfunctions.KernelFunction;
import com.alibaba.emas.agent.role.agents.ConversableAgent;
import com.alibaba.emas.agent.role.utils.PromptUtils;
import com.alibaba.emas.mtl4.services.ai.agents.serve.flow.config.analyzer.ConfigResolver;
import com.alibaba.emas.mtl4.services.ai.agents.serve.flow.config.model.NodeInputConfig;
import com.alibaba.emas.mtl4.services.ai.agents.serve.flow.config.model.ConfigNodeType;
import com.alibaba.emas.mtl4.services.ai.agents.serve.config.agent.BaseAgentConfig;
import com.alibaba.emas.mtl4.services.ai.agents.serve.config.prompt.Prompt;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * agent node resolver.
 *
 * <AUTHOR>
 * @date 2024/7/12
 */
@Slf4j
@Component
public class ConversableAgentNodeResolver extends ConfigResolver<BaseAgentConfig> {

    @Override
    public boolean accept(NodeInputConfig<BaseAgentConfig> node) {
        return Objects.equals(ConfigNodeType.AGENT.getType(), node.getType());
    }

    @Override
    public Object resolve(NodeInputConfig<BaseAgentConfig> node) {
        BaseAgentConfig agentConfig = JSON.parseObject(JSON.toJSONString(node.getSource()), BaseAgentConfig.class);
        Kernel kernel = kernelManager.createKernel(agentConfig.getKernel());
        String summaryPrompt = Optional.ofNullable(promptManager.createFromConfig(agentConfig.getDefaultSummaryPromptConfig()))
                .map(Prompt::getPromptTemplate)
                .orElse(null);
        KernelFunction<?> kernelFunction = promptManager.createFunctionFromConfig(agentConfig.getChatFunction());
        PromptExecutionSettings promptExecutionSettings = PromptUtils.getPromptExecutionSettings(JSON.toJSONString(agentConfig.getExecuteSettings()));
        ConversableAgent agent = new ConversableAgent();
        agent.setIdentifier(agentConfig.getIdentifier());
        agent.setName(agentConfig.getName());
        agent.setSystemMessage(agentConfig.getSystemMessage());
        agent.setMaxConsecutiveAutoReply(agentConfig.getMaxConsecutiveAutoReply());
        agent.setKernel(kernel);
        agent.setChatFunction(kernelFunction);
        agent.setDefaultSummaryPrompt(summaryPrompt);
        agent.setPromptExecutionSettings(promptExecutionSettings);
        return agent;
    }

}
