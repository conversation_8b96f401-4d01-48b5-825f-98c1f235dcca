package com.alibaba.emas.mtl4.services.ai.agents.serve.orchestrators.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * agent request message.
 *
 * <AUTHOR>
 * @date 2024/8/5
 */
@Getter
@AllArgsConstructor
public enum AgentRequestMessageType {

    SIMPLE_MESSAGE("simple", "simple"),

    OBJECT_MESSAGE("object", "object")

    ;

    private final String type;

    private final String description;

}
