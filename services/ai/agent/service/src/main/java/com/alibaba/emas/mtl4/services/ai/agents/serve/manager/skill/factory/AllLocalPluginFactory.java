package com.alibaba.emas.mtl4.services.ai.agents.serve.manager.skill.factory;

import com.alibaba.emas.agent.base.plugin.KernelPlugin;
import com.alibaba.emas.mtl4.services.ai.agents.serve.manager.plugin.impl.LocalPluginManager;
import com.alibaba.emas.mtl4.services.ai.agents.serve.config.tool.AllLocalPluginConfig;
import com.alibaba.emas.mtl4.services.ai.agents.serve.config.tool.SkillConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * all local plugin factory.
 *
 * <AUTHOR>
 * @date 2024/7/12
 */
@Slf4j
@Component
public class AllLocalPluginFactory implements SkillFactory {

    @Resource
    private LocalPluginManager localPluginManager;

    @Override
    public boolean support(SkillConfig skillConfig) {
        return skillConfig instanceof AllLocalPluginConfig;
    }

    @Override
    public List<KernelPlugin> createSkill(SkillConfig skillConfig) {
        return localPluginManager.getAllPlugins();
    }

}
