package com.alibaba.emas.mtl4.services.ai.agents.serve.config.tool;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * open api plugin config.
 *
 * <AUTHOR>
 * @date 2024/8/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OpenApiPluginConfig extends SkillConfig {

    /**
     * plugin name.
     */
    private String pluginName;

    /**
     * plugin schema.
     */
    private String schemaContent;

    /**
     * plugin schema url.
     */
    private String schemaUrl;

    /**
     * server url.
     */
    private String serverUrl;

    /**
     * headers map.
     */
    private Map<String, String> headersMap;

}
