package com.alibaba.emas.mtl4.services.ai.agents.serve.orchestrators.service.impl;

import com.alibaba.emas.agent.base.semanticfunctions.KernelFunction;
import com.alibaba.emas.agent.base.services.chatcompletion.ChatMessageContent;
import com.alibaba.emas.agent.ext.kernel.model.ModelEnum;
import com.alibaba.emas.agent.role.agents.Agent;
import com.alibaba.emas.agent.role.agents.ConversableAgent;
import com.alibaba.emas.agent.role.agents.SimpleAssistantAgent;
import com.alibaba.emas.mtl4.services.ai.agents.serve.manager.kernel.KernelFunctionManager;
import com.alibaba.emas.mtl4.services.ai.agents.serve.orchestrators.model.AgentHandlerBO;
import com.alibaba.emas.mtl4.services.ai.agents.serve.orchestrators.service.AbstractBaseOrchestrator;
import com.alibaba.emas.mtl4.services.ai.agents.serve.util.AgentRequestMessageHelper;
import com.alibaba.emas.mtl4.services.ai.agents.serve.util.ConfigHelper;
import com.alibaba.emas.mtl4.services.ai.api.enums.AgentThoughtMode;
import com.alibaba.emas.mtl4.services.ai.api.enums.PromptTemplateEnum;
import com.alibaba.emas.mtl4.services.ai.api.model.AgentInfo;
import com.alibaba.emas.mtl4.services.ai.api.model.AgentTaskRequest;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * agent分发协调器.
 *
 * <AUTHOR>
 * @since 2025/3/24
 */
@Slf4j
@Component
public class AgentDistributionOrchestrator extends AbstractBaseOrchestrator {

    @Resource
    private KernelFunctionManager kernelFunctionManager;

    /**
     * 分类器.
     */
    private ConversableAgent classifier;

    /**
     * agent列表.
     */
    private List<ConversableAgent> agentList = new ArrayList<>();

    private static final Pattern IDENTIFIER_PATTERN;

    static {
        String regex = "Identifier：\\s*([^\\s]+)";
        IDENTIFIER_PATTERN = Pattern.compile(regex);
    }

    @PostConstruct
    public void init() {
        KernelFunction<?> kernelFunction = kernelFunctionManager.createFromConfig(ConfigHelper.createDbPromptFunction(
                PromptTemplateEnum.AGENT_CLASSIFIER.getIdentifier()));
        classifier = SimpleAssistantAgent.builder()
                .modelId(ModelEnum.getLatestQwenModelId())
                .chatFunction(kernelFunction)
                .name("classifier-1")
                .build();
        // 简单模拟几个
        ConversableAgent agent1 = SimpleAssistantAgent.builder()
                .name("agent1")
                .modelId(ModelEnum.getLatestQwenModelId())
                .description("可以解决代码问答问题的 agent")
                .build();
        ConversableAgent agent2 = SimpleAssistantAgent.builder()
                .name("agent2")
                .modelId(ModelEnum.getLatestQwenModelId())
                .description("可以解决bug问题的 agent")
                .build();
        addAgent(agent1);
        addAgent(agent2);
    }

    public void addAgent(ConversableAgent agent) {
        agentList.add(agent);
    }

    @Override
    public Integer priority() {
        return -1;
    }

    @Override
    public boolean acceptTask(AgentTaskRequest<?> taskRequest) {
        return CollectionUtils.isNotEmpty(agentList);
    }

    @Override
    public AgentHandlerBO getTaskHandler(AgentTaskRequest<?> taskRequest) {
        List<AgentInfo> agentInfoList = this.agentList
                .stream()
                .map(Agent::getAgentInfo)
                .toList();
        Map<String, Object> params = new HashMap<>();
        params.put("agents", JSON.parseArray(JSON.toJSONString(agentList)));
        params.put("question", taskRequest.getParams().toString());

        List<ChatMessageContent<?>> result = classifier.generateReply(null, params);
        String content = result.get(0).getContent();
        Assert.notNull(content, "content is null");
        Matcher matcher = IDENTIFIER_PATTERN.matcher(content);

        // 查找匹配并提取结果
        ConversableAgent agent = null;
        if (matcher.find()) {
            String identifier = matcher.group(1);
            agent = agentInfoList.stream()
                    .filter(agentInfo -> agentInfo.getIdentifier().equals(identifier))
                    .findFirst()
                    .map(AgentInfo::getIdentifier)
                    .map(agentsManagerV2::getAgentByIdentifier)
                    .orElse(null);
        }
        if (Objects.isNull(agent)) {
            agent = this.agentList.get(0);
        }
        return AgentHandlerBO.builder()
                .handler(agent)
                .agentThoughtMode(AgentThoughtMode.SIMPLE_AGENT)
                .taskMessages(AgentRequestMessageHelper.createSimpleChatMessageContents(taskRequest))
                .build();
    }

}
