package com.alibaba.emas.mtl4.services.ai.agents.serve.flow.config.analyzer;

import com.alibaba.emas.mtl4.services.ai.agents.serve.flow.config.model.NodeInputConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * node resolver manager.
 *
 * <AUTHOR>
 * @date 2024/7/13
 */
@Slf4j
@Component
public class ConfigResolverManager {

    @Autowired
    private List<ConfigResolver<?>> configResolvers;

    /**
     * resolve node config.
     *
     * @param node the node
     * @return the object
     */
    public Object resolveNodeConfig(NodeInputConfig<?> node) {
        return configResolvers.stream()
                .filter(resolver -> resolver.accept((NodeInputConfig) node))
                .findFirst()
                .map(resolver -> resolver.resolve((NodeInputConfig) node))
                .orElse(null);
    }

}
