package com.alibaba.emas.agent.tools.code;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.alibaba.emas.mtl4.commons.utils.httpclient.ClientConfig;
import com.alibaba.emas.mtl4.commons.utils.httpclient.HttpMethod;
import com.alibaba.emas.mtl4.commons.utils.httpclient.OkHttpServiceClient;
import com.alibaba.emas.mtl4.commons.utils.httpclient.RequestContext;
import com.alibaba.emas.mtl4.commons.utils.httpclient.RequestMessage;
import com.alibaba.emas.mtl4.commons.utils.httpclient.ResponseMessage;
import com.alibaba.fastjson.JSONObject;

import com.google.common.util.concurrent.RateLimiter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpStatus;

@Slf4j
public class CoverageManager {
    private static OkHttpServiceClient okHttpServiceClient = new OkHttpServiceClient(new ClientConfig());
    private static RateLimiter COVERAGE_RATE_LIMITER = RateLimiter.create(5);

    public static void main(String[] args) {
        //System.out.println(CoverageManager.getTopMetaIdsFromCTM(6L));
        //System.out.println(CoverageManager.getMetaIdFromCTM("10.37.20"));
        System.out.println(CoverageManager.getUnreachedClassFromCTM(CoverageManager.getTopMetaIdsFromCTM(1L), "com.taobao.android:layoutmanager-taobao@aar"));
    }

    public static String getTopMetaIdsFromCTM(Long appId) {
        String app;
        String platform;
        if (1L == appId) {
            app = "com.taobao.taobao";
            platform = "android";
        } else if (6L == appId) {
            app = "com.taobao.taobao4iphone";
            platform = "ios";
        } else {
            throw new RuntimeException("invalid appId " + appId);
        }

        String url = String.format("https://ctm.alibaba-inc.com/api/v2/app/manager?appId=%s&platform=%s", app, platform) ;
        int count = 0;
        while (count++ < 10) {
            try {
                if (count > 1) {
                    Thread.sleep(1000);
                }
                COVERAGE_RATE_LIMITER.acquire();

                RequestMessage requestMessage = RequestMessage.builder()
                    .url(url)
                    .method(HttpMethod.GET)
                    .build();
                ResponseMessage responseMessage = okHttpServiceClient.execute(requestMessage, new RequestContext());
                if (HttpStatus.SC_OK == responseMessage.getStatusCode()) {
                    JSONObject resJO = JSONObject.parseObject(responseMessage.getJson());
                    JSONObject dataJO = resJO.getJSONObject("data");
                    List<AppCoverageVersion> versions = dataJO.getJSONArray("versions")
                        .toJavaList(AppCoverageVersion.class);
                    Collections.reverse(versions);
                    return versions.stream()
                        .limit(5)
                        .map(AppCoverageVersion::getMetaId)
                        .collect(Collectors.joining(","));
                }
            } catch (Exception e) {
                log.error("第{}次请求ctm失败", count, e);
            }
        }
        throw new RuntimeException("getMetaIdFromCTM error.");
    }

    public static String getMetaIdFromCTM(Long appId, String version) {
        String app;
        String platform;
        if (1L == appId) {
            app = "com.taobao.taobao";
            platform = "android";
        } else if (6L == appId) {
            app = "com.taobao.taobao4iphone";
            platform = "ios";
        } else {
            throw new RuntimeException("invalid appId " + appId);
        }

        String url = String.format("https://ctm.alibaba-inc.com/api/v2/app/manager?appId=%s&platform=%s", app, platform) ;
        int count = 0;
        while (count++ < 10) {
            try {
                if (count > 1) {
                    Thread.sleep(1000);
                }
                COVERAGE_RATE_LIMITER.acquire();

                RequestMessage requestMessage = RequestMessage.builder()
                    .url(url)
                    .method(HttpMethod.GET)
                    .build();
                ResponseMessage responseMessage = okHttpServiceClient.execute(requestMessage, new RequestContext());
                if (HttpStatus.SC_OK == responseMessage.getStatusCode()) {
                    JSONObject resJO = JSONObject.parseObject(responseMessage.getJson());
                    JSONObject dataJO = resJO.getJSONObject("data");
                    List<AppCoverageVersion> versions = dataJO.getJSONArray("versions")
                        .toJavaList(AppCoverageVersion.class);
                    return versions.stream()
                        .filter(v -> v.getVersion().equals(version))
                        .findFirst()
                        .map(AppCoverageVersion::getMetaId)
                        .orElse(null);
                }
            } catch (Exception e) {
                log.error("第{}次请求ctm失败", count, e);
            }
        }
        throw new RuntimeException("getMetaIdFromCTM error.");
    }

    public static List<ClassCoverage> getUnreachedClassFromCTM(String metaId, String gat) {
        String url = String.format("https://ctm.alibaba-inc.com/api/v2/coverage/classInfoList/multiVersions?metaIds=%s&bundleName=%s",
            metaId, gat);

        int count = 0;
        while (count++ < 10) {
            try {
                if (count > 1) {
                    Thread.sleep(1000);
                }
                COVERAGE_RATE_LIMITER.acquire();
                RequestMessage requestMessage = RequestMessage.builder()
                    .url(url)
                    .method(HttpMethod.GET)
                    .build();
                ResponseMessage responseMessage = okHttpServiceClient.execute(requestMessage, new RequestContext());
                if (HttpStatus.SC_OK == responseMessage.getStatusCode()) {
                    JSONObject resJO = JSONObject.parseObject(responseMessage.getJson());
                    if (null != resJO) {
                        JSONObject dataJO = resJO.getJSONObject("data");
                        if (null != dataJO && dataJO.containsKey("classInfoList")) {
                            return dataJO.getJSONArray("classInfoList").toJavaList(ClassCoverage.class)
                                .stream()
                                .filter(classCoverage -> classCoverage.getHitCount().equals(0L))
                                .toList();
                        } else {
                            log.info(">>>getUnreachedClassFromCTM empty:{},{}", gat, responseMessage.getJson());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("第{}次请求ctm失败", count, e);
            }
        }
        throw new RuntimeException("getUnreachedClassFromCTM error.");
    }

    @Data
    public static class ClassCoverage {
        private String className;
        private Long hitCount;
    }

    @Data
    public static class AppCoverageVersion {
        private String version;
        private Date gmtCreate;
        private String metaId;
    }

}
