package com.alibaba.emas.mtl4.services.ai.agent.model.code;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.emas.mtl4.commons.utils.ConversionUtils;
import com.alibaba.emas.mtl4.services.ai.api.model.SlimmingMethodDTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocstringMetaData {
    private String documentId;
    private String module;
    private String repo;
    private String tag;
    private String language;
    private String type;
    private String path;
    private String className;
    private String method;
    private String parameters;
    private String docstring;
    private Integer startRow;
    private Integer endRow;
    private List<Float> vector;
    private Double score;

    public DocstringMetaData(Map<String, Object> map) {
        this.module = ConversionUtils.toString(map.get("module"));
        this.repo = ConversionUtils.toString(map.get("repo"));
        this.tag = ConversionUtils.toString(map.get("tag"));
        this.language = ConversionUtils.toString(map.get("language"));
        this.type = ConversionUtils.toString(map.get("type"));
        this.path = ConversionUtils.toString(map.get("path"));
        this.method = ConversionUtils.toString(map.get("method"));
        this.parameters = ConversionUtils.toString(map.get("parameters"));
        this.className = ConversionUtils.toString(map.get("className"));
        this.startRow = ConversionUtils.toInteger(map.get("startRow"));
        this.endRow = ConversionUtils.toInteger(map.get("endRow"));
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("module", module);
        map.put("repo", repo);
        map.put("tag", tag);
        map.put("language", language);
        map.put("type", type);
        map.put("path", path);
        map.put("method", method);
        map.put("parameters", parameters);
        map.put("className", className);
        map.put("startRow", startRow);
        map.put("endRow", endRow);
        return map;
    }

    public SlimmingMethodDTO toSlimmingMethodDTO() {
        SlimmingMethodDTO dto = new SlimmingMethodDTO();
        dto.setRepo(repo);
        dto.setTag(tag);
        dto.setPath(path);
        dto.setMethodName(method);
        dto.setLanguage(language);
        dto.setMethodParameters(parameters);
        dto.setMethodDocstring(docstring);
        dto.setSimilarity(score);
        dto.setClassName(className);
        dto.setStartRow(startRow);
        dto.setEndRow(endRow);
        dto.setRows(endRow - startRow + 1);
        return dto;
    }

    public Map<String, Object> toMapWithoutNull() {
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isNotBlank(module)) {
            map.put("module", module);
        }
        if (StringUtils.isNotBlank(repo)) {
            map.put("repo", repo);
        }
        if (StringUtils.isNotBlank(tag)) {
            map.put("tag", tag);
        }
        if (StringUtils.isNotBlank(language)) {
            map.put("language", language);
        }
        if (StringUtils.isNotBlank(type)) {
            map.put("type", type);
        }
        if (StringUtils.isNotBlank(path)) {
            map.put("path", path);
        }
        if (StringUtils.isNotBlank(method)) {
            map.put("method", method);
        }
        if (StringUtils.isNotBlank(parameters)) {
            map.put("parameters", parameters);
        }
        if (StringUtils.isNotBlank(className)) {
            map.put("className", className);
        }
        if (null != startRow) {
            map.put("startRow", startRow);
        }
        if (null != endRow) {
            map.put("endRow", endRow);
        }
        return map;
    }
}
