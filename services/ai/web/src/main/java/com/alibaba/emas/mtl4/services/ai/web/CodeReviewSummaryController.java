package com.alibaba.emas.mtl4.services.ai.web;

import com.alibaba.emas.mtl4.services.ai.api.model.CodeReviewResultRepositoryDTO;
import com.alibaba.emas.mtl4.services.ai.service.codeReviewSummary.service.CodeReviewSummaryServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 代码评审总结数据库CRUD接口
 *
 * <AUTHOR>
 * @date 2024/08/09
 */
@Api
@Slf4j
@RestController
@RequestMapping("/code_review_summary_crud")
public class CodeReviewSummaryController {
    @Resource
    private CodeReviewSummaryServiceImpl codeReviewSummaryService;

    @GetMapping("/get_ai_cr_code_review_summary_by_id")
    @ApiOperation("获取MTL AI CR某一条代码评审总结记录")
    public CodeReviewResultRepositoryDTO getCodeReviewSummaryById(Long id) {
        return codeReviewSummaryService.getCodeReviewSummaryById(id);
    }

    @GetMapping("/get_ai_cr_code_review_summary_by_mr_id_and_project_id")
    @ApiOperation("获取MTL AI CR某一条代码评审总结记录")
    public CodeReviewResultRepositoryDTO getCodeReviewSummaryByMrIdAndProjectId(Integer projectId, Integer mergeRequestId) {
        return codeReviewSummaryService.getCodeReviewSummary(projectId, mergeRequestId);
    }

    @PostMapping("/create_ai_cr_code_review_summary")
    @ApiOperation("创建MTL AI CR代码评审总结记录")
    public Long createCodeReviewSummary(CodeReviewResultRepositoryDTO codeReviewResultRepositoryDTO) {
        return codeReviewSummaryService.createCodeReviewSummary(codeReviewResultRepositoryDTO);
    }

    @PostMapping("/update_ai_cr_code_review_summary")
    @ApiOperation("更新MTL AI CR代码评审总结记录")
    public void updateCodeReviewSummary(Long id, CodeReviewResultRepositoryDTO codeReviewResultRepositoryDTO) {
        codeReviewSummaryService.updateCodeReviewSummary(id, codeReviewResultRepositoryDTO);
    }

    @PostMapping("/delete_ai_cr_code_review_summary")
    @ApiOperation("删除MTL AI CR代码评审总结记录")
    public void deleteCodeReviewSummary(Long id) {
        codeReviewSummaryService.deleteCodeReviewSummary(id);
    }
}
