package com.alibaba.emas.mtl4.services.ai.web.dto;

import java.util.Date;

import com.alibaba.emas.mtl4.services.ai.api.enums.AgentTaskStatus;

import lombok.Data;

@Data
public class AgentTaskDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 任务目标
     */
    private String goal;

    /**
     * 任务状态
     */
    private AgentTaskStatus status;

    /**
     * 场景类型
     */
    private String sceneType;

    /**
     * 场景实体id
     */
    private Long sceneEntityId;

    /**
     * 计划信息
     */
    private String planInfo;

    /**
     * 任务配置
     */
    private String taskConfig;

    /**
     * 最终结果
     */
    private String finalResult;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 最后修改人
     */
    private String modifier;
}
