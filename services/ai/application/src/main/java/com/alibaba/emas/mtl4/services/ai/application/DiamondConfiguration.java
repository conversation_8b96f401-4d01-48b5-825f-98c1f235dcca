package com.alibaba.emas.mtl4.services.ai.application;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * diamond config.
 *
 * <AUTHOR>
 * @since 2025/2/25
 */
@Slf4j
@Component
@Getter
public class DiamondConfiguration {

    @Value("${cdn.host}")
    private String cdnHost;

    private String version;

    private static final String GROUP_ID = "DEF_FRONT";

    private static final String DATA_ID = "mtl4-admin-fe-version";

    private static final String VERSION_KEY = "version";

    @PostConstruct
    public void init() {
        try {
            // 启动用，并且变化需要立即推送最新值
            Diamond.addListener(DATA_ID, GROUP_ID,
                    new ManagerListenerAdapter() {
                        @Override
                        public void receiveConfigInfo(String config) {
                            try {
                                if (StringUtils.isNotBlank(config)) {
                                    JSONObject configJsonObject = JSON.parseObject(config);
                                    version = configJsonObject.getString(VERSION_KEY);
                                }
                            } catch (Exception e) {
                                log.error("listen config error:", e);
                            }
                        }
                    });
        } catch (Exception e) {
            log.error("listen config error:", e);
        }
    }

    /**
     * 初始化配置.
     */
    private void loadConfig() {
        try {
            String config = Diamond.getConfig(DATA_ID, GROUP_ID, 6000);
            if (StringUtils.isNotBlank(config)) {
                JSONObject configJsonObject = JSON.parseObject(config);
                version = configJsonObject.getString(VERSION_KEY);
            }
        } catch (Exception e) {
            log.error("init config error:", e);
        }
    }

    /**
     * 获取CDN地址.
     *
     * @return CDN地址
     */
    public String getCDNUrlPrefix() {
        return "//" + cdnHost + "/mtl-cloud/mtl-ai-portal/" + version;
    }


}
