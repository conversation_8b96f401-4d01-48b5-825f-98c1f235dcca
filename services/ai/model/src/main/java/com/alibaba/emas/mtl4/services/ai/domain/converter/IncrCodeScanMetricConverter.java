package com.alibaba.emas.mtl4.services.ai.domain.converter;

import javax.persistence.AttributeConverter;

import com.alibaba.emas.mtl4.services.ai.api.model.IncrCodeScanResult;
import com.alibaba.fastjson.JSON;

import org.apache.commons.lang3.StringUtils;

public class IncrCodeScanMetricConverter implements AttributeConverter<IncrCodeScanResult.Metric, String> {

    @Override
    public String convertToDatabaseColumn(IncrCodeScanResult.Metric attribute) {
        if (null != attribute) {
            return JSON.toJSONString(attribute);
        }
        return null;
    }

    @Override
    public IncrCodeScanResult.Metric convertToEntityAttribute(String dbData) {
        if (StringUtils.isNotBlank(dbData)) {
            return JSON.parseObject(dbData, IncrCodeScanResult.Metric.class);
        }
        return null;
    }
}
