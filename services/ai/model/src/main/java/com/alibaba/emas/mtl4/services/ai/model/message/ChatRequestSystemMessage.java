package com.alibaba.emas.mtl4.services.ai.model.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeName;
import lombok.extern.slf4j.Slf4j;


/**
 * system message.
 *
 * <AUTHOR>
 * @date 2024/3/22
 */
@Slf4j
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "role")
@JsonTypeName("system")
public final class ChatRequestSystemMessage extends ChatRequestMessage {

    /*
     * The contents of the system message.
     */
    @JsonProperty(value = "content")
    private Object content;

    /*
     * An optional name for the participant.
     */
    @JsonProperty(value = "name")
    private String name;

    /**
     * Creates an instance of ChatRequestSystemMessage class.
     *
     * @param content the content value to set.
     */
    @JsonCreator
    public ChatRequestSystemMessage(@JsonProperty(value = "content") String content) {
        this.content = content;
        try {
            this.content = OBJECT_MAPPER.readValue(content, Object.class);
        } catch (Exception e) {
            log.debug("skip exception");
        }
    }

    /**
     * Get the content property: The contents of the system message.
     *
     * @return the content value.
     */
    public Object getContent() {
        return this.content;
    }

    /**
     * Get the name property: An optional name for the participant.
     *
     * @return the name value.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Set the name property: An optional name for the participant.
     *
     * @param name the name value to set.
     * @return the ChatRequestSystemMessage object itself.
     */
    public ChatRequestSystemMessage setName(String name) {
        this.name = name;
        return this;
    }
}