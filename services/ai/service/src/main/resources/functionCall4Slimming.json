{"temperature": 0, "maxTokens": 2048, "model": "gpt-4-turbo-128k", "functionCall": {"name": "fetchFunctionsToSlimming"}, "functions": [{"name": "fetchFunctionsToSlimming", "description": "Search for all functions to code slimming.", "parameters": {"type": "object", "properties": {"repoPath": {"type": "string", "description": "code repository URL"}, "branch": {"type": "string", "description": "code repository branch"}, "filePath": {"type": "string", "description": "code file path"}, "methodName": {"type": "string", "description": "code function name"}}, "required": []}}]}