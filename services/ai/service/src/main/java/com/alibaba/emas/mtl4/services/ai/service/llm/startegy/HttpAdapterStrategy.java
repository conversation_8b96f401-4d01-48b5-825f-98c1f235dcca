package com.alibaba.emas.mtl4.services.ai.service.llm.startegy;

import com.alibaba.emas.mtl4.commons.utils.StringKit;
import com.alibaba.emas.mtl4.services.ai.api.model.AskLLMDTO;
import com.alibaba.emas.mtl4.services.ai.api.model.LLMResponseVO;
import com.alibaba.emas.mtl4.services.ai.domain.LLMInfo;
import com.alibaba.emas.mtl4.services.ai.model.Constants;
import com.alibaba.emas.mtl4.services.ai.service.common.exception.AiErrorCode;
import com.alibaba.emas.mtl4.services.ai.service.common.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static jodd.util.ThreadUtil.sleep;

/**
 * 基础适配器策略抽象类
 *
 * <AUTHOR>
 * @Date 2023/08/01
 */
@Slf4j
public abstract class HttpAdapterStrategy extends BaseAdapter {

    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(Constants.HTTP_CONNECT_TIMEOUT_SECOND, TimeUnit.SECONDS)
            .readTimeout(Constants.HTTP_READ_WRITE_TIMEOUT_SECOND, TimeUnit.SECONDS)
            .writeTimeout(Constants.HTTP_READ_WRITE_TIMEOUT_SECOND, TimeUnit.SECONDS)
            .build();


    /**
     * 获取LLM回答
     *
     * @param askLLMDTO 查询信息
     * @param llmInfo   模型信息
     * @return LLM回答
     */
    @Override
    public LLMResponseVO getLLMResponse(AskLLMDTO askLLMDTO, LLMInfo llmInfo) {
        // 超参数填充
        AskLLMDTO filledDTO = fillHyperParams(askLLMDTO, llmInfo);

        // 组装请求
        JSONObject askLLMJson = getAskLLMJson(filledDTO, llmInfo);

        // 发送请求
        JSONObject llmResponseJson = askLLM(askLLMJson, llmInfo);

        // 解析返回
        return getResponseVO(llmResponseJson, filledDTO, llmInfo);
    }


    public AskLLMDTO fillHyperParams(AskLLMDTO askLLMDTO, LLMInfo llmInfo) {
        askLLMDTO.setMaxTokens(
                        Objects.nonNull(askLLMDTO.getMaxTokens()) ?
                                askLLMDTO.getMaxTokens() : llmInfo.getMaxTokenLength());
        askLLMDTO.setTopP(Objects.nonNull(askLLMDTO.getTopP()) ? askLLMDTO.getTopP() : llmInfo.getTopP());
        askLLMDTO.setTemperature(
                Objects.nonNull(askLLMDTO.getTemperature()) ? askLLMDTO.getTemperature() : llmInfo.getTemperature());
        return askLLMDTO;
    }


    /**
     * 检查LLM状态
     *
     * @param askLLMDTO 查询信息
     * @param llmInfo   模型信息
     * @return LLM接口状态
     */
    @Override
    public boolean checkLLMStatus(AskLLMDTO askLLMDTO, LLMInfo llmInfo) {
        JSONObject askLLMJson = getAskLLMJson(askLLMDTO, llmInfo);
        return checkConnection(askLLMJson, llmInfo);
    }


    /**
     * 响应检测
     *
     * @param askLLMJson
     * @param llmInfo
     * @return 响应结果
     */
    public boolean checkConnection(JSONObject askLLMJson, LLMInfo llmInfo) {
        boolean isResponseNonNull = false;
        // LLM请求 带重试
        for (int retry = 0; retry < Constants.RETRY_TIMES && !isResponseNonNull; retry++) {
            JSONObject llmResponseJson = askLLM(askLLMJson, llmInfo);
            isResponseNonNull = Objects.nonNull(llmResponseJson);
            if (!isResponseNonNull) {
                sleep(Constants.RETRY_ONE_MINUTE);
            }
        }
        log.info("查询LLM接口 [{}] 状态为[{}]", llmInfo.getLlmEndpoint(), isResponseNonNull);
        return isResponseNonNull;
    }


    /**
     * 获取调用LLM的请求json
     *
     * @param askLLMDTO
     * @param llmInfo
     * @return 请求体json
     */
    public abstract JSONObject getAskLLMJson(AskLLMDTO askLLMDTO, LLMInfo llmInfo);


    /**
     * 将LLM接口返回结果转换为统一的VO
     *
     * @param llmResponseJson
     * @return 结果VO
     */
    public abstract LLMResponseVO getResponseVO(JSONObject llmResponseJson, AskLLMDTO askLLMDTO, LLMInfo llmInfo);


    /**
     * 获得模型回答结果.
     *
     * @param askContent    请求内容
     * @param llmInfo       模型 Endpoint
     * @return 模型回答结果
     */
    public JSONObject askLLM(JSONObject askContent, LLMInfo llmInfo) {
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"),
                askContent.toJSONString());
        Request request = new Request.Builder()
                .url(llmInfo.getLlmEndpoint() + llmInfo.getApi())
                .post(requestBody)
                .build();
        JSONObject responseJsonObj = null;
        log.info("请求 llm answer 的url [{}]", llmInfo.getLlmEndpoint() + llmInfo.getApi());
        // 发送请求
        try (Response response = CLIENT.newCall(request).execute()) {
            // 校验
            if (Objects.isNull(response.body()) || BooleanUtils.isFalse(response.isSuccessful())) {
                throw new IOException(StringKit.format("请求【{}】失败，响应【{}】",
                        request.url(), JSON.toJSONString(response)));
            }
            responseJsonObj = JSON.parseObject(IOUtils.toString(response.body().byteStream(),
                    StandardCharsets.UTF_8));
            if (response.code() != Constants.SUCCESS_RESPONSE_CODE) {
                throw new IOException(StringKit.format("请求【{}】失败，响应【{}】",
                        request.url(), JSON.toJSONString(response)));
            }
            log.info("LLM的回答为【{}】", responseJsonObj.getString("response"));
        } catch (Exception e) {
            log.error("url [{}] 请求llm答案失败 [{}]", llmInfo.getLlmEndpoint() ,e.getMessage(), e);
            throw new BizException(AiErrorCode.LLM_REQUEST_ERROR);
        }
        return responseJsonObj;
    }


}
