package com.alibaba.emas.mtl4.services.ai.service.common.exception;

/**
 * mtl open api请求异常.
 *
 * <AUTHOR>
 * @date 2023/12/5
 */
public class MtlOpenApiRequestException extends Exception {

    /**
     * 构造函数.
     */
    public MtlOpenApiRequestException() {
        super();
    }

    /**
     * 构造函数.
     *
     * @param message 异常信息
     */
    public MtlOpenApiRequestException(String message) {
        super(message);
    }

    /**
     * 构造函数.
     *
     * @param cause 异常原因
     */
    public MtlOpenApiRequestException(Throwable cause) {
        super(cause);
    }

    /**
     * 构造函数.
     *
     * @param message 异常信息
     * @param cause   异常原因
     */
    public MtlOpenApiRequestException(String message, Throwable cause) {
        super(message, cause);
    }

}
