package com.alibaba.emas.mtl4.services.ai.service.llm.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 请求dto.
 *
 * <AUTHOR>
 * @date 2024/3/31
 */
@Data
@Accessors(chain = true)
public class RequestDTO {

    /**
     * 对话的员工工号
     * 生成型：非必填
     * 对话型：必填
     */
    private String empId;

    /**
     * sessionId	对话的id，由调用方生成。注意保证唯一性，如果sessionId重复，会被视为同一个对话
     * 必填
     */
    private String sessionId;

    /**
     * 一个对话的消息id	非必填，默认为traceId
     */
    private String messageId;

    /**
     * 用户问题，对话型应用必填
     * 生成型：非必填
     * 对话型：必填
     */
    private String question;

    /**
     * variableMap	变量和对应的值
     * 生成型：必填
     * 对话型：非必填
     */
    private Map<String, Object> variableMap;

    /**
     * 是否流式输出	否，默认false
     */
    private Boolean stream;

}
