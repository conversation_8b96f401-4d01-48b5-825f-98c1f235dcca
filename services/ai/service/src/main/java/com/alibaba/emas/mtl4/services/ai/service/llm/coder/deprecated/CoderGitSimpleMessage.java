//package com.alibaba.emas.mtl4.services.ai.service.llm.coder;
//
//import java.util.Base64;
//import java.util.List;
//
//import com.alibaba.emas.mtl4.commons.utils.Assert;
//import com.alibaba.emas.mtl4.services.dev.code.model.CodeReviewChangeSet;
//import com.alibaba.emas.mtl4.services.dev.code.model.CodeReviewChangeTreeDTO;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//
//import com.aliyun.odps.Odps;
//import com.aliyun.odps.PartitionSpec;
//import com.aliyun.odps.Table;
//import com.aliyun.odps.account.Account;
//import com.aliyun.odps.account.AliyunAccount;
//import com.aliyun.odps.data.Record;
//import com.aliyun.odps.data.RecordWriter;
//import com.aliyun.odps.tunnel.TableTunnel;
//import jodd.http.HttpRequest;
//import jodd.http.HttpResponse;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang.StringUtils;
//import org.apache.commons.lang3.ObjectUtils;
//
//@Slf4j
//public class CoderGitSimpleMessage {
//
//    private static final String commonPrivateToken = "rbNzjSUxK2SEyNr3iTQ7";
//    private static final String aoneHost = "http://code.aone.alibaba-inc.com";
//    private static String accessId = new String(Base64.getDecoder().decode("T1JsZG5NOVFzZWJFNHVtdQ=="));
//    private static String accessKey = new String(
//        Base64.getDecoder().decode("aVZzZHYyN0FOdkoxZ1VNa0tFbXBXVmxHTUYwVXJp"));
//    private static String endPoint = "http://service-corp.odps.aliyun-inc.com/api";
//    private static String project = "wireless_mcap_dev";
//    private static String sdTable = "mtl4_coder_llm_git_merge_data_set";
//    private static String codeUrl = "**************************:taobao-android/mytaobao.git";
//    //private static String codeUrl = "**************************:detail-solution/TBDetailCore_android.git";
//    private static Long PROJECT_ID = 13094L;
//
//    //private static Long PROJECT_ID = 396418L;
//
//    public static void main(String[] args) {
//        try {
//            Account account = new AliyunAccount(accessId, accessKey);
//            Odps odps = new Odps(account);
//            odps.setEndpoint(endPoint);
//            odps.setDefaultProject(project);
//
//            TableTunnel tunnel = new TableTunnel(odps);
//            Table table = odps.tables().get(sdTable);
//            String partition = String.format("module_id=%s", "388");
//            //String partition = String.format("module_id=%s", "8527");
//            PartitionSpec partitionSpec = new PartitionSpec(partition);
//            table.deletePartition(partitionSpec, true);
//            table.createPartition(partitionSpec, true);
//
//            for (int r = 0; r < 20; r++) {
//                log.info(">>>round:{}", r);
//                JSONArray records = queryCodeMergeRecordsByProject(codeUrl, r);
//                if (records == null || records.isEmpty()) {
//                    break;
//                }
//                for (int i = 0; i < records.size(); i++) {
//                    try {
//                        log.info(">>>record round:{}", i);
//                        JSONObject record = records.getJSONObject(i);
//                        String title = record.getString("title");
//                        String targetBranch = record.getString("target_branch");
//                        Integer id = record.getInteger("id");
//                        Integer projectId = record.getInteger("project_id");
//                        String detailUrl = record.getString("detail_url");
//                        String mergeBase = record.getString("merge_base");
//
//                        //wirelessread的直接跳过
//                        JSONObject author = record.getJSONObject("author");
//                        if (null != author && "WORKER_1483064542557".equals(author.getString("extern_uid"))) {
//                            continue;
//                        }
//                        //包含revert和merge的直接跳过
//                        if (StringUtils.containsIgnoreCase(title, "merge")
//                            || StringUtils.containsIgnoreCase(title, "revert")) {
//                            continue;
//                        }
//                        //就写了一个bugfix的直接跳过
//                        if (StringUtils.equalsIgnoreCase(title.trim(), "bugfix")) {
//                            continue;
//                        }
//
//                        CodeReviewChangeTreeDTO changeTree = getCodeReviewChangeTree(projectId, id);
//                        if (changeTree != null) {
//                            List<CodeReviewChangeSet> changesets = changeTree.getChangesetDTOS();
//                            //修改文件大于10的只取 修改行数最大的10个文件
//                            if (changesets.size() > 1) {
//                                changesets.sort((o1, o2) ->
//                                    (o2.getAddLines() + o2.getDelLines()) - (o1.getAddLines() + o2.getDelLines()));
//                                changesets = changesets.subList(0, 1);
//                            }
//
//                            if (CollectionUtils.isNotEmpty(changesets)) {
//                                for (int j = 0; j < changesets.size(); j++) {
//                                    CodeReviewChangeSet changeSet = changesets.get(j);
//                                    Boolean renamedFile = changeSet.getRenamedFile();
//                                    Boolean deletedFile = changeSet.getDeletedFile();
//                                    Boolean newFile = changeSet.getNewFile();
//                                    Boolean isBinary = changeSet.getIsBinary();
//                                    String newPath = changeSet.getNewPath();
//                                    String oldPath = changeSet.getOldPath();
//                                    String ref = changeSet.getRef();
//
//                                    String oldClass = StringUtils.substringAfterLast(oldPath, "/");
//                                    if (StringUtils.isBlank(oldClass)) {
//                                        oldClass = oldPath;
//                                    }
//                                    String newClass = StringUtils.substringAfterLast(newPath, "/");
//                                    if (StringUtils.isBlank(newClass)) {
//                                        newClass = newPath;
//                                    }
//                                    String language = StringUtils.substringAfterLast(
//                                        ObjectUtils.firstNonNull(newClass, oldClass), ".");
//
//                                    //二进制文件过滤
//                                    if (isBinary) {
//                                        continue;
//                                    }
//
//                                    String oldContent = null;
//                                    if (StringUtils.isNotBlank(oldPath)) {
//                                        oldContent = getOldFileContent(projectId, oldPath, mergeBase);
//                                    }
//                                    String diff = getChangeSetDiff(projectId, id, ref, 1, true, true, null);
//
//                                    //log.info(oldContent);
//                                    //log.info(diff);
//
//                                    TableTunnel.UploadSession uploadSession = tunnel.createUploadSession(project,
//                                        sdTable, partitionSpec, false);
//
//                                    RecordWriter recordWriter = uploadSession.openRecordWriter(0);
//                                    Record upload = uploadSession.newRecord();
//                                    //模块名称
//                                    upload.setString(0, "mytaobao");
//                                    //upload.setString(0, "TBDetailCore_android");
//                                    //模块描述
//                                    upload.setString(1, "我的淘宝");
//                                    //upload.setString(1, "2018技术升级后手淘标准详情模块之一。核心库（组件，容器，主逻辑）");
//                                    //业务
//                                    upload.setString(2, "");
//                                    //仓库地址
//                                    upload.setString(3, codeUrl);
//                                    //合并id
//                                    upload.setString(4, id.toString());
//                                    //合并url
//                                    upload.setString(5, detailUrl);
//                                    //旧类名全路径
//                                    upload.setString(6, oldPath);
//                                    //新类名全路径
//                                    upload.setString(7, newPath);
//                                    //是否重命名
//                                    upload.setBoolean(8, renamedFile);
//                                    //是否新增文件
//                                    upload.setBoolean(9, newFile);
//                                    //是否删除文件
//                                    upload.setBoolean(10, deletedFile);
//                                    //新类名
//                                    upload.setString(11, newClass);
//                                    //编程语言
//                                    upload.setString(12, language);
//                                    //需求
//                                    upload.setString(13, title);
//                                    //源文件内容
//                                    upload.setString(14, oldContent);
//                                    //修改DIFF内容
//                                    upload.setString(15, diff);
//
//                                    recordWriter.write(upload);
//                                    recordWriter.close();
//                                    uploadSession.commit(new Long[] {0L});
//
//                                    log.info("success");
//
//                                }
//                            }
//                        }
//                    } catch (Exception e) {
//                        log.error("inner error", e);
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("error", e);
//        }
//    }
//
//    public static JSONArray queryCodeMergeRecordsByProject(String scmAddress, Integer round) {
//        Assert.isTrue(StringUtils.isNotEmpty(scmAddress), "scmAddress cannot be null");
//        String url = String.format("%s/api/v3/projects/%s/merge_requests", aoneHost, PROJECT_ID);
//        HttpResponse response = HttpRequest.get(url).query("private_token", commonPrivateToken)
//            .query("state", "merged")
//            .query("per_page", 20)
//            .query("page", round + 1)
//            .send();
//        String content = response.charset("utf-8").bodyText();
//        //log.info(JSON.toJSONString(content));
//        if (200 == response.statusCode()) {
//            return JSON.parseArray(content);
//        }
//        return null;
//    }
//
//    public static CodeReviewChangeTreeDTO getCodeReviewChangeTree(Integer projectId, Integer mergeRequestId) {
//        Assert.notNull(projectId, "projectId cannot be null");
//        Assert.notNull(mergeRequestId, "mergeResquestId cannot be null");
//        String changeTreeUrl = aoneHost + "/api/v4/projects/" + projectId + "/merge_request/" + mergeRequestId +
//            "/push_record/change_tree";
//        HttpRequest request = HttpRequest.get(changeTreeUrl);
//        request.contentType("application/json", "utf-8");
//        request.query("private_token", commonPrivateToken);
//        HttpResponse response = request.send();
//        String content = response.charset("utf-8").bodyText();
//        if (200 == response.statusCode()) {
//            return JSON.parseObject(content, CodeReviewChangeTreeDTO.class);
//        }
//        return null;
//    }
//
//    public static String getChangeSetDiff(Integer projectId, Integer mergeRequestId, String ref,
//        Integer context, boolean ignoreWhitespace, boolean includeExtraInfoLines,
//        String rangeContext) {
//        String changeSetDiffUrl = aoneHost + "/api/v4/projects/" + projectId + "/merge_request/" + mergeRequestId +
//            "/push_record/changeset/diff";
//        HttpRequest request = HttpRequest.get(changeSetDiffUrl);
//        request.contentType("application/json", "utf-8");
//        request.query("ref", ref);
//        if (context != null) {
//            request.query("context", context);
//        }
//        request.query("ignoreWhitespace", ignoreWhitespace);
//        request.query("includeExtraInfoLines", includeExtraInfoLines);
//        request.query("rangeContext", rangeContext);
//        request.query("private_token", commonPrivateToken);
//        HttpResponse response = request.send();
//        String content = response.charset("utf-8").bodyText();
//        if (200 == response.statusCode()) {
//            return JSONObject.parseObject(content).getString("diff");
//        }
//        return null;
//    }
//
//    public static String getOldFileContent(Integer projectId, String filepath, String ref) {
//        String url = String.format("%s/api/v4/projects/%s/repository/blobs", aoneHost, projectId);
//        HttpResponse response = HttpRequest.get(url).query("private_token", commonPrivateToken)
//            .query("filepath", filepath)
//            .query("ref", ref)
//            .send();
//        String content = response.charset("utf-8").bodyText();
//        if (200 == response.statusCode()) {
//            return JSONObject.parseObject(content).getString("content");
//        }
//        return null;
//    }
//}
