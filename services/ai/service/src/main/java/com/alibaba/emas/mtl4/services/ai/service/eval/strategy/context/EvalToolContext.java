package com.alibaba.emas.mtl4.services.ai.service.eval.strategy.context;


import com.alibaba.emas.mtl4.services.ai.enums.EvalToolType;
import com.alibaba.emas.mtl4.services.ai.service.eval.strategy.eval.tool.DataEvalToolExecuteStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 评测工具上下文策略
 *
 * <AUTHOR>
 * @date 2023/10/19
 */
@Slf4j
@Component
public class EvalToolContext {

    @Autowired
    private List<DataEvalToolExecuteStrategy> dataEvalToolExecuteStrategyList;


    public DataEvalToolExecuteStrategy getExecuteStrategy(EvalToolType evalToolType) {
        return dataEvalToolExecuteStrategyList.stream()
                .filter(strategy -> Objects.equals(evalToolType, strategy.getEvalToolType()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("未找到对应的执行策略"));
    }


}
