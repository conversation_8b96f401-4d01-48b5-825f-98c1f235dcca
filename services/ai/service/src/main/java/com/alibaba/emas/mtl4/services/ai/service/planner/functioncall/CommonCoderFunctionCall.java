package com.alibaba.emas.mtl4.services.ai.service.planner.functioncall;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.emas.mtl4.services.ai.service.planner.codeplanner.DefaultStepwisePlanner;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;

import com.microsoft.semantickernel.util.EmbeddedResourceLoader;
import com.microsoft.semantickernel.util.EmbeddedResourceLoader.ResourceLocation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CommonCoderFunctionCall implements CoderFunctionCall {
    private final String systemPrompt;
    private final String originalPromptRes;
    private final String functionParamsRes;
    private final int maxStep;

    public CommonCoderFunctionCall(String systemPrompt, String originalPromptRes, String functionParamsRes, int maxStep) {
        this.systemPrompt = systemPrompt;
        this.originalPromptRes = originalPromptRes;
        this.functionParamsRes = functionParamsRes;
        this.maxStep = maxStep;
    }

    @Override
    public String run(String goal, String empId) {

        String originalPrompt;
        JSONObject functionParams;

        try {
            originalPrompt = EmbeddedResourceLoader.readFile(
                originalPromptRes,
                DefaultStepwisePlanner.class,
                ResourceLocation.CLASSPATH_ROOT,
                ResourceLocation.CLASSPATH,
                ResourceLocation.FILESYSTEM);
            functionParams = JSON.parseObject(EmbeddedResourceLoader.readFile(
                functionParamsRes,
                DefaultStepwisePlanner.class,
                ResourceLocation.CLASSPATH_ROOT,
                ResourceLocation.CLASSPATH,
                ResourceLocation.FILESYSTEM));
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }

        List<FunctionCallMessage> messages = new ArrayList<>();
        messages.add(FunctionCallMessage.builder().role("system").content(systemPrompt).build());
        messages.add(FunctionCallMessage.builder().role("user").content(goal).build());

        //Long agentTaskId = CoderManager.saveAgentTask(goal, empId);
        int stepIndex = 0;

        while (true) {
            log.info(">>>round:{}", stepIndex + 1);

            JSONObject promptJO = JSON.parseObject(originalPrompt);
            promptJO.put("messages", messages);
            if (stepIndex >= maxStep) {
                promptJO.put("functionCall", "none");
            }
            String req = promptJO.toJSONString();

            log.info(">>>{}", req);

            String res = CoderFunctionCallManager.run(req);

            try {
                JSONObject functionCall = JSON.parseObject(res).getJSONObject("function_call");

                var tuple3 = CoderFunctionCallManager.invoke(functionCall, functionParams);

                String functionName = tuple3.getT1();
                String actionVariables = tuple3.getT2();
                String invoked = tuple3.getT3();
                log.info(">>>{}", invoked);

                messages.add(FunctionCallMessage.builder().role("assistant").function_call(functionCall).build());
                messages.add(FunctionCallMessage.builder().role("function").name(functionName).content(invoked).build());

                //CoderManager.saveAgentTaskStep(agentTaskId, stepIndex, functionName, actionVariables, invoked, res, empId);
            } catch (JSONException e) {
                //CoderManager.saveAgentTaskStep(agentTaskId, stepIndex, null, null, null, res, empId);
                //CoderManager.updateAgentTask(agentTaskId, "SUCCESS", res);
                return res;
            } catch (Exception e) {
                //CoderManager.updateAgentTask(agentTaskId, "FAILED", e.getMessage());
                log.error("run error", e);
                throw new RuntimeException(e.getMessage());
            }

            stepIndex++;
        }
    }
}
