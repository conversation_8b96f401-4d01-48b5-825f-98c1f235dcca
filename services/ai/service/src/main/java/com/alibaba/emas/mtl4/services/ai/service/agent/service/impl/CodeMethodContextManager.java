package com.alibaba.emas.mtl4.services.ai.service.agent.service.impl;

import java.util.List;

import com.alibaba.emas.agent.tools.code.CoderManager;
import com.alibaba.emas.mtl4.services.ai.api.model.CodeClass;
import com.alibaba.emas.mtl4.services.ai.api.model.CodeMethod;
import com.alibaba.emas.mtl4.services.ai.api.model.CodeMethodContext;
import com.alibaba.emas.mtl4.services.ai.api.model.DependencyGraphSymbol;
import com.alibaba.emas.mtl4.services.ai.api.model.IncrCodeScanRequest;
import com.alibaba.emas.mtl4.services.ai.service.llm.coder.DependencyGraphManager;
import com.alibaba.fastjson.JSON;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.alibaba.emas.mtl4.services.ai.service.llm.coder.DependencyGraphClient.methodsDependByMethod;

@Slf4j
@Component
public class CodeMethodContextManager {
    @Setter
    @Autowired
    private CodeDiffMethodSetManager codeDiffMethodSetManager;

    public CodeMethodContext buildCodeMethodContext(IncrCodeScanRequest request, CodeMethod codeMethod) {
        var result = new CodeMethodContext();

        String moduleDepGraphKey = DependencyGraphManager.generateDepGraphKey(request.getAppId(),
            request.getModuleGroupId(), request.getModuleArtifactId(), request.getModuleType(),
            request.getModuleDepKey());

        var method = codeDiffMethodSetManager.genMethodFromSymbol(request, moduleDepGraphKey, codeMethod);

        if (StringUtils.isNotBlank(method.getMatchedMethodSymbolIdentifier())) {
            List<DependencyGraphSymbol> symbols = methodsDependByMethod(request.getAppId(), request.getAppVersion(),
                method.getMatchedModuleDepGraphKey(), method.getMatchedMethodSymbolIdentifier());

            symbols.forEach(symbol -> {
                CodeMethod matchingMethod = DependencyGraphManager.findMatchedMethodCode(request.getAppId(), null, symbol);
                if (null != matchingMethod) {
                    result.getDependByMethods().add(matchingMethod);
                }
            });
        }

        if (StringUtils.isNotBlank(codeMethod.getClassName())) {
            CodeClass codeClass = CoderManager.getClassContent(request.getProjectId(),
                codeMethod.getFilePath(), request.getTo(), codeMethod.getClassName());
            result.setCodeClass(codeClass);
        }

        return result;
    }
}
