package com.alibaba.emas.mtl4.services.ai.service.eval.service.impl;

import com.alibaba.emas.mtl4.commons.utils.BeanUtils;
import com.alibaba.emas.mtl4.services.ai.domain.DataEvalJob;
import com.alibaba.emas.mtl4.services.ai.domain.DataEvalJobInstance;
import com.alibaba.emas.mtl4.services.ai.domain.EvalDataSource;
import com.alibaba.emas.mtl4.services.ai.enums.EvalDataSourceType;
import com.alibaba.emas.mtl4.services.ai.enums.EvalInstanceStatus;
import com.alibaba.emas.mtl4.services.ai.model.DataEvalJobDTO;
import com.alibaba.emas.mtl4.services.ai.model.DataEvalJobInstanceBO;
import com.alibaba.emas.mtl4.services.ai.model.EvalDataSourceBO;
import com.alibaba.emas.mtl4.services.ai.repository.DataEvalJobInstanceRepository;
import com.alibaba.emas.mtl4.services.ai.repository.DataEvalJobRepository;
import com.alibaba.emas.mtl4.services.ai.repository.EvalDataSourceRepository;
import com.alibaba.emas.mtl4.services.ai.service.common.exception.BizException;
import com.alibaba.emas.mtl4.services.ai.service.eval.job.ModelEvalJob;
import com.alibaba.emas.mtl4.services.ai.service.eval.strategy.context.EvalDataContext;
import com.alibaba.emas.mtl4.services.ai.service.eval.strategy.context.EvalToolContext;
import com.alibaba.emas.mtl4.services.ai.service.eval.strategy.eval.data.MysqlEvalDataStrategy;
import com.alibaba.emas.mtl4.services.ai.service.eval.strategy.eval.data.OdpsEvalDataStrategy;
import com.alibaba.emas.mtl4.services.ai.service.eval.strategy.eval.tool.EvalPipelineExecuteStrategy;
import com.alibaba.fastjson.JSON;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;

@RunWith(MockitoJUnitRunner.class)
public class DataEvalServiceImplTests {

    @InjectMocks
    private DataEvalServiceImpl dataEvalService;

    @Mock
    private DataEvalJobRepository dataEvalJobRepository;

    @Mock
    private DataEvalJobInstanceRepository dataEvalJobInstanceRepository;

    @Mock
    private EvalDataSourceRepository evalDataSourceRepository;

    @Mock
    private EvalDataContext evalDataContext;

    @Mock
    private EvalToolContext evalToolContext;

    @Mock
    private OdpsEvalDataStrategy odpsEvalDataStrategy;

    @Mock
    private MysqlEvalDataStrategy mysqlEvalDataStrategy;

    @Mock
    private EvalPipelineExecuteStrategy evalPipelineExecuteStrategy;

    @Mock
    ModelEvalJob modelEvalJob;


    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testCreateDataEvalJob() {
        // 准备测试数据
        DataEvalJobDTO dataEvalJobDTO = new DataEvalJobDTO();
        dataEvalJobDTO.setEvalJobName("test");
        dataEvalJobDTO.setEvalJobDesc("test description");

        {
            DataEvalJob dataEvalJob = new DataEvalJob();
            BeanUtils.copyProperties(dataEvalJobDTO, dataEvalJob);
            dataEvalJob.setTriggerCron("0 0 0 * *?");
            Map<String, String> triggerExtra = new HashMap<>(1);
            triggerExtra.put("status", "ONLINE");
            dataEvalJob.setTriggerExtra(triggerExtra);
            dataEvalJob.setIsDeleted(false);
            dataEvalJob.setId(1L);

            DataEvalJob newDataEvalJob = new DataEvalJob();
            BeanUtils.copyProperties(dataEvalJob, newDataEvalJob);
            newDataEvalJob.setJobExtra(new HashMap<>(1));
            newDataEvalJob.getJobExtra().put("schedulerJobId", "2");
            {
                Mockito.when(dataEvalJobRepository.save(any(DataEvalJob.class))).thenReturn(dataEvalJob);
                Mockito.when(modelEvalJob.createScheduleJob(any(DataEvalJob.class))).thenThrow(RuntimeException.class);
                Assertions.assertThrows(BizException.class, () -> dataEvalService.createDataEvalJob(dataEvalJobDTO));
            }

            Mockito.when(dataEvalJobRepository.save(any(DataEvalJob.class))).thenReturn(dataEvalJob, newDataEvalJob);
            Mockito.when(modelEvalJob.createScheduleJob(any(DataEvalJob.class))).thenReturn(2L);

            Long id = dataEvalService.createDataEvalJob(dataEvalJobDTO);

            Assertions.assertEquals(dataEvalJob.getId(), id);
        }

        DataEvalJob dataEvalJob = new DataEvalJob();
        BeanUtils.copyProperties(dataEvalJobDTO, dataEvalJob);
        dataEvalJob.setIsDeleted(false);
        dataEvalJob.setId(1L);

        Mockito.when(dataEvalJobRepository.save(any(DataEvalJob.class))).thenReturn(dataEvalJob);

        // 调用方法
        Long jobId = dataEvalService.createDataEvalJob(dataEvalJobDTO);

        // 验证结果
        Assertions.assertNotNull(jobId);
        Assertions.assertEquals(dataEvalJob.getId(), jobId);
    }


    @Test
    public void testUpdateDataEvalJob() {
        // 测试数据-1 有 trigger 任务
        DataEvalJobDTO dataEvalJobDTOWithTrigger = new DataEvalJobDTO();
        dataEvalJobDTOWithTrigger.setEvalJobName("test");
        dataEvalJobDTOWithTrigger.setEvalJobDesc("test description");
        dataEvalJobDTOWithTrigger.setTriggerCron("0 0 0 * *?");
        Map<String, String> triggerExtra = new HashMap<>(1);
        triggerExtra.put("status", "ONLINE");
        dataEvalJobDTOWithTrigger.setTriggerExtra(triggerExtra);
        dataEvalJobDTOWithTrigger.setIsDeleted(false);

        DataEvalJob dataEvalJobWithTrigger = new DataEvalJob();
        BeanUtils.copyProperties(dataEvalJobDTOWithTrigger, dataEvalJobWithTrigger);
        dataEvalJobWithTrigger.setJobExtra(new HashMap<>(1));
        dataEvalJobWithTrigger.getJobExtra().put("schedulerJobId", "2");
        dataEvalJobWithTrigger.setIsDeleted(false);
        dataEvalJobWithTrigger.setId(1L);

        // 测试数据-2 没有 trigger 任务
        DataEvalJobDTO dataEvalJobDTO = new DataEvalJobDTO();
        dataEvalJobDTO.setEvalJobName("test");
        dataEvalJobDTO.setEvalJobDesc("test description");
        DataEvalJob dataEvalJob = new DataEvalJob();
        BeanUtils.copyProperties(dataEvalJobDTO, dataEvalJob);
        dataEvalJob.setIsDeleted(false);
        dataEvalJob.setId(1L);

        // 原本没有scx任务，更新后没有scx任务 - 不创建
        {
            Mockito.when(dataEvalJobRepository.findById(anyLong())).thenReturn(Optional.of(dataEvalJob));
            Mockito.when(dataEvalJobRepository.save(any(DataEvalJob.class))).thenReturn(dataEvalJob);

            dataEvalService.updateDataEvalJob(1L, dataEvalJobDTO);
            Mockito.verify(dataEvalJobRepository, Mockito.times(1)).save(any(DataEvalJob.class));
            Mockito.verify(modelEvalJob, Mockito.times(0)).createScheduleJob(any(DataEvalJob.class));
            Mockito.verify(modelEvalJob, Mockito.times(0)).updateModelEvalScheduleJob(any(DataEvalJob.class));
            Mockito.verify(modelEvalJob, Mockito.times(0)).deleteModelEvalScheduleJob(any(DataEvalJob.class));
        }

        // 原本没有scx任务，更新后有scx任务 - 新建任务
        {
            Mockito.when(dataEvalJobRepository.findById(anyLong())).thenReturn(Optional.of(dataEvalJob));
            Mockito.when(dataEvalJobRepository.save(any(DataEvalJob.class))).thenReturn(dataEvalJob);
            Mockito.when(modelEvalJob.createScheduleJob(any(DataEvalJob.class))).thenReturn(2L);

            dataEvalService.updateDataEvalJob(1L, dataEvalJobDTOWithTrigger);

            Mockito.verify(dataEvalJobRepository, Mockito.times(2)).save(any(DataEvalJob.class));
            Mockito.verify(modelEvalJob, Mockito.times(1)).createScheduleJob(any(DataEvalJob.class));
        }
        // 原本有scx任务，更新后有有scx任务 - Trigger 不同
        {
            dataEvalJobWithTrigger.setTriggerCron("0 0 7 * *?");
            dataEvalJobWithTrigger.setTriggerExtra(triggerExtra);
            dataEvalJobWithTrigger.getJobExtra().put("schedulerJobId", "2");
            Mockito.when(dataEvalJobRepository.findById(anyLong())).thenReturn(Optional.of(dataEvalJobWithTrigger));
            Mockito.when(dataEvalJobRepository.save(any(DataEvalJob.class))).thenReturn(dataEvalJob);
            Mockito.when(modelEvalJob.createScheduleJob(any(DataEvalJob.class))).thenReturn(2L);

            dataEvalService.updateDataEvalJob(1L, dataEvalJobDTOWithTrigger);

            Mockito.verify(dataEvalJobRepository, Mockito.times(3)).save(any(DataEvalJob.class));
            Mockito.verify(modelEvalJob, Mockito.times(1)).updateModelEvalScheduleJob(any(DataEvalJob.class));

        }
        // 原本有scx任务，更新后有有scx任务 - Extra 不同
        {
            dataEvalJobWithTrigger.setTriggerCron("0 0 0 * *?");
            dataEvalJobWithTrigger.setTriggerExtra(triggerExtra);
            dataEvalJobWithTrigger.getJobExtra().put("schedulerJobId", "2");
            Map<String, String> newTriggerExtra = new HashMap<>(1);
            newTriggerExtra.put("status", "OFFLINE");
            dataEvalJobDTOWithTrigger.setTriggerExtra(newTriggerExtra);
            Mockito.when(dataEvalJobRepository.findById(anyLong())).thenReturn(Optional.of(dataEvalJobWithTrigger));
            Mockito.when(dataEvalJobRepository.save(any(DataEvalJob.class))).thenReturn(dataEvalJobWithTrigger);

            dataEvalService.updateDataEvalJob(1L, dataEvalJobDTOWithTrigger);

            Mockito.verify(dataEvalJobRepository, Mockito.times(4)).save(any(DataEvalJob.class));
            Mockito.verify(modelEvalJob, Mockito.times(2)).updateModelEvalScheduleJob(any(DataEvalJob.class));
        }


    }

    @Test
    public void testExecuteDataEvalJob() {
        // dataSourceId not exist
        {
            Mockito.when(dataEvalJobRepository.findById(Mockito.anyLong())).thenReturn(Optional.empty());
            Assertions.assertThrows(BizException.class, () -> dataEvalService.executeDataEvalJob(123L));
        }
        // 准备测试数据
        String json = "{\"evalJobDesc\":\"日志对比-1116-限制数据长度\",\"evalJobName\":\"test-日志对比\",\"evalTool\":{\"desc\":\"string\",\"type\":\"MTL_PIPELINE\"},\"dataSourceTemplate\":{\"inputDataType\":\"ODPS\",\"outputDataType\":\"MySQL\",\"inputTemplate\":\"select @{searchItem} from wireless_mcap.@{tableName} where @{timeItem} BETWEEN  @{startTime} and @{endTime} limit 10;\",\"outputTemplate\":\"select * from @{tableName} where job_instance_id = @{jobInstanceId};\"},\"isDeleted\":false,\"toolRunTemplate\":{\"inputDataInfo\":{\"searchItem\":\"log_analyze_result_id, search_content, knowledge_content\",\"tableName\":\"emas_mtl_key_log_match_valid\",\"timeItem\":\"analyze_start_time\"},\"outputDataInfo\":{\"tableName\":\"emas_mtl4_ai_data_eval_content\",\"jobInstanceId\":0},\"evalDataColumnMapper\":{\"dataId\":\"log_analyze_result_id\",\"error_log_1\":\"search_content\",\"error_log_2\":\"knowledge_content\"},\"pipelineInfo\":{\"pipelineId\":2017612,\"executeParams\":{\"appId\":16652,\"appType\":\"CLIENT\",\"dependencyType\":\"RELEASE_PRODUCT\",\"needBuild\":true,\"scmAddress\":\"**************************:haoyuan.zj/main_builder.git\",\"scmBranch\":\"V9.8.5.24\",\"selectedStageId\":[7088802],\"uuid\":\"ded05cd9-9e5b-46d7-9a75-7d79f091a125\",\"otherBuildParamsMap\":{\"inputSourceId\":\"@{dataSourceId}\",\"outputSourceId\":\"@{resultSourceId}\",\"jobInstanceId\":\"@{id}\",\"dataEvalJobId\":\"@{dataEvalJobId}\",\"evalDataColumnMapper\":\"@{evalDataColumnMapper}\"}}}}}\n";
        DataEvalJob dataEvalJob = JSON.parseObject(json, DataEvalJob.class);
        Mockito.when(dataEvalJobRepository.findById(Mockito.anyLong())).thenReturn(Optional.of(dataEvalJob));

        DataEvalJobInstance jobInstance = new DataEvalJobInstance();
        jobInstance.setDataEvalJobId(dataEvalJob.getId());
        jobInstance.setStartTime(new Date());
        jobInstance.setEvalStatus(EvalInstanceStatus.RUNNING);
        jobInstance.setId(123L);
        Mockito.when(dataEvalJobInstanceRepository.save(any(DataEvalJobInstance.class))).thenReturn(jobInstance);

        Mockito.when(evalDataContext.getDataStrategy(EvalDataSourceType.ODPS)).thenReturn(odpsEvalDataStrategy);
        Mockito.when(evalDataContext.getDataStrategy(EvalDataSourceType.MYSQL)).thenReturn(mysqlEvalDataStrategy);

        EvalDataSourceBO inputSourceBO = new EvalDataSourceBO();
        inputSourceBO.setSourceType(EvalDataSourceType.ODPS);
        inputSourceBO.setSourceContent("SELECT * FROM wireless_mcap.emas_mtl_key_log_match_valid where job_instance_id = @{jobInstanceId};");
        Mockito.when(odpsEvalDataStrategy.getEvalDataSourceFromTemplate(anyString(), anyMap(), any(), any())).thenReturn(inputSourceBO);

        EvalDataSource inputDataSource = new EvalDataSource();
        BeanUtils.copyProperties(inputSourceBO, inputDataSource);
        inputDataSource.setId(1L);

        EvalDataSourceBO outputSourceBO = new EvalDataSourceBO();
        outputSourceBO.setSourceType(EvalDataSourceType.MYSQL);
        outputSourceBO.setSourceContent("SELECT * FROM wireless_mcap.emas_mtl_key_log_match_valid where job_instance_id = @{jobInstanceId};");
        Mockito.when(mysqlEvalDataStrategy.getResultDataSourceFromTemplate(anyString(), anyMap(), any())).thenReturn(outputSourceBO);

        EvalDataSource outputDataSource = new EvalDataSource();
        BeanUtils.copyProperties(outputSourceBO, outputDataSource);
        outputDataSource.setId(1L);
        Mockito.when(evalDataSourceRepository.save(any(EvalDataSource.class))).thenReturn(inputDataSource, outputDataSource);

        Mockito.when(evalToolContext.getExecuteStrategy(any())).thenReturn(evalPipelineExecuteStrategy);

        DataEvalJobInstanceBO jobInstanceBO = new DataEvalJobInstanceBO();
        BeanUtils.copyProperties(jobInstance, jobInstanceBO);
        jobInstanceBO.setToolRunParams(new HashMap<>());
        Mockito.when(evalPipelineExecuteStrategy.run(any(), anyMap())).thenReturn(jobInstanceBO);

        // 调用方法
        dataEvalService.executeDataEvalJob(123L);

        // 验证结果
        Assertions.assertNotNull(jobInstanceBO.getEvalStatus());
        Mockito.verify(dataEvalJobRepository, Mockito.times(2)).findById(anyLong());
        Mockito.verify(evalDataContext, Mockito.times(2)).getDataStrategy(any());
        Mockito.verify(evalDataSourceRepository, Mockito.times(2)).save(any(EvalDataSource.class));
        Mockito.verify(odpsEvalDataStrategy, Mockito.times(1)).getEvalDataSourceFromTemplate(anyString(), anyMap(), any(), any());
        Mockito.verify(mysqlEvalDataStrategy, Mockito.times(1)).getResultDataSourceFromTemplate(anyString(), anyMap(), any());
        Mockito.verify(evalPipelineExecuteStrategy, Mockito.times(1)).run(any(), anyMap());
        Mockito.verify(dataEvalJobInstanceRepository, Mockito.times(2)).save(any(DataEvalJobInstance.class));
    }


    @Test
    public void testExecuteDataEvalJobsWithSameData() {

    }

}


