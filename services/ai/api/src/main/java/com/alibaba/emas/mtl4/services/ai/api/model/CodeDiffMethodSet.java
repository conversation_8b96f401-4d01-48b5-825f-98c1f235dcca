package com.alibaba.emas.mtl4.services.ai.api.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import static com.alibaba.emas.mtl4.services.ai.api.model.CodeConstant.ADDED_TYPE;
import static com.alibaba.emas.mtl4.services.ai.api.model.CodeConstant.CHANGED_TYPE;
import static com.alibaba.emas.mtl4.services.ai.api.model.CodeConstant.DELETED_TYPE;
import static com.alibaba.emas.mtl4.services.ai.api.model.CodeConstant.RELATED_TYPE;

@Data
public class CodeDiffMethodSet {
    //新增函数
    private Set<DiffMethodMetadata> added;
    //修改函数
    private Set<DiffMethodMetadata> changed;
    //删除函数
    private Set<DiffMethodMetadata> deleted;
    //相关函数
    private Set<DiffMethodMetadata> related;
    //函数之间的关系
    private List<Relation> relations;
    //上游模块
    private Set<DiffMethodModuleMetaData> dependByModules;

    public List<DiffMethodMetadata> fetchAllDiffMethod() {
        List<DiffMethodMetadata> total = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(added)) {
            total.addAll(added.stream().peek(i -> i.setType(ADDED_TYPE)).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(changed)) {
            total.addAll(changed.stream().peek(i -> i.setType(CHANGED_TYPE)).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(deleted)) {
            total.addAll(deleted.stream().peek(i -> i.setType(DELETED_TYPE)).collect(Collectors.toList()));
        }
        return total;
    }

    public List<DiffMethodMetadata> fetchAllMethod() {
        List<DiffMethodMetadata> total = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(added)) {
            total.addAll(added.stream().peek(i -> i.setType(ADDED_TYPE)).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(changed)) {
            total.addAll(changed.stream().peek(i -> i.setType(CHANGED_TYPE)).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(deleted)) {
            total.addAll(deleted.stream().peek(i -> i.setType(DELETED_TYPE)).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(related)) {
            total.addAll(related.stream().peek(i -> i.setType(RELATED_TYPE)).collect(Collectors.toList()));
        }
        return total;
    }

    public Integer countEmptyIdentifier() {
        int emptyIdentifier = 0;
        if (CollectionUtils.isNotEmpty(added)) {
            emptyIdentifier += (int)added.stream()
                .filter(method -> StringUtils.isBlank(method.getIdentifier()))
                .count();
        }
        if (CollectionUtils.isNotEmpty(changed)) {
            emptyIdentifier += (int)changed.stream()
                .filter(method -> StringUtils.isBlank(method.getIdentifier()))
                .count();
        }
        if (CollectionUtils.isNotEmpty(deleted)) {
            emptyIdentifier += (int)deleted.stream()
                .filter(method -> StringUtils.isBlank(method.getIdentifier()))
                .count();
        }
        return emptyIdentifier;
    }

    public void fillInIdentifier() {
        if (CollectionUtils.isNotEmpty(added)) {
            added.stream()
                .filter(method -> StringUtils.isBlank(method.getIdentifier()))
                .forEach(method -> method.setIdentifier(UUID.randomUUID().toString()));
        }
        if (CollectionUtils.isNotEmpty(changed)) {
            changed.stream()
                .filter(method -> StringUtils.isBlank(method.getIdentifier()))
                .forEach(method -> method.setIdentifier(UUID.randomUUID().toString()));
        }
        if (CollectionUtils.isNotEmpty(deleted)) {
            deleted.stream()
                .filter(method -> StringUtils.isBlank(method.getIdentifier()))
                .forEach(method -> method.setIdentifier(UUID.randomUUID().toString()));
        }
    }

    @Data
    @NoArgsConstructor
    public static class DiffMethodMetadata {
        private String identifier;

        private String type;
        private Long appId;
        private String appVersion;
        private String repo;
        private String ref;
        private String filePath;
        private String className;
        private String methodName;
        private String methodParams;
        private Integer startRow;
        private Integer endRow;

        private String methodDocstring;

        private Long moduleId;
        private String moduleDepGraphKey;
        private String moduleVersion;

        private String matchedModuleDepGraphKey;
        private String matchedMethodSymbolIdentifier;

        private Long docstringCost;
        private Long symbolCost;

        public DiffMethodMetadata(CodeMethod codeMethod) {
            this.filePath = codeMethod.getFilePath();
            this.className = codeMethod.getClassName();
            this.methodName = codeMethod.getMethodName();
            this.methodParams = codeMethod.getMethodParams();
            this.startRow = codeMethod.getStartRow();
            this.endRow = codeMethod.getEndRow();
        }

        public DiffMethodMetadata(DependencyGraphSymbol symbol) {
            this.setIdentifier(symbol.getSymbolIdentifier());
            this.setAppId(symbol.getAppId());
            this.setAppVersion(symbol.getAppVersion());
            this.setMatchedModuleDepGraphKey(symbol.getModuleDepKey());
            this.setModuleDepGraphKey(symbol.getModuleDepKey());
            this.setModuleVersion(symbol.getModuleVersion());

            String[] strings = symbol.getSymbolIdentifier().split(":");
            if (strings.length == 3) {
                this.setClassName(strings[0]);
                this.setMethodName(strings[1]);
                this.setMethodParams(strings[2]);
            }

            //this.setStartRow(symbol.getStartRow());
            //this.setEndRow(symbol.getEndRow());
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Relation {
        private String from;
        private String to;
        private String type;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiffMethodModuleMetaData {
        private String identifier;
        private Long moduleId;

        private List<DiffMethodMetadata> methods;
    }
}
