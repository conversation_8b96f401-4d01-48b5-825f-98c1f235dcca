package com.alibaba.emas.mtl4.services.ai.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * 代码反馈条目值对象.
 * <AUTHOR>
 * @date 2024/5/19
 */
@Data
@Accessors(chain = true)
public class CodeFeedbackItemVO {

    /**
     * aone的评论id
     */
    private Integer noteId;

    /**
     * 评论状态
     * ACCEPTED,REJECTED,IGNORED
     */
    private String status;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 相关文件
     */
    private String relevantFile;

    /**
     * 相关规则
     */
    private String relevantRules;

    /**
     * 语言
     */
    private String language;

    /**
     * 建议
     */
    private String suggestion;

    /**
     * 建议等级
     */
    private String suggestionLevel;

    /**
     * 相关行
     */
    private String relevantLine;

    /**
     * 相关行行号
     * */
    private String relevantLineNo;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CodeFeedbackItemVO that = (CodeFeedbackItemVO) o;
        return Objects.equals(relevantFile, that.relevantFile) &&
                Objects.equals(relevantLine, that.relevantLine) &&
                Objects.equals(relevantRules, that.relevantRules);
    }

    @Override
    public int hashCode() {
        return Objects.hash(relevantFile, relevantLine);
    }
}
