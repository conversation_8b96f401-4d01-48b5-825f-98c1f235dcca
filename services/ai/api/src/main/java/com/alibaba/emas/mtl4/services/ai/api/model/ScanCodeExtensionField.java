package com.alibaba.emas.mtl4.services.ai.api.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * 代码扫描扩展字段
 *
 * <AUTHOR>
 * @date 2025/04/16
 */
public class ScanCodeExtensionField {
    private final Map<String, Object> extensions;
    public static Builder builder() {
        return new Builder();
    }
    public static class Builder {
        private ScanCodeExtensionField field = new ScanCodeExtensionField();
        public Builder addAttribute(String key, Object value) {
            field.extensions.put(key, value);
            return this;
        }
        public ScanCodeExtensionField build() {
            return field;
        }
    }
    /**
     * 构造函数
     */
    public ScanCodeExtensionField() {
        this.extensions = new HashMap<>();
    }
    public ScanCodeExtensionField(Map<String, Object> extensions) {
        this.extensions = new HashMap<>(extensions);
    }
    /**
     * 添加 C
     */
    public void put(String key, Object value) {
        extensions.put(Objects.requireNonNull(key), value);
    }
    /**
     * 获取 R
     */
    public <T> T get(String key, Class<T> type) {
        Object value = extensions.get(key);
        return type.isInstance(value) ? type.cast(value) : null;
    }
    public boolean containsKey(String key) {
        return extensions.containsKey(key);
    }
    public String getString(String key) {
        return get(key, String.class);
    }
    public Integer getInteger(String key) {
        return get(key, Integer.class);
    }
    public Boolean getBoolean(String key) {
        return get(key, Boolean.class);
    }
    /**
     * 带默认值的获取字段 R
     */
    public <T> T getOrDefault(String key, Class<T> type, T defaultValue) {
        T value = get(key, type);
        return value != null ? value : defaultValue;
    }
    /**
     * 动态获取默认值 R
     */
    public <T> T getOrSupply(String key, Class<T> type, Supplier<T> defaultValueSupplier) {
        T value = get(key, type);
        return value != null ? value : defaultValueSupplier.get();
    }
    /**
     * 类型安全移除字段 D
     */
    public <T> T remove(String key, Class<T> type) {
        Object value = extensions.remove(key);
        return type.isInstance(value) ? type.cast(value) : null;
    }
    /**
     * 序列化
     * @return
     */
    public String toJson() {
        return JSON.toJSONString(extensions);
    }
    /**
     * 反序列化
     * @param json
     * @return
     */
    public static ScanCodeExtensionField fromJson(String json) {
        Type type = new TypeReference<Map<String, Object>>() {}.getType();
        Map<String, Object> map = JSON.parseObject(json, type);
        return new ScanCodeExtensionField(map);
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ScanCodeExtensionField that = (ScanCodeExtensionField) o;
        return extensions.equals(that.extensions);
    }
    @Override
    public int hashCode() {
        return Objects.hash(extensions);
    }
    @Override
    public String toString() {
        return "ExtensionField" + extensions;
    }
}