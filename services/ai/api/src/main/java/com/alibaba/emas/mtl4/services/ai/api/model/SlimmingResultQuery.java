package com.alibaba.emas.mtl4.services.ai.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SlimmingResultQuery {
    private String module;
    private String repo;
    private String from;
    private String to;
    private Long messageId;
    private SlimmingStrategy strategy;

    private SlimmingResultFeedbackStatus feedbackStatus;
    private String slimmingUniqueSymbol;

    private Long appId;
    private String appVersion;

    private Long startTime;
    private Long endTime;
}
