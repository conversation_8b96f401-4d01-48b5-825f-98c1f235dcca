package com.alibaba.emas.mtl4.services.ai.api.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import com.alibaba.emas.mtl4.commons.utils.ConversionUtils;
import com.alibaba.fastjson.JSON;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CodeClass extends CodeBasicInfo<CodeClass> {
    //包名
    private String packageName;
    //类名
    private String className;
    //类目名
    private String categoryName;
    //父类名
    private String superClassName;
    //接口名
    private Set<String> interfaceNames;
    //类源代码
    private String classSourceCode;
    //类注释
    private String classComment;

    public CodeClass(Map<String, Object> metadata, Map<String, Object> content, String text) {
        super(metadata, content, text);
        from(this, metadata, content, text);
    }

    public String fetchClassSourceCodeWithComment() {
        if (StringUtils.isNotBlank(classComment)) {
            return String.format("%s\n%s", classComment, classSourceCode);
        }
        return classSourceCode;
    }

    @Override
    public Map<String, Object> metadata() {
        Map<String, Object> result = new HashMap<>(super.metadata());
        result.put("packageName", packageName);
        result.put("className", className);
        result.put("superClassName", superClassName);
        result.put("interfaceNames", interfaceNames);
        return result;
    }

    @Override
    public void from(CodeClass codeClass, Map<String, Object> metadata, Map<String, Object> content, String text) {
        codeClass.setPackageName(ConversionUtils.toString(metadata.get("packageName")));
        codeClass.setClassName(ConversionUtils.toString(metadata.get("className")));
        codeClass.setSuperClassName(ConversionUtils.toString(metadata.get("superClassName")));
        Optional.ofNullable(metadata.get("interfaceNames"))
            .ifPresent(o -> {
                if (o instanceof ArrayList) {
                    codeClass.setInterfaceNames(new HashSet<>((ArrayList)o));
                }
            });
    }
}
