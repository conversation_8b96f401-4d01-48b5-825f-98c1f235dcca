INSERT INTO TABLE wireless_mcap.mtl4_dependency_graph_realtime_symbol_vertex
SELECT *
FROM (SELECT CONCAT_WS(":", app_id, SPLIT_PART(module_gav, ':', 1, 3), 'method', class_name, method_name,
                       method_desc) AS primary_key
           , app_id
           , module_id
           , SPLIT_PART(module_gav, ':', 1, 3)                                                           AS module_dep_key
           , 'method'                                                                                    AS symbol_type
           , CONCAT_WS(":", class_name, method_name, method_desc)                                        AS symbol_identifier
           , start_row
           , end_row
           , (end_row - start_row + 1)                                                                   AS rows
      FROM wireless_mcap.mtl4_dependency_graph_realtime_android_method_info
      WHERE pipeline_instance_id = '${pipeline_instance_id}'
        AND module_gav = '${module_gav}'
        AND app_id = '${app_id}') as new_value
WHERE NOT EXISTS(SELECT 1
                 FROM wireless_mcap.mtl4_dependency_graph_realtime_symbol_vertex
                 WHERE primary_key = new_value.primary_key)
;