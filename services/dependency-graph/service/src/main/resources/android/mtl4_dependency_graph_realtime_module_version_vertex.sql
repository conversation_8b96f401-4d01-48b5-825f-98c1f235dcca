INSERT INTO TABLE wireless_mcap.mtl4_dependency_graph_realtime_module_version_vertex
SELECT *
FROM (SELECT CONCAT_WS(":", app_id, SPLIT_PART(module_gav, ':', 1, 3), SPLIT_PART(module_gav, ':', 4, 4)) AS primary_key
           , app_id
           , module_id
           , SPLIT_PART(module_gav, ':', 1, 3)                                AS module_dep_key
           , SPLIT_PART(module_gav, ':', 4, 4)                         AS module_version
      FROM wireless_mcap.mtl4_dependency_graph_realtime_android_module_info
      WHERE pipeline_instance_id = '${pipeline_instance_id}'
        AND module_gav = '${module_gav}'
        AND app_id = '${app_id}') new_value
WHERE NOT EXISTS(SELECT 1
                 FROM wireless_mcap.mtl4_dependency_graph_realtime_module_version_vertex
                 WHERE primary_key = new_value.primary_key)
;