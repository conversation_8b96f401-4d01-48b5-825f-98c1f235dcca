package com.alibaba.emas.mtl4.services.dependencygraph.service.impl;

import com.alibaba.emas.mtl4.services.dependencygraph.gateway.ModuleClassRelationGateway;
import com.alibaba.emas.mtl4.services.dependencygraph.model.android.DependencyGav;
import com.alibaba.emas.mtl4.services.dependencygraph.model.android.ModuleInfo;
import com.alibaba.emas.mtl4.services.dependencygraph.model.android.VisitMethodInfo;
import com.alibaba.emas.mtl4.services.dependencygraph.model.graph.ModuleClassRelation;
import com.alibaba.emas.mtl4.services.dependencygraph.service.android.AndroidCollector;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ModuleInfoService {
    @Setter(onMethod_ = {@Autowired})
    private ModuleClassRelationGateway moduleClassRelationGateway;

    public ModuleInfo getModuleInfo(@Nullable Long moduleId, String moduleDepKey, String moduleVersion) {
        DependencyGav gav = new DependencyGav(moduleDepKey + ":" + moduleVersion);
        AndroidCollector.cleanLocalRepo();
        ModuleInfo moduleInfo = AndroidCollector.processGavModule(gav);
        moduleInfo.setModuleId(moduleId);
        // fill 必须在第二遍扫描，确保能命中
        fillVisitModuleInfo(moduleInfo);
        return moduleInfo;
    }


    private void fillVisitModuleInfo(ModuleInfo moduleInfo) {
        Set<String> visitClasses = moduleInfo.getVisitMethodInfos()
                .stream()
                .map(VisitMethodInfo::getVisitMethodOwner)
                .collect(Collectors.toSet());

        Map<String, ModuleClassRelation> moduleClassRelationMap = moduleClassRelationGateway.findAllByClass(visitClasses)
                .stream()
                .collect(Collectors.toMap(ModuleClassRelation::getClassIdentifier, Function.identity(), (a, b) -> a));

        moduleInfo.getVisitMethodInfos()
                .stream()
                .filter(visitMethodInfo -> moduleClassRelationMap.containsKey(visitMethodInfo.getVisitMethodOwner()))
                .forEach(visitMethodInfo -> {
                    ModuleClassRelation moduleClassRelation = moduleClassRelationMap.get(visitMethodInfo.getVisitMethodOwner());
                    visitMethodInfo.setVisitModuleId(moduleClassRelation.getModuleId());
                    visitMethodInfo.setVisitModuleDepKey(moduleClassRelation.getModuleDepKey());
                });
    }
}
