package com.alibaba.emas.mtl4.services.dependencygraph.service.ios;

import com.alibaba.emas.mtl4.services.dependencygraph.model.ios.ModuleDependency;
import com.alibaba.emas.mtl4.services.dependencygraph.model.ios.ModuleInfo;
import com.alibaba.emas.mtl4.commons.utils.UrlUtils;
import com.google.gson.Gson;
import lombok.SneakyThrows;

import java.util.List;
import java.util.Map;

class iOSTest {

    public static final String VERSION = "10.35.10";

    @SneakyThrows
    public static void main(String[] args) {
        String moduleContent = UrlUtils.getContent("https://mtl4.alibaba-inc.com/scheduler/jobs/6621851/artifacts/d229d3959afa4d03908186dda1a2e8c4/download/Podfile.lock");
        List<ModuleInfo> modules = IosCollector.collectModules(moduleContent);
        String depContent = UrlUtils.getContent("https://mtl4.alibaba-inc.com/scheduler/jobs/6652201/artifacts/dafff285eff24adeb2e1ca64dd2921c2/download/pod_deps.json");
        Map<String, List<String>> depMap = IosCollector.collectDeps(depContent);
        List<ModuleDependency> deps = IosCollector.genDep(depMap, modules);
        System.out.println(new Gson().toJson(deps));
        IosUploader.recordModules(modules, VERSION);
        IosUploader.recordDeps(deps, VERSION);
        IosGenerator.runSql(6L, VERSION);
    }
}