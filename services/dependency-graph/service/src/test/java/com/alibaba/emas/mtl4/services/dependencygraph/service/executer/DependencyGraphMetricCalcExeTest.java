package com.alibaba.emas.mtl4.services.dependencygraph.service.executer;

import com.alibaba.emas.mtl4.services.dependencygraph.model.metric.MetricType;
import com.alibaba.emas.mtl4.services.dependencygraph.repository.model.ModuleDependencyDO;
import com.alibaba.emas.mtl4.services.dependencygraph.repository.model.ModuleDependencyPathDO;
import com.alibaba.emas.mtl4.services.dev.api.model.ApplicationBO;
import com.alibaba.emas.mtl4.services.dev.collaboration.model.CollaborationSpaceCodeBO;
import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

class DependencyGraphMetricCalcExeTest {
    private List<ModuleDependencyDO> dependencyList;
    private List<ModuleDependencyPathDO> pathList;
    private List<ApplicationBO> modules;
    private Map<Long, Set<Long>> spaceModuleMap;

    @BeforeEach
    void init() throws IOException {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(Date.class, new DateDeserializer());
        Gson gson = gsonBuilder.create();
        String prefix = "/Users/<USER>/WorkSpace/Project/mtl4/services/dependency-graph/service/src/test/java/com/alibaba/emas/mtl4/services/dependencygraph/service/executer/";
        dependencyList = gson.fromJson(FileUtils.readFileToString(new File(prefix + "/dependency.json")), new TypeToken<List<ModuleDependencyDO>>() {
        }.getType());
        pathList = gson.fromJson(FileUtils.readFileToString(new File(prefix + "/path.json")), new TypeToken<List<ModuleDependencyPathDO>>() {
        }.getType());
        modules = gson.fromJson(FileUtils.readFileToString(new File(prefix + "/modules.json")), new TypeToken<List<ApplicationBO>>() {
        }.getType());

        this.spaceModuleMap = new HashMap<>();
//        long spaceId = 1068050L;
//        List<CollaborationSpaceCodeBO> spaceCodes = gson.fromJson(FileUtils.readFileToString(new File(prefix + "/spaceCode.json")), new TypeToken<List<CollaborationSpaceCodeBO>>() {
//        }.getType());
//        this.spaceModuleMap.put(spaceId, spaceCodes.stream().map(CollaborationSpaceCodeBO::getAppId).collect(Collectors.toSet()));
//        modules = gson.fromJson(FileUtils.readFileToString(new File(prefix + "/modules.json")), new TypeToken<List<ApplicationBO>>() {
//        }.getType());

        long spaceId = 1067914L;
        List<CollaborationSpaceCodeBO> spaceCodes = gson.fromJson(FileUtils.readFileToString(new File(prefix + "/spaceCode2.json")), new TypeToken<List<CollaborationSpaceCodeBO>>() {
        }.getType());
        this.spaceModuleMap.put(spaceId, spaceCodes.stream().map(CollaborationSpaceCodeBO::getAppId).collect(Collectors.toSet()));
        modules = modules.stream().filter(module -> spaceModuleMap.get(spaceId).contains(module.getId())).collect(Collectors.toList());
    }

    @Test
    void calculateGlobalModuleMetric() {
        DependencyGraphMetricCalcExe dependencyGraphMetricCalcExe = new DependencyGraphMetricCalcExe();
        Map<MetricType, Map<Long, Long>> result = dependencyGraphMetricCalcExe.calculateGlobalModuleMetric(modules, dependencyList, pathList);
        System.out.println(new Gson().toJson(result));
    }

    /**
     * SELECT COUNT(DISTINCT(P.id) )  FROM `emas_mtl4_module_dependency_path` as P, `emas_mtl4_collaboration_space_code` as SC
     * WHERE  P.app_id = 1 AND P.app_version = '10.41.10' AND SC.`collaboration_space_id` = 1067914
     * AND JSON_CONTAINS(P.id_path, CONVERT(SC.`app_id`, JSON));
     */
    @Test
    void calculateGlobalSpaceMetric() {
        DependencyGraphMetricCalcExe dependencyGraphMetricCalcExe = new DependencyGraphMetricCalcExe();
        Map<MetricType, Map<Long, Long>> result = dependencyGraphMetricCalcExe.calculateGlobalSpaceMetric(spaceModuleMap, dependencyList, pathList);
        System.out.println(new Gson().toJson(result));
    }

    @Test
    void calculateInSpaceModuleMetric() {
        DependencyGraphMetricCalcExe dependencyGraphMetricCalcExe = new DependencyGraphMetricCalcExe();
        Map<Long, Long> moduleSpaceMap = dependencyGraphMetricCalcExe.fetchModuleSpaceMap(modules, spaceModuleMap);
        Map<MetricType, Map<Long, Long>> result = dependencyGraphMetricCalcExe.calculateInSpaceModuleMetric(moduleSpaceMap, dependencyList, pathList);
        System.out.println(new Gson().toJson(result));
    }

    @Test
    void calculateSpaceMetric() {
        DependencyGraphMetricCalcExe dependencyGraphMetricCalcExe = new DependencyGraphMetricCalcExe();
        Map<MetricType, Map<Long, Long>> result = dependencyGraphMetricCalcExe.calculateSpaceMetric(spaceModuleMap, dependencyList, pathList);
        System.out.println(new Gson().toJson(result));
    }

    class DateDeserializer implements JsonDeserializer<Date> {
        @Override
        public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            long timestamp = json.getAsLong(); // Assuming timestamp is in milliseconds
            return new Date(timestamp);
        }
    }
}