package com.alibaba.emas.mtl4.services.dependencygraph.open;

import com.alibaba.emas.mtl4.boot.starter.web.AbstractController;
import com.alibaba.emas.mtl4.commons.utils.WebResult;
import com.alibaba.emas.mtl4.services.dependencygraph.gateway.DependencyAnalysisTaskGateway;
import com.alibaba.emas.mtl4.services.dependencygraph.model.task.DependencyAnalysisTask;
import com.alibaba.emas.mtl4.services.dependencygraph.model.task.DependencyAnalysisTaskStatus;
import com.alibaba.emas.mtl4.services.dev.api.enums.ConfigAttributeName;
import com.alibaba.emas.mtl4.services.dev.api.enums.ConfigType;
import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.api.model.ConfigurationBO;
import com.alibaba.emas.mtl4.services.dev.configuration.ConfigurationInnerService;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/open/v1/iosAnalysis")
public class iOSAnalysisController extends AbstractController {
    @Autowired
    private ConfigurationInnerService configurationInnerService;
    @Autowired
    private DependencyAnalysisTaskGateway dependencyAnalysisTaskGateway;

    @GetMapping("/sourceDir")
    public WebResult<List<String>> getSourceDir(@RequestParam("appId") Long appId) {
        String configurationValue = configurationInnerService.getConfigurationValue(EntityType.APPLICATION, appId, ConfigType.APPLICATION_DEFAULT_CONFIG, ConfigAttributeName.SOURCE_CODE_DIR);
        if (StringUtils.isBlank(configurationValue)) {
            return WebResult.ok(null);
        }
        return WebResult.ok(Arrays.asList(configurationValue.split(",")));
    }

    @PutMapping("/sourceDir")
    public WebResult<Boolean> setSourceDir(@RequestParam("appId") Long appId, @RequestParam("sourceDir") String sourceDir) {
        ConfigurationBO configuration = configurationInnerService.findInnerConfiguration(EntityType.APPLICATION, appId, ConfigType.APPLICATION_DEFAULT_CONFIG, ConfigAttributeName.SOURCE_CODE_DIR);
        if (configuration != null) {
            configuration.setModifier(this.getOperatorUserEmpId());
            configuration.setConfigAttributeValue(sourceDir);
            configurationInnerService.saveInnerConfiguration(configuration);
        } else {
            ConfigurationBO configurationBO = new ConfigurationBO();
            configurationBO.setCreator(this.getOperatorUserEmpId());
            configurationBO.setModifier(this.getOperatorUserEmpId());
            configurationBO.setEntityType(EntityType.APPLICATION);
            configurationBO.setEntityId(appId);
            configurationBO.setConfigType(ConfigType.APPLICATION_DEFAULT_CONFIG);
            configurationBO.setConfigAttributeName(ConfigAttributeName.SOURCE_CODE_DIR);
            configurationBO.setConfigAttributeValue(sourceDir);
            configurationInnerService.saveInnerConfiguration(configurationBO);
        }

        return WebResult.ok(true);
    }

    @PutMapping("/task/{taskId}")
    public WebResult<Boolean> updateResult(@PathVariable("taskId") Long taskId,
                                           @RequestBody TaskResultVO result) {
        Optional<DependencyAnalysisTask> taskOptional = dependencyAnalysisTaskGateway.findById(taskId);
        if (!taskOptional.isPresent()) {
            return WebResult.err(500, "taskOptional not found", "taskOptional not found");
        }
        DependencyAnalysisTask task = taskOptional.get();
        task.setStatus(result.getStatus());
        task.setErrorMsg(result.getResult());
        dependencyAnalysisTaskGateway.saveTask(task);
        return WebResult.ok(true);
    }

    @Getter
    @Setter
    public static class TaskResultVO {
        private DependencyAnalysisTaskStatus status;
        private String result;
    }
}
