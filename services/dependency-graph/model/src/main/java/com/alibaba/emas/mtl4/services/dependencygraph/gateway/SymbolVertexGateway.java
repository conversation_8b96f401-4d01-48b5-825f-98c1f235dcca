package com.alibaba.emas.mtl4.services.dependencygraph.gateway;

import com.alibaba.emas.mtl4.services.dependencygraph.model.graph.SymbolVertex;

import java.util.List;

public interface SymbolVertexGateway {
    /**
     * 查找模块中未被调用的类列表
     *
     * @param appId         应用ID
     * @param version       应用版本
     * @param moduleDepKey  模块依赖key
     * @return 类列表
     */
    List<SymbolVertex> findModuleNotUsedClasses(Long appId, String version, String moduleDepKey);

    /**
     * 查找模块中未被调用的方法列表
     *
     * @param appId         应用ID
     * @param version       应用版本
     * @param moduleDepKey  模块依赖key
     * @return 方法列表
     */
    List<SymbolVertex> findModuleNotUsedMethods(Long appId, String version, String moduleDepKey, Integer limit);

    /**
     * 查找调用本类的类列表
     *
     * @param appId           应用ID
     * @param version         应用版本
     * @param moduleDepKey    模块依赖key
     * @param classIdentifier 类标识，如com/taobao/android/favsdk/avfsplugin/AVFSCachePlugin
     * @return 类列表
     */
    List<SymbolVertex> findClassDependBy(Long appId, String version, String moduleDepKey, String classIdentifier);

    /**
     * 查找调用本方法的方法列表
     *
     * @param appId           应用ID
     * @param version         应用版本
     * @param moduleDepKey    模块依赖key
     * @param classIdentifier 类标识，如com/taobao/android/favsdk/avfsplugin/AVFSCachePlugin
     * @param methodName      方法名，如removeObjectForKey
     * @return 方法列表
     */
    List<SymbolVertex> findMethodDependBy(Long appId, String version, String moduleDepKey, String classIdentifier, String methodName);

    /**
     * 查找调用本方法的方法列表
     */
    List<SymbolVertex> findMethodDependBy(Long appId, String version, String moduleDepKey, String methodIdentifier);

    /**
     * 查找调用本方法的符号列表
     */
    List<SymbolVertex> findMethodDependOn(Long appId, String version, String moduleDepKey, String methodIdentifier);

    /**
     * 查找同模块中直接调用本方法的方法列表
     */
    List<SymbolVertex> findMethodDependByInSameModule(Long appId, String version, String moduleDepKey, String methodIdentifier);

    /**
     * 查找不同模块中直接或间接调用本方法的方法列表
     */
    List<SymbolVertex> findMethodDependByInOtherModule(Long appId, String version, String moduleDepKey, String methodIdentifier, Integer hierarchy);

    /**
     * 查找两个函数是否存在依赖关系（同模块）
     */
    Boolean isMethodsIndirectDependByInSameModule(Long appId, String version, String moduleDepKey, String methodIdentifier, String dependByMethodIdentifier, Integer hierarchy);

    /**
     * 查找满足条件的符号
     */
    List<SymbolVertex> findSymbols(Long appId, String version, String moduleDepKey, String symbolType, String symbolIdentifier);

    /**
     * 查找满足条件的符号
     */
    List<SymbolVertex> findSymbols(Long appId, String version, String symbolType, String symbolIdentifier);
}
