package com.alibaba.emas.mtl4.services.dependencygraph.groot;

import com.alibaba.graphscope.groot.sdk.GrootClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GrootConfiguration {
    @Bean
    public GrootClient grootClient() {
        return GrootClient.newBuilder().addHost("*************", 55556).setUsername("graphscope").setPassword("graphscope225902").build();
    }
}
