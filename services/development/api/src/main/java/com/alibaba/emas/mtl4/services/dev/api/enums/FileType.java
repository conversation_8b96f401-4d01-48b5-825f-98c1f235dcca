package com.alibaba.emas.mtl4.services.dev.api.enums;

import lombok.Getter;

/**
 * Created by 箫枫 on 2019/9/6.
 */
@Getter
public enum FileType {
    APK("apk"),

    IPA("ipa"),

    AP("ap"),

    AWB("awb"),

    APKLIB("APKLIB"),

    JAR("jar"),

    POM("pom"),

    LOG("log.tgz,log.zip"),

    //TODO
    ZIP(""),

    FRAMEWORK("framework.zip,library.zip"),

    A("a"),

    PODSPEC("podspec,podspec.json"),

    AAR("AAR"),

    SO("so"),

    SOLIB("solib"),

    OTHER(""),

    DYNAMICLIB("dynamic.framework.zip"),

    IOSHOTPATCH("patch.zip,vrmodel.bin"),

    DARTHOTPATCH("drmodel.bin"),

    PLIST("plist"),

    APATCH("apatch"),

    TPATCH("tpatch"),

    DMG("dmg"),

    DYLIB("dylib"),

    MAPPING(""),

    OCPATCH("iamocFile.zip"),

    OCPATCH_PUB("iam.zip,vrmodel3.bin,vrmodel6.bin"),

    APPZIP("app.zip"),

    AAB("aab"),

    SIGNED_AAB("signed.aab"),

    APK_UNENHANCED("apk.unenhanced"),

    IPATCH("ipatch"),

    TSPATCH("adat"),

    CPPPatch("cdat"),

    SOPATCH("sopatch,result.zip,.aar"),

    DDFP("ddfp"),

    PNG("png"),

    /**
     * 鸿蒙app产物
     */
    HAP("hap"),

    /**
     * 鸿蒙app研发期产物可能也存在HSP 比如手淘
     */
    HSP("hsp"),

    /**
     * 鸿蒙app发布最后的交付产物
     */
    APP("app"),
    ;

    /**
     * 文件后缀
     */
    private String fileSuffix;

    FileType(String fileSuffix) {
        this.fileSuffix = fileSuffix;
    }

    public static FileType getFileType(String fileName) {
        fileName = fileName.toLowerCase();

        if (fileName.endsWith(".apk")) {
            return FileType.APK;
        }
        else if (fileName.endsWith(".ipa")) {
            return FileType.IPA;
        }
        else if (fileName.endsWith(".app.zip")) {
            return FileType.APPZIP;
        }
        else if (fileName.endsWith(".log.tgz") || fileName.endsWith(".log.zip")) {
            return FileType.LOG;
        }
        else if (fileName.endsWith(".dynamic.framework.zip")) {
            return FileType.DYNAMICLIB;
        }
        else if (fileName.endsWith(".framework.zip") || fileName.endsWith(".library.zip")) {
            return FileType.FRAMEWORK;
        }
        else if (fileName.endsWith(".a")) {
            return FileType.A;
        }
        else if (fileName.endsWith(".jar")) {
            return FileType.JAR;
        }
        else if (fileName.endsWith(".apklib")) {
            return FileType.APKLIB;
        }
        else if (fileName.endsWith(".pom")) {
            return FileType.POM;
        }
        else if (fileName.endsWith(".awb")) {
            return FileType.AWB;
        }
        else if (fileName.endsWith(".podspec") || fileName.endsWith(".podspec.json")) {
            return FileType.PODSPEC;
        }
        else if (fileName.endsWith(".aar")) {
            return FileType.AAR;
        }
        else if (fileName.endsWith(".so")) {
            return FileType.SO;
        }
        else if (fileName.endsWith(".solib")) {
            return FileType.SOLIB;
        }
        else if (fileName.endsWith("patch.zip") || fileName.endsWith("vrmodel.bin")) {
            return FileType.IOSHOTPATCH;
        }
        else if ( fileName.endsWith("drmodel.bin")) {
            return FileType.DARTHOTPATCH;
        }
        else if (fileName.endsWith(".plist")) {
            return FileType.PLIST;
        }
        else if (fileName.endsWith(".ap")) {
            return FileType.AP;
        }
        else if (fileName.endsWith(".apatch")) {
            return FileType.APATCH;
        }
        else if (fileName.endsWith(".tpatch")) {
            return FileType.TPATCH;
        }
        else if (fileName.endsWith(".dmg")) {
            return FileType.DMG;
        }
        else if (fileName.endsWith(".dylib")) {
            return FileType.DYLIB;
        }
        else if (fileName.endsWith("iamocFile.zip")) {
            return FileType.OCPATCH;
        }
        else if (fileName.endsWith(".iam.zip") || fileName.endsWith("vrmodel3.bin") || fileName.endsWith("vrmodel6.bin")) {
            return FileType.OCPATCH_PUB;
        }
        else if (fileName.endsWith(".aab")) {
            return FileType.AAB;
        }
        else if (fileName.endsWith(".apk.unenhanced")) {
            return FileType.APK_UNENHANCED;
        }
        else if (fileName.endsWith(".ipatch")) {
            return FileType.IPATCH;
        }
        else if (fileName.endsWith(".ddfp")) {
            return FileType.DDFP;
        }
        else if (fileName.endsWith(".png")) {
            return FileType.PNG;
        } else if (fileName.endsWith(".hap")) {
            return FileType.HAP;
        }
        else if (fileName.endsWith(".adat")) {
            return FileType.TSPATCH;
        }
        else if (fileName.endsWith(".cdat")) {
            return FileType.CPPPatch;
        }
        return FileType.OTHER;
    }


    public static FileType getGooglePlayFileType() {
        return FileType.SIGNED_AAB;
    }

}
