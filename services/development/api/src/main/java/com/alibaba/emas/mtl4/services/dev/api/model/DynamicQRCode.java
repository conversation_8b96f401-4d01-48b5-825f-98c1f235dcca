package com.alibaba.emas.mtl4.services.dev.api.model;

import com.alibaba.emas.mtl4.commons.storage.FileInfo;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicQRCode {

    private static final String ossDownloadUrl = "https://mtl.alibaba-inc.com/oss/";

    private String url;

    private String targetversion;

    private FileInfo fileInfo;

    @Override
    public String toString() {
        return dyFeatureToString();
    }

    private String dyFeatureToString() {
        JSONObject dynamicdeploy = new JSONObject();
        dynamicdeploy.put("targetversion", this.targetversion);
        dynamicdeploy.put("url", url);
        JSONObject result = new JSONObject();
        result.put("dynamicdeploy", dynamicdeploy);
        return result.toJSONString();
    }
}