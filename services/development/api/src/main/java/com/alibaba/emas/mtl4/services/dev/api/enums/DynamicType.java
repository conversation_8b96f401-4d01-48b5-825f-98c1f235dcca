package com.alibaba.emas.mtl4.services.dev.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * 动态化发布类型
 */
@Getter
public enum DynamicType {

    NATIVE_DYNAMIC("NATIVE_DYNAMIC","dynamicupdate"),
    REMOTE_SO_DYNAMIC("REMOTE_SO_DYNAMIC","remoteso"),
    PRE_LOAD("PRE_LOAD","preload"),
    WHALES("WHALES","whales");

    private String type;
    private String safeName; //安全文件名称

    DynamicType(String type, String safeName){
        this.type = type;
        this.safeName = safeName;
    }
}
