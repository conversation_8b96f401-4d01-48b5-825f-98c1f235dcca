package com.alibaba.emas.mtl4.services.dev.api.model;

import com.alibaba.emas.mtl4.acl.commons.annotation.resource.MTLACLId;
import com.alibaba.emas.mtl4.acl.commons.annotation.resource.MTLACLName;
import com.alibaba.emas.mtl4.acl.commons.annotation.resource.MTLACLResource;
import com.alibaba.emas.mtl4.acl.commons.annotation.resource.MTLACLRole;
import com.alibaba.emas.mtl4.acl.commons.model.Resource;
import com.alibaba.emas.mtl4.commons.pipes.annotations.NoPipelineVariable;
import com.alibaba.emas.mtl4.services.dev.api.annotation.BizId;
import com.alibaba.emas.mtl4.services.dev.api.annotation.BizOperator;
import com.alibaba.emas.mtl4.services.dev.api.annotation.BizUserRole;
import com.alibaba.emas.mtl4.services.dev.api.annotation.ContainsBizUserRoleObject;
import com.alibaba.emas.mtl4.services.dev.api.enums.*;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/6/25
 * 集成单
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ContainsBizUserRoleObject(entityType = EntityType.INTEGRATE_SHEET)
@MTLACLResource(resource = Resource.INTEGRATE_SHEET)
public class IntegrateSheetBO {

    /**
     * 主键id
     */
    @MTLACLId
    @MTLACLName
    @BizId
    private Long id;

    /**
     * 创建时间
     */
    @NoPipelineVariable
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @NoPipelineVariable
    private Date gmtModified;

    /**
     * 创建者
     */
    @MTLACLRole(value = com.alibaba.emas.mtl4.acl.commons.model.role.Role.INTEGRATE_SHEET_MANAGER)
    private String creator;

    /**
     * 修改者
     */
    @BizOperator
    private String modifier;

    /**
     * 变更单id
     */
    private Long alterSheetId;

    /**
     * 变更单名称
     */
    private String alterSheetName;

    /**
     * 集成区id
     */
    private Long integrateAreaId;

    /**
     * 要集成的客户端id
     */
    private Long applicationId;

    /**
     * 集成单状态
     */
    private IntegrateSheetStatus status;

    /**
     * 集成原因分类
     */
    @NoPipelineVariable
    private IntegrateReasonCategory integrateReasonCategory;

    /**
     * 集成单描述
     */
    @NoPipelineVariable
    private String description;

    /**
     * 集成单类型
     */
    @NoPipelineVariable
    private IntegrateSheetType integrateSheetType;

    /**
     * 集成单模块列表
     * 这里 IntegrateSheetModuleBO 参数并不完整 使用时 check 一下
     */
    private List<IntegrateSheetModuleBO> integrateSheetModuleBOList;

    /**
     * 关联需求列表
     */
    private List<RequirementRelationBO> requirementRelationList;

    /**
     * 壳工程集成单的git tag名称
     */
    private String scmTag;

    /**
     * 壳工程集成单
     */
    private Boolean isMainFramework;

    /**
     * 持续集成单
     * true: 面向持续集成区的集成单，包括持续集成迭代的集成单，或者普通变更单持续集成模式
     * false: 持续集成区 -> 版本集成区 同步的集成单
     * null: 普通变更单集成单，有可能有持续集成区集成单但未打标
     */
    private Boolean continuousIntegration;

    /**
     * 开发
     */
    @MTLACLRole(value = com.alibaba.emas.mtl4.acl.commons.model.role.Role.INTEGRATE_SHEET_DEVELOPER, isApprover = true)
    @BizUserRole(role = Role.DEVELOPER)
    private String developers;

    /**
     * 测试
     */
    @MTLACLRole(value = com.alibaba.emas.mtl4.acl.commons.model.role.Role.INTEGRATE_SHEET_TESTER)
    @BizUserRole(role = Role.TESTER)
    private String testers;

    /**
     * 集成时间
     */
    private Date integrationTime;

    /**
     * 集成人
     */
    private String integrationUser;

    /**
     * 紧急集成信息
     */
    private EmergencyIntegrateInfo emergencyIntegrateInfo;

    /**
     * 持续集成关联的版本计划
     */
    private Long versionPlanId;

    /**
     * 版本计划关联的集成区ID
     */
    private Long viAreaId;


}
