package com.alibaba.emas.mtl4.services.dev.module.vo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModuleIncrCodeScanMethodTopoVO {

    private List<ModuleNodeVO> dependByModuleNodes;
    private List<MethodNodeVO> methodNodes;
    private List<MethodEdgeVO> methodEdges;

    private Integer addedMethodSize;
    private Integer changedMethodSize;
    private Integer deletedMethodSize;

    private Integer relatedMethodSize;
    private Integer dependByModuleSize;
    private Integer suggestionSize;
    private String totalSummary;

    @Data
    @Builder
    @AllArgsConstructor
    public static class MethodNodeVO {
        private String identifier;

        private String type;
        private Long postNodeCount;
        private Long preNodeCount;

        private String fileLabel;
        private String methodLabel;
        private String methodDocstring;
        private Long methodChartId;
        private String ruleStatus;

        private Map<String, Long> typeCount;
        private List<MethodInfoVO> methodInfos;

        public MethodNodeVO() {
            methodInfos = new ArrayList<>();
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {return true;}
            if (!(o instanceof MethodNodeVO)) {return false;}
            MethodNodeVO that = (MethodNodeVO)o;
            return Objects.equals(identifier, that.identifier);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(identifier);
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MethodInfoVO {
        private String fileLabel;
        private String methodLabel;

        private String repo;
        private String ref;
        private String filePath;
        private String methodName;
        private String methodParams;
        private Integer startRow;
        private Integer endRow;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MethodEdgeVO {
        private String source;
        private String target;
        private String type;

        @Override
        public boolean equals(Object o) {
            if (this == o) {return true;}
            if (!(o instanceof MethodEdgeVO)) {return false;}
            MethodEdgeVO that = (MethodEdgeVO)o;
            return Objects.equals(source, that.source) && Objects.equals(target, that.target)
                && Objects.equals(type, that.type);
        }

        @Override
        public int hashCode() {
            return Objects.hash(source, target, type);
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ModuleNodeVO {
        private String identifier;
        private String moduleName;

        private Long postNodeCount;
        private Long preNodeCount;

        @Override
        public boolean equals(Object o) {
            if (this == o) {return true;}
            if (!(o instanceof ModuleNodeVO)) {return false;}
            ModuleNodeVO that = (ModuleNodeVO)o;
            return Objects.equals(identifier, that.identifier);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(identifier);
        }
    }
}
