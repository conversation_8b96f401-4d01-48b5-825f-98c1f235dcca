package com.alibaba.emas.mtl4.services.dev.app.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;

import com.alibaba.buc.sso.client.util.SimpleUserUtil;
import com.alibaba.buc.sso.client.vo.BucSSOUser;
import com.alibaba.emas.mtl4.acl.commons.annotation.permission.PermissionCheck;
import com.alibaba.emas.mtl4.acl.commons.model.permission.Permission;
import com.alibaba.emas.mtl4.commons.utils.WebResult;
import com.alibaba.emas.mtl4.services.dev.app.service.AppInnerService;
import com.alibaba.emas.mtl4.services.dev.versionPlan.model.TimeNodeType;
import com.alibaba.emas.mtl4.services.dev.versionPlan.model.VersionPlanBO;
import com.alibaba.emas.mtl4.services.dev.versionPlan.model.VersionPlanYearTimeNodeBO;
import com.alibaba.emas.mtl4.services.dev.versionPlan.model.VersionPlanQuery;
import com.alibaba.emas.mtl4.services.dev.versionPlan.VersionPlanService;
import com.alibaba.emas.mtl4.services.dev.versionPlan.service.VersionPlanInfoGetter;
import com.alibaba.emas.mtl4.services.dev.versionPlan.service.VersionPlanInfoGetter.VersionPlanInfo;
import com.alibaba.emas.mtl4.services.open.model.OpenConstants;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021.06.19
 */
@RestController
@RequestMapping("/api/v1/app/{appId}/versionPlan")
@Slf4j
@Api(tags = "应用版本计划接口")
public class AppVersionPlanController {

    @Autowired
    private VersionPlanService versionPlanService;

    @Autowired
    private VersionPlanInfoGetter versionPlanInfoGetter;

    @Autowired
    private AppInnerService appInnerService;

    @PostMapping("/createVersionPlan")
    @ApiOperation(value = "创建版本计划")
    @PermissionCheck(permissions = Permission.APP_EDIT, resourceId = "#appId")
    public WebResult<Long> createVersionPlan(@RequestBody VersionPlanBO versionPlanBO,
        @PathVariable(name = "appId") Long appId, HttpServletRequest request) throws ServletException, IOException {
        BucSSOUser user = SimpleUserUtil.getBucSSOUser(request);
        versionPlanBO.setCreator(user.getEmpId());
        versionPlanBO.setModifier(user.getEmpId());
        versionPlanBO.setGmtCreated(new Date());
        versionPlanBO.setGmtModified(new Date());
        versionPlanBO.setAppId(appId);

        return WebResult.ok(versionPlanService.createVersionPlan(versionPlanBO));
    }

    @GetMapping("/getVersionPlans")
    @ApiOperation(value = "根据开始时间和结束时间获取应用的版本计划列表")
    public WebResult<List<VersionPlanBO>> getVersionPlans(@PathVariable(name = "appId") Long appId,
        @RequestParam Long startTime,
        @RequestParam Long endTime) {
        Date startDate = new Date(startTime);
        Date endDate = new Date(endTime);
        List<VersionPlanBO> versionPlans = versionPlanService.findVersionPlansByAppIdAndTime(appId, startDate, endDate);
        return WebResult.ok(versionPlans);
    }

    @GetMapping("/getYearVersionPlanTimeNodes")
    @ApiOperation(value = "根据开始时间和结束时间获取应用的年历")
    public WebResult<List<VersionPlanYearTimeNodeBO>> getYearVersionPlanTimeNodes(@PathVariable(name = "appId") Long appId,
        @RequestParam Long startTime,
        @RequestParam Long endTime) {
        Date startDate = new Date(startTime);
        Date endDate = new Date(endTime);
        List<VersionPlanYearTimeNodeBO> timeNodes = versionPlanService.findYearTimeNodesByAppIdAndTime(appId, startDate, endDate);
        return WebResult.ok(timeNodes);
    }

    @GetMapping("/getVersionPlansInDate")
    @ApiOperation(value = "获取指定一天所属的版本计划")
    public WebResult<List<VersionPlanBO>> getVersionPlansWithinDate(@PathVariable(name = "appId") Long appId,
        @RequestParam Long dateTime) {
        Date date = new Date(dateTime);
        return WebResult.ok(versionPlanService.getVersionPlansWithinDate(appId, date));
    }

    @PostMapping("/updateVersionPlan")
    @ApiOperation(value = "更新版本计划")
    @PermissionCheck(permissions = Permission.APP_EDIT, resourceId = "#versionPlanBO.appId")
    public WebResult<Boolean> updateVersionPlan(@RequestBody VersionPlanBO versionPlanBO, HttpServletRequest request)
        throws ServletException, IOException {
        // 初始化信息
        BucSSOUser user = SimpleUserUtil.getBucSSOUser(request);
        versionPlanBO.setModifier(user.getEmpId());
        versionPlanService.updateVersionPlan(versionPlanBO);

        return WebResult.ok(true);
    }

    @DeleteMapping("/deleteVersionPlan")
    @ApiOperation(value = "删除版本计划")
    @PermissionCheck(permissions = Permission.APP_EDIT, resourceId = "#appId")
    public WebResult<Boolean> deleteVersionPlan(@PathVariable(name = "appId") Long appId,
        @RequestParam(name = "id") Long id,
        HttpServletRequest request) throws ServletException, IOException {
        return WebResult.ok(versionPlanService.deleteVersionPlan(id));
    }

    @DeleteMapping("/deleteVersionPlanNode")
    @ApiOperation(value = "删除版本计划的时间节点 仅限测试使用")
    public WebResult<Boolean> deleteVersionPlanNode(@PathVariable(name = "appId") Long appId,
        @RequestParam(name = "id") Long id,
        HttpServletRequest request) throws ServletException, IOException {
        return WebResult.ok(versionPlanService.deleteVersionPlanTimeNode(id));
    }

    @ApiOperation(value = "获取时间节点类型")
    @GetMapping("/timeNodeTypes")
    public WebResult<List<TimeNodeType>> getTimeNodeTypes(HttpServletRequest request) {
        return WebResult.ok(Arrays.asList(TimeNodeType.values()));
    }

    @ApiOperation(value = "分页查询应用的版本计划", tags = OpenConstants.OPEN_TAG)
    @GetMapping("/page")
    public WebResult<WebResult.WebPagedModel<VersionPlanBO>> queryVersionPlanPage(
        @PathVariable(name = "appId") Long appId,
        @RequestParam(name = "keyword", required = false) String keyword,
        @RequestParam(name = "isSimple", required = false, defaultValue = "false") Boolean isSimple,
        @RequestParam(name = "startTimeAsc", required = false, defaultValue = "false") Boolean startTimeAsc,
        @RequestParam(name = "lowerEndDate", required = false) Long lowerEndDate,
        @RequestParam(name = "higherStartDate", required = false) Long higherStartDate,
        @RequestParam(name = "pageNum", defaultValue = "0") Integer pageNum,
        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<VersionPlanBO> versionPlanPage = versionPlanService.queryVersionPlans(VersionPlanQuery.builder()
                .keyword(keyword).appId(appId).isSimple(isSimple).startTimeAsc(startTimeAsc)
                .lowerEndDate(lowerEndDate).higherStartDate(higherStartDate).build(), pageNum, pageSize);
        return WebResult.okPaged(versionPlanPage.getContent(), pageNum, pageSize, versionPlanPage.getTotalElements());
    }

    @ApiOperation(value = "根据主键查询版本计划")
    @GetMapping("/{id}")
    public WebResult<VersionPlanBO> getVersionPlan(@PathVariable(name = "appId") Long appId,
        @PathVariable(name = "id") Long id) {
        return WebResult.ok(versionPlanService.getVersionPlanById(id));
    }

    @ApiOperation(value = "根据主键查询版本计划信息提示")
    @GetMapping("/info/{id}")
    public WebResult<VersionPlanInfo> getVersionPlanInfo(@PathVariable(name = "appId") Long appId,
        @PathVariable(name = "id") Long id) {
        return WebResult.ok(versionPlanInfoGetter.getVersionPlanInfo(id));
    }

}
