package com.alibaba.emas.mtl4.services.dev.app.controller;

import com.alibaba.emas.mtl4.commons.utils.WebResult;
import com.alibaba.emas.mtl4.services.dev.appkey.model.ProductFieldConfigCreateRequest;
import com.alibaba.emas.mtl4.services.dev.appkey.service.ProductFieldConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 产品字段配置信息api.
 *
 * <AUTHOR>
 * @date 2022/4/22
 */
@Api(tags = "字段配置信息接口")
@Slf4j
@RestController
@RequestMapping("/api/v1/product_field_config")
public class ProductFieldConfigController {

    @Resource
    private ProductFieldConfigService productFieldConfigService;

    @PostMapping
    @ApiOperation(value = "保存产品字段配置信息", notes = "保存产品字段配置信息")
    public WebResult<Void> saveProductConfig(@RequestBody ProductFieldConfigCreateRequest fieldConfigCreateRequest) {
        productFieldConfigService.saveProductFieldConfig(fieldConfigCreateRequest);
        return WebResult.ok(null);
    }

    @PutMapping("/{id}")
    @ApiOperation(value = "更新产品字段配置信息", notes = "更新产品字段配置信息")
    public WebResult<Void> updateProductConfig(@PathVariable("id") Long id, @RequestBody ProductFieldConfigCreateRequest
            fieldConfigCreateRequest) {
        productFieldConfigService.updateProductFieldConfig(id, fieldConfigCreateRequest);
        return WebResult.ok(null);
    }

    @PatchMapping("/{id}")
    @ApiOperation(value = "更新部分产品字段配置信息", notes = "更新部分产品字段配置信息")
    public WebResult<Void> patchUpdateProductConfig(@PathVariable("id") Long id, @RequestBody
            ProductFieldConfigCreateRequest fieldConfigCreateRequest) {
        productFieldConfigService.patchUpdateProductFieldConfig(id, fieldConfigCreateRequest);
        return WebResult.ok(null);
    }

}
