package com.alibaba.emas.mtl4.services.dev.feedback;

import com.alibaba.emas.mtl4.commons.utils.WebResult;
import com.alibaba.motu.utils.result.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 反馈开放接口
 * 背景：高可用准确性反馈所需
 * <p>
 * TODO: 开放方案落地后可能需要合规改造
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
@Slf4j
@RestController
@RequestMapping("/open/v1/feedbacks")
public class FeedbackOpenController {
    @Autowired
    private MtlFeedbackService mtlFeedbackService;

    @ApiOperation(value = "保存反馈")
    @PostMapping
    @ResponseBody
    public WebResult<String> saveMtlFeedback(@RequestBody MtlFeedbackBO mtlFeedbackBO) {
        Long id = mtlFeedbackService.saveMtlFeedback(mtlFeedbackBO);
        MtlFeedbackBO feedbackBO = mtlFeedbackService.findMtlFeedbackById(id);
        return WebResult.ok(String.format("https://aone.alibaba-inc.com/v2/project/951276/req/%d", feedbackBO.getAoneId()));
    }

    @ApiOperation(value = "查询反馈")
    @GetMapping
    @ResponseBody
    public WebResult<WebResult.WebPagedModel<MtlFeedbackBO>> findMtlFeedbackByQuery(
            @RequestParam String keyword,
            @RequestParam Integer pageSize,
            @RequestParam Integer pageNum
    ) {
        MtlFeedbackQuery query = new MtlFeedbackQuery();
        query.setTitleKeyword(keyword);

        Page<MtlFeedbackBO> result = mtlFeedbackService.findMtlFeedbackByQuery(query, pageSize, pageNum);
        return WebResult.okPaged(result.getItems(), pageNum, pageSize, result.getTotalCount());
    }

}
