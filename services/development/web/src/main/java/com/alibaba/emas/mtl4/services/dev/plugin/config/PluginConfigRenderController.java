package com.alibaba.emas.mtl4.services.dev.plugin.config;

import com.alibaba.emas.mtl4.commons.utils.WebResult;
import com.alibaba.emas.mtl4.services.dev.plugin.model.PluginConfigVO;
import com.alibaba.emas.mtl4.services.dev.plugin.service.PluginConfigRenderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2020-06-11
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/plugin/config")
@Api(tags="render plugin config")
public class PluginConfigRenderController {

    @Autowired
    private PluginConfigRenderService pluginConfigRenderService;

    @ApiOperation(value = "render plugin config")
    @GetMapping()
    public WebResult<PluginConfigVO> renderPluginConfig(@RequestParam(value = "pluginVersionId") Long pluginVersionId) {
        return WebResult.ok(pluginConfigRenderService.renderPluginConfig(pluginVersionId));
    }

}
