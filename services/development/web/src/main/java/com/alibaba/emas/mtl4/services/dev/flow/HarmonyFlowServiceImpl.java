package com.alibaba.emas.mtl4.services.dev.flow;


import com.alibaba.emas.mtl4.commons.storage.FileInfo;
import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.services.dev.build.model.BuildArtifactSuffix;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineExecuteService;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.pipeline.PipelineInstanceExecuteSummary;
import com.alibaba.emas.mtl4.services.dev.publish.model.ExternalLink;
import com.alibaba.emas.mtl4.services.dev.publish.service.GeneralExternalLinkService;
import com.alibaba.emas.mtl4.services.flow.model.annotation.FlowBean;
import com.alibaba.emas.mtl4.services.flow.model.annotation.FlowFunction;
import com.alibaba.emas.mtl4.services.flow.model.annotation.FlowFunctionOutput;
import com.alibaba.emas.mtl4.services.flow.model.annotation.FlowRequestParam;
import com.alibaba.emas.mtl4.services.flow.model.common.FlowDefinitionCategory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alibaba.emas.mtl4.services.flow.model.common.FlowConstants.MAP;
import static com.alibaba.emas.mtl4.services.flow.model.common.FlowConstants.STRING;
import static com.alibaba.emas.mtl4.services.flow.model.common.FlowDefinitionInfo.*;

/**
 * <AUTHOR>
 * @Date 2024-05-27
 */
@Slf4j
@FlowBean
@Component(value = "harmonyFlowService")
public class HarmonyFlowServiceImpl implements HarmonyFlowService {

    @Autowired
    private PipelineExecuteService pipelineExecuteService;
    @Autowired
    private GeneralExternalLinkService generalExternalLinkService;

    private final String admin = "163277";

    private static final String FILE_INFO_JSON = "FILE_INFO_JSON";


    @Override
    @FlowFunction(name = "手淘鸿蒙包对接华为", desc = "手淘鸿蒙包对接华为 （手淘专用）",
            category = FlowDefinitionCategory.INTEGRATE_AREA_CATEGORY,
            icon = "build",
            outputs = {
            @FlowFunctionOutput(name = "产物列表", field = FILE_INFO_JSON, dataType = MAP),
            }
    )
    public void generateTaobaoPackageUrlForHuawei(DelegateExecution execution,
                                                  @FlowRequestParam(info = PIPELINE_INSTANCE_ID) Long pipelineInstanceId,
                                                  @FlowRequestParam(info = EXECUTOR) String executor) {
        //只有超级管理员才允许生成外网链接
        if (!admin.equals(executor)) {
            log.error("generateTaobaoPackageUrlForHuawei executor {} not admin", executor);
            return;
        }

        PipelineInstanceExecuteSummary summary = pipelineExecuteService.queryPipelineInstanceSummaryById(pipelineInstanceId);
        Map<String, Object> fileInfoMap = new HashMap<>(8);
        summary.getStageExecuteSummaries().forEach(stageSummary -> {
            stageSummary.getJobExecuteSummaries().forEach(jobSummary -> {
                List<FileInfo> mainFileInfos = stageSummary.getStageFileInfos().stream()
                        .filter(fi -> BuildArtifactSuffix.BUILD_MAIN_ARTIFACT_SUFFIX.contains(fi.getFileType()))
                        .collect(Collectors.toList());
                if (mainFileInfos.isEmpty()) {
                    return;
                }

                JSONObject fileJSONObject = new JSONObject();
                fileJSONObject.put("jobInstanceId", jobSummary.getId());
//                fileJSONObject.put("fileInfo", mainFileInfos.get(0));
                FileInfo mainFileInfo = mainFileInfos.get(0);
                ExternalLink externalLink = new ExternalLink();
                externalLink.setBuildId(jobSummary.getId());
                externalLink.setJobArtifactId(mainFileInfo.getJobArtifactId());
                try {
                    BizResult<String> externalLinkResult = generalExternalLinkService.general(externalLink, executor);
                    if (externalLinkResult.isSuccess()) {
                        fileJSONObject.put("externalLink", externalLinkResult.getData());
                    }
                } catch (Exception e) {
                    log.error("generate external link error:", e);
                }

                fileInfoMap.put(jobSummary.getName(), fileJSONObject);
            });
        });

        execution.setVariable(FILE_INFO_JSON, fileInfoMap);
    }
}
