package com.alibaba.emas.mtl4.services.dev.alter.model;

import com.alibaba.emas.mtl4.services.dev.api.enums.AlterSheetMode;
import com.alibaba.emas.mtl4.services.dev.api.enums.AlterSheetType;
import com.alibaba.emas.mtl4.services.dev.api.model.AoneRequestItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @Date 2019/10/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlterSheetDTO {

    /**
     * 变更单id
     */
    private Long alterSheetId;

    /**
     * 变更单名称
     */
    private String name;

    /**
     * 变更单模块列表
     */
    private List<AlterSheetModuleDTO> alterSheetModuleItemList;

    /**
     * aone需求列表
     */
    private List<AoneRequestItem> aoneRequestItemList;

    /**
     * 产品id
     */
    private Long applicationId;

    /**
     * 变更描述
     */
    private String description;

    /**
     * 变更单类型
     */
    private AlterSheetType type;

    /**
     * 变更单模式
     */
    private AlterSheetMode mode;

    /**
     * 壳工程对应的集成区ID
     */
    private Long integrateAreaId;

    /**
     * 壳工程/小app变更分支
     */
    private String branch;

    /**
     * 壳工程变更分支前缀
     */
    private String branchPrefix;


    /**
     * 开发者
     */
    private String developers;

    /**
     * 测试
     */
    private String testers;

    /**
     * 测试负责人
     */
    private String testOwner;

    /**
     * 发布负责人
     */
    private String releaseOwner;

    /**
     * 研发协作空间ID
     */
    private Long collaborationSpaceId;

    /**
     * 被复制的变更单id
     */
    private Long copiedAlterSheetId;

    /**
     * 是否复制流水线配置
     */
    private boolean copyPipelineConfig;

    /**
     * 动态化变更单专用，指定已归档的发布单
     */
    private Long targetReleaseId;

    /**
     * 来源变更单id，自主验证->持续集成 和 动态发布->持续集成 的时候会用到
     */
    private Long sourceAlterSheetId;

}
