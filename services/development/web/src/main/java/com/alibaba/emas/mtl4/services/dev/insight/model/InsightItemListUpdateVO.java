package com.alibaba.emas.mtl4.services.dev.insight.model;

import com.alibaba.emas.mtl4.services.dev.insight.model.InsightItemContent;
import com.alibaba.emas.mtl4.services.dev.insight.model.InsightItemPlanType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "insightItemListUpdateVO",description = "发布工作台查询入参")
public class InsightItemListUpdateVO {

    private Long id;

    private String level;

    private String feedback;

    /**
     * 检查项类型
     * NORMAL
     * CRASH
     * FEEDBACK(舆情)
     * CHECKITEM
     * CUSTOM(自定义问题反馈)
     */
    @ApiModelProperty(name = "问题类型", value = "问题类型", required = false)
    private String type;

    @ApiModelProperty(name = "状态undo/done", value = "状态undo/done", required = false)
    private String status;

    @ApiModelProperty(name = "问题来源", value = "问题来源", required = false)
    private String origin;

    @ApiModelProperty(name = "是否通过", value = "是否通过", required = false)
    private Boolean pass;

    @ApiModelProperty(name = "内容", value = "内容", required = false)
    private InsightItemContent content;

    @ApiModelProperty(name = "应用id", value = "应用id", required = false)
    private Long applicationId;

    private Long regressionItemId;

    private Long checkItemId;

    @ApiModelProperty(name = "发布单id", value = "发布单id", required = false)
    private Long releaseId;

    @ApiModelProperty(name = "发布单版本", value = "发布单版本", required = false)
    private String appVersion;

    @ApiModelProperty(name = "处理人列表", value = "处理人列表", required = false)
    private String processor;

    private String validator;

    private InsightItemPlanType plan;

    private String planDetail;

    private String aoneUrl;

    private String aoneId;

    private List<FeedbackProgress> feedbackList;
}
