package com.alibaba.emas.mtl4.services.dev.requirement;

import com.alibaba.emas.mtl4.services.dev.alter.service.AlterSheetService;
import com.alibaba.emas.mtl4.services.dev.alter.service.query.AlterSheetQuery;
import com.alibaba.emas.mtl4.services.dev.api.enums.AlterSheetStatus;
import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.api.enums.IntegrateSheetStatus;
import com.alibaba.emas.mtl4.services.dev.integrate.model.IntegrateSheetInnerBO;
import com.alibaba.emas.mtl4.services.dev.integrate.query.IntegrateSheetQuery;
import com.alibaba.emas.mtl4.services.dev.integrate.service.IntegrateSheetInnerService;
import com.alibaba.emas.mtl4.services.dev.requirement.vo.PromotionRequirementCountVO;
import com.alibaba.emas.mtl4.services.dev.requirement.vo.PromotionRequirementVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024-12-26
 */
@Component
@Slf4j
public class PromotionRequirementAssembler {

    @Autowired
    private PromotionRequirementService promotionRequirementService;
    @Autowired
    private AoneRequirementService aoneReqService;
    @Autowired
    private AlterSheetService alterSheetService;
    @Autowired
    private IntegrateSheetInnerService integrationService;
    @Autowired
    private RequirementRelationService relationService;

    public PromotionRequirementCountVO assemblePromotionRequirementCount(Long appId, Long versionPlanId) {
        PromotionRequirementCountVO countVO = new PromotionRequirementCountVO();
        Page<BigInteger> reqIdPage = getAlterSheetRelatedReqIdPage(appId, versionPlanId, 0, 10);
        countVO.setAllCount((int)reqIdPage.getTotalElements());
        if (countVO.getAllCount() == 0) {
            countVO.setReviewedCount(0);
            return countVO;
        }

        Page<BigInteger> allReqIdPage = getAlterSheetRelatedReqIdPage(appId, versionPlanId, 0, (int)reqIdPage.getTotalElements());
        List<Long> allReqIds = allReqIdPage.getContent().stream()
                .map(BigInteger::longValue)
                .collect(Collectors.toList());
        List<PromotionRequirement> promotionRequirements = promotionRequirementService.batchGetPromotionRequirements(
                appId, versionPlanId);
        List<Long> reviewedReqIds = promotionRequirements.stream()
                .filter(req -> !PromotionRequirementStatus.PENDING.equals(req.getStatus()))
                .map(PromotionRequirement::getReqId)
                .distinct()
                .collect(Collectors.toList());
        reviewedReqIds.retainAll(allReqIds);
        countVO.setReviewedCount(reviewedReqIds.size());
        return countVO;
    }

    public Page<PromotionRequirementVO> assembleAlterSheetRelatedReqs(Long appId, Long versionPlanId, String empId,
                                                                      int pageNo, int pageSize) {
        Page<BigInteger> reqIdPage = getAlterSheetRelatedReqIdPage(appId, versionPlanId, pageNo, pageSize);
        if (CollectionUtils.isEmpty(reqIdPage.getContent())) {
            return new PageImpl<>(Collections.emptyList(), reqIdPage.getPageable(), reqIdPage.getTotalElements());
        }
        List<Integer> reqIds = reqIdPage.getContent().stream()
                .map(BigInteger::intValue).collect(Collectors.toList());
        List<Long> reqIdLongs = reqIdPage.getContent().stream()
                .map(BigInteger::longValue).collect(Collectors.toList());
        Map<Long, AoneRequest> aoneRequestMap = aoneReqService.getAoneRequests(reqIds, empId)
                .stream()
                .collect(Collectors.toMap(AoneRequest::getAoneReqId, Function.identity()));
        Map<Long, PromotionRequirement> promotionRequirementMap = promotionRequirementService.batchGetPromotionRequirements(
                        appId, versionPlanId, reqIdLongs)
                .stream()
                .collect(Collectors.toMap(PromotionRequirement::getReqId, Function.identity()));
        List<PromotionRequirementVO> requirementVOList = reqIdPage.getContent().stream().map(reqId -> {
            PromotionRequirementVO requirementVO = new PromotionRequirementVO();
            requirementVO.setReqId(reqId.longValue());
            requirementVO.setVersionPlanId(versionPlanId);
            requirementVO.setAppId(appId);
            requirementVO.setAoneRequest(aoneRequestMap.get(reqId.longValue()));
            requirementVO.setRequirement(promotionRequirementMap.get(reqId.longValue()));
            return requirementVO;
        }).collect(Collectors.toList());
        return new PageImpl<>(requirementVOList, reqIdPage.getPageable(), reqIdPage.getTotalElements());
    }

    public Page<PromotionRequirementVO> assembleIntegratedReqs(Long appId, Long versionPlanId, String empId,
                                                               int pageNo, int pageSize) {
        Page<BigInteger> reqIdPage = getIntegrateSheetRelatedReqIdPage(appId, versionPlanId, pageNo, pageSize);
        if (CollectionUtils.isEmpty(reqIdPage.getContent())) {
            return new PageImpl<>(Collections.emptyList(), reqIdPage.getPageable(), reqIdPage.getTotalElements());
        }
        List<Integer> reqIds = reqIdPage.getContent().stream()
                .map(BigInteger::intValue).collect(Collectors.toList());
        List<Long> reqIdLongs = reqIdPage.getContent().stream()
                .map(BigInteger::longValue).collect(Collectors.toList());
        Map<Long, AoneRequest> aoneRequestMap = aoneReqService.getAoneRequests(reqIds, empId)
                .stream()
                .collect(Collectors.toMap(AoneRequest::getAoneReqId, Function.identity()));
        Map<Long, PromotionRequirement> promotionRequirementMap = promotionRequirementService.batchGetPromotionRequirements(
                appId, versionPlanId, reqIdLongs)
                .stream()
                .collect(Collectors.toMap(PromotionRequirement::getReqId, Function.identity()));
        List<PromotionRequirementVO> requirementVOList = reqIdPage.getContent().stream().map(reqId -> {
            PromotionRequirementVO requirementVO = new PromotionRequirementVO();
            requirementVO.setReqId(reqId.longValue());
            requirementVO.setVersionPlanId(versionPlanId);
            requirementVO.setAppId(appId);
            requirementVO.setAoneRequest(aoneRequestMap.get(reqId.longValue()));
            requirementVO.setRequirement(promotionRequirementMap.get(reqId.longValue()));
            return requirementVO;
        }).collect(Collectors.toList());
        return new PageImpl<>(requirementVOList, reqIdPage.getPageable(), reqIdPage.getTotalElements());
    }


    private Page<BigInteger> getAlterSheetRelatedReqIdPage(Long appId, Long versionPlanId, int pageNo, int pageSize) {
        AlterSheetQuery query = new AlterSheetQuery();
        query.setApplicationId(appId);
        query.setVersionPlanId(versionPlanId);
        query.setExcludedStatus(AlterSheetStatus.CLOSED);
        List<Long> alterSheetIds = alterSheetService.findAlterSheetIds(query, null);
        if (CollectionUtils.isEmpty(alterSheetIds)) {
            return new PageImpl<>(Collections.emptyList(), PageRequest.of(pageNo, pageSize), 0);
        }
        return relationService.getReqIdPage(EntityType.ALTER_SHEET, alterSheetIds, pageNo, pageSize);
    }


    private Page<BigInteger> getIntegrateSheetRelatedReqIdPage(Long appId, Long versionPlanId, int pageNo, int pageSize) {
        IntegrateSheetQuery query = new IntegrateSheetQuery();
        query.setApplicationId(appId);
        query.setVersionPlanId(versionPlanId);
        query.setIntegrateSheetStatusList(Arrays.asList(IntegrateSheetStatus.INTEGRATE_TO_AREA_SUCCESS,
                IntegrateSheetStatus.EMERGENT_WAIT_APPROVAL));
        List<IntegrateSheetInnerBO> integrateSheets = integrationService.findSimpleIntegrateSheetList(query, null);
        List<Long> integrateSheetIds = integrateSheets.stream()
                .map(IntegrateSheetInnerBO::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(integrateSheetIds)) {
            return new PageImpl<>(Collections.emptyList(), PageRequest.of(pageNo, pageSize), 0);
        }

        return relationService.getReqIdPage(EntityType.INTEGRATE_SHEET, integrateSheetIds,
                pageNo, pageSize);
    }

}
