package com.alibaba.emas.mtl4.services.dev.uniapp;

import com.alibaba.emas.mtl4.boot.starter.web.utils.ErrorCode;
import com.alibaba.emas.mtl4.boot.starter.web.utils.WebRequestUtil;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.commons.utils.ExecutorServiceUtil;
import com.alibaba.emas.mtl4.commons.utils.WebResult;
import com.alibaba.emas.mtl4.services.dev.ServiceFacade;
import com.alibaba.emas.mtl4.services.dev.api.enums.*;
import com.alibaba.emas.mtl4.services.dev.api.model.*;
import com.alibaba.emas.mtl4.services.dev.changefree.service.model.ChangefreeResponse;
import com.alibaba.emas.mtl4.services.dev.common.LockService;
import com.alibaba.emas.mtl4.services.dev.common.monitor.MultipleInstanceMonitor;
import com.alibaba.emas.mtl4.services.dev.deploy.service.ModuleDeployService;
import com.alibaba.emas.mtl4.services.dev.o2.O2CheckPoint;
import com.alibaba.emas.mtl4.services.dev.o2.O2Service;
import com.alibaba.emas.mtl4.services.dev.uniapp.dto.UniAppConfigDTO;
import com.alibaba.emas.mtl4.services.dev.uniapp.dto.UniAppPublishParamDTO;
import com.alibaba.emas.mtl4.services.dev.uniapp.dto.UniAppRollbackParamDTO;
import com.alibaba.emas.mtl4.services.dev.uniapp.meta.UniAppManifestMeta;
import com.alibaba.emas.mtl4.services.dev.uniapp.query.UniAppAssetQuery;
import com.alibaba.emas.mtl4.services.dev.uniapp.vo.*;
import com.alibaba.emas.mtl4.services.flow.model.work.Workflow;
import com.alibaba.emas.mtl4.services.flow.model.work.WorkflowTemplate;
import com.alibaba.emas.mtl4.services.flow.model.work.query.WorkflowQuery;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.motu.utils.result.Page;
import com.google.common.base.Preconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static com.alibaba.emas.mtl4.services.dev.uniapp.UniAppConstants.*;
import static com.alibaba.emas.mtl4.services.flow.model.work.WorkflowConstants.UNI_APP_PUBLISH;

@Api(tags = "UniApp接口")
@Slf4j
@RestController
@RequestMapping("/api/v1/uniapp")
public class UniAppController {

    @Autowired
    private UniAppExecutor uniAppExecutor;

    @Autowired
    private UniAppQueryer uniAppQueryer;

    @Autowired
    private UniAppAssetService uniAppAssetService;

    @Autowired
    private UniAppMemberManager uniAppMemberManager;

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private O2Service o2Service;

    @Autowired
    private UniAppPublisher uniAppPublisher;

    @Autowired
    private ModuleDeployService moduleDeployService;

    @Autowired
    private UniAppConfigManager uniAppConfigManager;

    @Autowired
    private UniAppAssembler uniAppAssembler;

    @Autowired
    private UniAppValidator uniAppValidator;

    @Autowired
    private UniAppArchiver uniAppArchiver;

    @Autowired
    private UniAppCodeManager uniAppCodeManager;

    @Autowired
    private LockService lockService;

    private ExecutorService executorService = ExecutorServiceUtil.createExecutorService("uni-app-callback-%d");

    @ApiOperation(value = "构建")
    @PostMapping("/execute")
    @ResponseBody
    public WebResult<Long> execute(@RequestParam Long alterSheetId, @RequestParam String pubEnv,
        @RequestParam(required = false) Long stepId,
        HttpServletRequest request) {
        String userId = WebRequestUtil.getEmpIdFromRequest(request);
        String ssoTicket = WebRequestUtil.fetchSsoTicket(request);

        return WebResult.ok(uniAppExecutor.execute(alterSheetId, pubEnv, userId, ssoTicket));
    }

    @ApiOperation(value = "创建发布迭代")
    @PostMapping("/createReleaseIteration")
    @ResponseBody
    public WebResult<Long> createReleaseIteration(@RequestParam Long alterSheetId,
        @RequestParam(required = false) Long stepId,
        HttpServletRequest request) throws Exception {

        String lockName = String.format("CreateReleaseIterationLock#%d", alterSheetId);
        lockService.invokeWithLockWait(lockName, () -> {
            String userId = WebRequestUtil.getEmpIdFromRequest(request);

            MultipleInstanceMonitor monitor = uniAppQueryer.queryLatestExecute(alterSheetId, UniAppConstants.PUBLISH);

            AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService().findSimpleAlterSheetById(alterSheetId);
            WorkflowTemplate template = serviceFacade.getCollaborationSpaceService()
                    .getOrCreateReleaseSpaceWorkflowTemplate(UNI_APP_PUBLISH, alterSheetBO.getApplicationId(), userId);

            Preconditions.checkArgument(null == alterSheetBO.getReleaseSpaceId(), "该变更单已存在发布单，不允许重复创建");

            Long workflowId = serviceFacade.getWorkflowService().
                    createWorkflow(template.getId(), EntityType.ALTER_SHEET.name(), alterSheetId, userId);
            //更新发布空间信息
            serviceFacade.getAlterSheetService().updateAlterSheetReleaseSpaceId(alterSheetId, template.getSpaceId());
            //更新变更单状态
            //serviceFacade.getAlterSheetService().updateAlterSheetStatus(alterSheetId, AlterSheetStatus.INTEGRATED, userId);
            //创建ChangeFree
            uniAppPublisher.createChangeFree(alterSheetId, userId);
            //记录发布monitorId
            serviceFacade.getAlterSheetService().updateAlterSheetExtraInfo(alterSheetBO.getId(),
                    alterSheetExtraInfo -> {
                        alterSheetExtraInfo.setUniAppPublishMonitorId(monitor.getId());
                        return alterSheetExtraInfo;
                    });
            //记录发布迭代创建的操作记录
            serviceFacade.getOperationLogService().saveOperationLog(EntityType.ALTER_SHEET, alterSheetBO.getId(),
                    OperationType.UNI_APP_RELEASE_CREATE, null, null,
                    OperationType.UNI_APP_RELEASE_CREATE.getDescription() + " " + alterSheetBO.getName(), alterSheetBO.getModifier());

            return WebResult.ok(workflowId);
        });

        return WebResult.ok(null);
    }

    @ApiOperation(value = "默认版本号")
    @GetMapping("/default/version")
    @ResponseBody
    public WebResult<String> fetchDefaultVersion(@RequestParam Long appId, HttpServletRequest request) {
        String currentVersion = uniAppConfigManager.fetchCurrentVersion(appId);
        if (StringUtils.isNotBlank(currentVersion)) {
            return WebResult.ok(moduleDeployService.getIncreasedVersion(currentVersion, 1));
        }
        return WebResult.ok("1.0.0");
    }

    @ApiOperation(value = "获取发布迭代")
    @GetMapping("/fetchReleaseIteration")
    @ResponseBody
    public WebResult<UniAppReleaseIterationVO> fetchReleaseIteration(@RequestParam Long alterSheetId,
        HttpServletRequest request) {
        String userId = WebRequestUtil.getEmpIdFromRequest(request);
        UniAppReleaseIterationVO iterationVO = new UniAppReleaseIterationVO();
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService()
            .findSimpleAlterSheetById(alterSheetId);

        if (null != alterSheetBO.getReleaseSpaceId()) {
            Workflow workflow = serviceFacade.getWorkflowService().querySingleWorkflow(WorkflowQuery.builder()
                .mainEntityType(EntityType.ALTER_SHEET.name())
                .mainEntityId(alterSheetBO.getId())
                .scope(UNI_APP_PUBLISH)
                .build(), true);
            if (null != workflow) {
                iterationVO.setName(alterSheetBO.getName());
                iterationVO.setUrl(serviceFacade.getDetailUrlService().getMcIterationUrlFromWorkflow(workflow));
                iterationVO.setStatus(UniAppPubStatus.valueOf(alterSheetBO.getStatus().name()));
                AlterSheetExtraInfo extraInfo = alterSheetBO.getExtraInfo();
                if (null != extraInfo && null != extraInfo.getUniAppBetaDeployOrderId()) {
                    iterationVO.setBetaOrderId(extraInfo.getUniAppBetaDeployOrderId());
                    iterationVO.setBetaOrderStatus(
                        uniAppPublisher.getBetaOrderStatus(extraInfo.getUniAppBetaDeployOrderId(), userId));
                }
                iterationVO.setCreator(workflow.getCreator());
                iterationVO.setGmtCreate(workflow.getGmtCreate());
            }
        }

        return WebResult.ok(iterationVO);
    }

    @ApiOperation(value = "构建历史")
    @GetMapping("/executeHistory")
    @ResponseBody
    public WebResult<List<UniAppExecuteInstanceVO>> executeHistory(@RequestParam Long alterSheetId,
        @RequestParam String pubEnv, HttpServletRequest request) {
        return WebResult.ok(uniAppQueryer.queryExecuteHistory(alterSheetId, pubEnv)
            .stream()
            .map(monitor -> completeInstance(monitor, true))
            .collect(Collectors.toList()));
    }

    @ApiOperation(value = "构建明细")
    @GetMapping("/executeDetail")
    @ResponseBody
    public WebResult<UniAppExecuteInstanceVO> executeDetail(@RequestParam Long executeId, HttpServletRequest request) {
        MultipleInstanceMonitor monitor = serviceFacade.getMultipleInstanceMonitorService().findById(executeId);
        return WebResult.ok(completeInstance(monitor, false));
    }

    @ApiOperation(value = "取消构建")
    @PostMapping("/cancelExecute")
    @ResponseBody
    public WebResult<Boolean> cancelExecute(@RequestParam Long executeId, @RequestParam(required = false) Long stepId,
        HttpServletRequest request) {
        String ssoTicket = WebRequestUtil.fetchSsoTicket(request);
        uniAppExecutor.cancelExecute(executeId, ssoTicket);
        return WebResult.ok(true);
    }

    @ApiOperation(value = "查找模块的发布历史")
    @RequestMapping(path = "/{id}/deploy/record/query", method = RequestMethod.GET)
    @ResponseBody
    public WebResult<WebResult.WebPagedModel<ModuleDeployRecordVO>> findModuleDeployRecordByQuery(
        @PathVariable(name = "id") long id,
        @RequestParam(required = false) String commitId,
        @RequestParam(required = false) String version,
        @RequestParam(required = false) String pubEnv,
        @RequestParam(value = "pageNum", defaultValue = "0") Integer pageNum,
        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
        HttpServletRequest request) {

        Page<UniAppAsset> page = uniAppAssetService.findByQuery(UniAppAssetQuery.builder()
            .applicationId(id)
            .commitId(commitId)
            .version(version)
            .pubEnv(pubEnv)
            .build(), pageNum, pageSize);

        List<ModuleDeployRecordVO> moduleDeployRecordVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getItems())) {
            moduleDeployRecordVOS = page.getItems()
                .stream()
                .map(uniAppAsset -> {
                    ModuleDeployRecordVO vo = new ModuleDeployRecordVO();
                    BeanUtils.copyProperties(uniAppAsset, vo);

                    AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService().findSimpleAlterSheetById(uniAppAsset.getAlterSheetId());
                    vo.setStatus(alterSheetBO.getStatus());

                    return vo;
                }).collect(Collectors.toList());
        }

        return WebResult.okPaged(moduleDeployRecordVOS, pageNum, pageSize, page.getTotalCount());
    }

    @ApiOperation(value = "卡口结果")
    @GetMapping("/fetchCheckPoint")
    @ResponseBody
    public WebResult<List<UniAppCheckPointVO>> fetchCheckPoint(@RequestParam Long alterSheetId,
        HttpServletRequest request) {
        String ssoTicket = WebRequestUtil.fetchSsoTicket(request);
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService().findAlterSheetById(alterSheetId);

        List<UniAppIterationInfo> iterationInfos = new ArrayList<>();
        iterationInfos.add(alterSheetBO.fetchManifestO2IterationInfo());
        alterSheetBO.getAlterSheetModuleList().forEach(alterSheetModuleBO -> {
            iterationInfos.add(alterSheetModuleBO.fetchModuleO2IterationInfo());
        });

        List<O2CheckPoint> o2CheckPoints = new ArrayList<>();
        iterationInfos
            .stream()
            .filter(uniAppIterationInfo -> null != uniAppIterationInfo.getO2IterationId())
            .forEach(uniAppIterationInfo -> {
                o2CheckPoints.addAll(o2Service.getCheckpoint(uniAppIterationInfo.getO2IterationId(), ssoTicket)
                    .stream()
                    .filter(o2CheckPoint -> !IGNORE.equals(o2CheckPoint.getStatus()))
                    .peek(o2CheckPoint -> o2CheckPoint.setAppId(uniAppIterationInfo.getAppId()))
                    .collect(Collectors.toList()));
            });

        List<UniAppCheckPointVO> result = new ArrayList<>();
        o2CheckPoints.stream().collect(Collectors.groupingBy(O2CheckPoint::getPoint))
            .forEach((key, value) -> {
                UniAppCheckPointVO checkPoint = new UniAppCheckPointVO();
                checkPoint.setPoint(key);
                checkPoint.setDesc(value.get(0).getDisplayName());
                checkPoint.setName(value.get(0).getDisplayName());
                checkPoint.setStatus(value.stream()
                    .allMatch(o2CheckPoint -> PASS.equals(o2CheckPoint.getStatus())) ? PASS : NOT_PASS);
                List<Long> notPassAppIds = value.stream()
                    .filter(o2CheckPoint -> !PASS.equals(o2CheckPoint.getStatus()))
                    .map(O2CheckPoint::getAppId)
                    .collect(Collectors.toList());
                checkPoint.setUnPassModules(serviceFacade.getAppInnerService().findSimpleApplications(notPassAppIds)
                    .stream().map(UniAppCheckPointModuleVO::new).collect(Collectors.toList()));

                result.add(checkPoint);
            });
        return WebResult.ok(result);
    }

    @ApiOperation(value = "回滚")
    @PostMapping("/rollback")
    @ResponseBody
    public WebResult<Boolean> rollback(@RequestBody UniAppRollbackParamDTO rollbackParamDTO, HttpServletRequest request) {
        String userId = WebRequestUtil.getEmpIdFromRequest(request);
        Assert.notNull(rollbackParamDTO.getSourceRecordList());

        ModuleDeployRecordBO moduleDeployRecordBO = new ModuleDeployRecordBO();

        BeanUtils.copyProperties(rollbackParamDTO.getTargetRecord(), moduleDeployRecordBO);
        List<ModuleDeployRecordBO> sourceRecordBOList = rollbackParamDTO.getSourceRecordList().stream().map(recordVO -> {
            ModuleDeployRecordBO recordBO = new ModuleDeployRecordBO();
            BeanUtils.copyProperties(recordVO, recordBO);

            return recordBO;
        }).collect(Collectors.toList());

        uniAppPublisher.rollback(sourceRecordBOList, moduleDeployRecordBO, userId);
        return WebResult.ok(Boolean.TRUE);
    }

    @ApiOperation(value = "正式发布")
    @PostMapping("/publish")
    @ResponseBody
    public WebResult<Boolean> publish(@RequestBody UniAppPublishParamDTO publishParamDTO,
        @RequestParam(required = false) Long stepId,
        HttpServletRequest request) {
        String userId = WebRequestUtil.getEmpIdFromRequest(request);
        Preconditions.checkNotNull(publishParamDTO);
        Preconditions.checkNotNull(publishParamDTO.getAlterSheetId());
        Preconditions.checkNotNull(publishParamDTO.getPubType());
        if (UniAppPubType.GRAY.equals(publishParamDTO.getPubType())) {
            Preconditions.checkNotNull(publishParamDTO.getBetaOrderStatus());
        }
        Long alterSheetId = publishParamDTO.getAlterSheetId();
        uniAppValidator.checkChangeFreePass(alterSheetId);

        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService().findAlterSheetById(alterSheetId);
        AlterSheetExtraInfo extraInfo = alterSheetBO.getExtraInfo();
        Preconditions.checkNotNull(extraInfo);
        Long monitorId = extraInfo.getUniAppPublishMonitorId();
        Preconditions.checkNotNull(monitorId);
        Long orderId = extraInfo.getUniAppBetaDeployOrderId();

        switch (publishParamDTO.getPubType()) {
            case BETA:  //内网灰度
                uniAppPublisher.publish(monitorId, UniAppPubType.BETA, userId);
                serviceFacade.getAlterSheetService().updateAlterSheetStatus(alterSheetId, AlterSheetStatus.BETA,
                    userId);
                break;
            case GRAY:  //外网灰度
                if (null != orderId) {
                    uniAppPublisher.updateBetaOrderStatus(orderId, publishParamDTO.getBetaOrderStatus(), userId);
                } else {
                    Preconditions.checkArgument(UniAppBetaStatus.STAGE0.equals(publishParamDTO.getBetaOrderStatus()), "灰度发布第一个阶段必须是0%");
                    //BETA环境发布
                    UniAppPage uniAppPage = uniAppPublisher.publish(monitorId, UniAppPubType.GRAY, userId);
                    //创建灰度发布单
                    Long newOrderId = uniAppPublisher.createBetaOrder(alterSheetBO, uniAppPage, userId);
                    //更新灰度发布单状态
                    uniAppPublisher.updateBetaOrderStatus(newOrderId, UniAppBetaStatus.STAGE0, userId);
                    //记录灰度发布单ID
                    serviceFacade.getAlterSheetService().updateAlterSheetExtraInfo(alterSheetId, (_extraInfo) -> {
                        _extraInfo.setUniAppBetaDeployOrderId(newOrderId);
                        return _extraInfo;
                    });
                    serviceFacade.getAlterSheetService().updateAlterSheetStatus(publishParamDTO.getAlterSheetId(),
                        AlterSheetStatus.GRAY, userId);
                }
                break;
            case RELEASE:   //正式发布
                Preconditions.checkNotNull(orderId);
                //修改灰度发布单状态为灰度完成
                uniAppPublisher.updateBetaOrderStatus(orderId, UniAppBetaStatus.READY, userId);
                //结束灰度发布单
                uniAppPublisher.finishBetaOrder(orderId, userId);
                //正式全量发布
                uniAppPublisher.publish(monitorId, UniAppPubType.RELEASE, userId);
                //修改状态
                serviceFacade.getAlterSheetService().updateAlterSheetStatus(publishParamDTO.getAlterSheetId(),
                    AlterSheetStatus.PUBLISHED, userId);
                //归档
                BizResult<Boolean> archiveResult = uniAppArchiver.archive(alterSheetId, userId);
                if (!archiveResult.isSuccess()) {
                    return WebResult.err(-1, "ARCHIVE_ERROR", String.format("发布成功，但部分归档任务失败，请手动重试：%s", archiveResult.getErrorMsg()));
                }
                break;
        }

        return WebResult.ok(true);
    }

    @ApiOperation(value = "获取发布页面")
    @GetMapping("/fetchPublishPages")
    @ResponseBody
    public WebResult<List<UniAppPageResultVO>> fetchPublishPages(@RequestParam Long alterSheetId,
        @RequestParam(value = "pubType") UniAppPubType uniAppPubType, @RequestParam(required = false) Long stepId, HttpServletRequest request) {
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService().findAlterSheetById(alterSheetId);
        AlterSheetExtraInfo extraInfo = alterSheetBO.getExtraInfo();
        Preconditions.checkNotNull(extraInfo);
        Long monitorId = extraInfo.getUniAppPublishMonitorId();
        MultipleInstanceMonitor monitor = serviceFacade.getMultipleInstanceMonitorService().findById(monitorId);
        return WebResult.ok(completePageResult(monitor, ApplicationType.UNI_APP_CLIENT, uniAppPubType));
    }

    @ApiOperation(value = "取消发布")
    @PostMapping("/cancelPublish")
    @ResponseBody
    public WebResult<Boolean> cancelPublish(@RequestParam Long alterSheetId,
        @RequestParam(required = false) Long stepId, HttpServletRequest request) {
        String userId = WebRequestUtil.getEmpIdFromRequest(request);
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService().findAlterSheetById(alterSheetId);
        switch (alterSheetBO.getStatus()) {
            case BETA:

                break;
            case GRAY:
                AlterSheetExtraInfo extraInfo = alterSheetBO.getExtraInfo();
                Preconditions.checkNotNull(extraInfo);
                Long orderId = extraInfo.getUniAppBetaDeployOrderId();
                Preconditions.checkNotNull(orderId);
                uniAppPublisher.cancelBetaOrder(orderId, userId);
                break;

            case PUBLISHED:

                break;
        }
        serviceFacade.getAlterSheetService().updateAlterSheetStatus(alterSheetId, AlterSheetStatus.CLOSED, userId);
        return WebResult.ok(true);
    }

    @ApiOperation(value = "重新生成CF")
    @GetMapping("/createChangeFree")
    @ResponseBody
    public WebResult<ChangefreeResponse> createChangeFree(@RequestParam Long alterSheetId,
        @RequestParam(required = false) Long stepId, HttpServletRequest request) {
        String userId = WebRequestUtil.getEmpIdFromRequest(request);
        uniAppPublisher.createChangeFree(alterSheetId, userId);
        return WebResult.ok(uniAppPublisher.checkRelease(alterSheetId));
    }

    @ApiOperation(value = "获取CF详情")
    @GetMapping("/fetchChangeFree")
    @ResponseBody
    public WebResult<ChangefreeResponse> fetchChangeFree(@RequestParam Long alterSheetId,
        @RequestParam(required = false) Long stepId, HttpServletRequest request) {
        return WebResult.ok(uniAppPublisher.checkRelease(alterSheetId));
    }

    @ApiOperation(value = "代码回流（Manifest和Module）")
    @PostMapping("/mergeAll")
    @ResponseBody
    public WebResult<Boolean> mergeAll(@RequestParam Long alterSheetId,
        @RequestParam(required = false) Long stepId, HttpServletRequest request) {
        String userId = WebRequestUtil.getEmpIdFromRequest(request);
        BizResult<Boolean> result = uniAppCodeManager.mergeAll(alterSheetId);
        if (result.isSuccess()) {
            return WebResult.ok(true);
        } else {
            return WebResult.err(result.getErrorNo(), result.getErrorCode(), result.getErrorMsg());
        }
    }

    @ApiOperation(value = "代码回流（Manifest）")
    @PostMapping("/mergeManifest")
    @ResponseBody
    public WebResult<Boolean> mergeManifest(@RequestParam Long alterSheetId,
        @RequestParam(required = false) Long stepId, HttpServletRequest request) {
        String userId = WebRequestUtil.getEmpIdFromRequest(request);
        BizResult<Boolean> result = uniAppCodeManager.mergeManifest(alterSheetId);
        if (result.isSuccess()) {
            return WebResult.ok(true);
        } else {
            return WebResult.err(result.getErrorNo(), result.getErrorCode(), result.getErrorMsg());
        }
    }

    @ApiOperation(value = "代码回流（Module）")
    @PostMapping("/mergeModule")
    @ResponseBody
    public WebResult<Boolean> mergeModule(@RequestParam Long codeReviewRecordId,
        @RequestParam(required = false) Long stepId, HttpServletRequest request) {
        String userId = WebRequestUtil.getEmpIdFromRequest(request);
        BizResult<Boolean> result = uniAppCodeManager.mergeModule(codeReviewRecordId);
        if (result.isSuccess()) {
            return WebResult.ok(true);
        } else {
            return WebResult.err(result.getErrorNo(), result.getErrorCode(), result.getErrorMsg());
        }
    }

    @ApiOperation(value = "获取UniApp迭代的配置")
    @GetMapping("/fetchConfig")
    @ResponseBody
    public WebResult<UniAppConfigDTO> fetchConfig(@RequestParam Long alterSheetId) {
        return WebResult.ok(new UniAppConfigDTO(alterSheetId,
            uniAppConfigManager.fetchAppInfo(EntityType.ALTER_SHEET, alterSheetId),
            uniAppConfigManager.fetchContainer(EntityType.ALTER_SHEET, alterSheetId),
            uniAppConfigManager.fetchPermission(EntityType.ALTER_SHEET, alterSheetId),
            uniAppConfigManager.fetchCache(EntityType.ALTER_SHEET, alterSheetId),
                uniAppConfigManager.fetchVariables(EntityType.ALTER_SHEET, alterSheetId)
        ));
    }

    @ApiOperation(value = "获取UniApp已归档的配置")
    @GetMapping("/fetchAppConfig")
    @ResponseBody
    public WebResult<UniAppConfigDTO> fetchAppConfig(@RequestParam Long applicationId) {
        return WebResult.ok(new UniAppConfigDTO(
            uniAppConfigManager.fetchAppInfo(EntityType.APPLICATION, applicationId),
            uniAppConfigManager.fetchContainer(EntityType.APPLICATION, applicationId),
            uniAppConfigManager.fetchPermission(EntityType.APPLICATION, applicationId),
            uniAppConfigManager.fetchCache(EntityType.APPLICATION, applicationId)
        ));
    }

    @ApiOperation(value = "更新UniApp迭代的配置")
    @PostMapping("/updateConfig")
    @ResponseBody
    public WebResult<Boolean> updateConfig(@RequestBody UniAppConfigDTO uniAppConfigDTO, HttpServletRequest request) {
        Preconditions.checkNotNull(uniAppConfigDTO, "入参不能为空");
        Preconditions.checkNotNull(uniAppConfigDTO.getAlterSheetId(), "变更单ID不能为空");
        String userId = WebRequestUtil.getEmpIdFromRequest(request);
        uniAppConfigManager.recordAppInfo(EntityType.ALTER_SHEET, uniAppConfigDTO.getAlterSheetId(), uniAppConfigDTO.getAppInfo(), userId);
        uniAppConfigManager.recordContainer(EntityType.ALTER_SHEET, uniAppConfigDTO.getAlterSheetId(), uniAppConfigDTO.getContainer(), userId);
        uniAppConfigManager.recordPermission(EntityType.ALTER_SHEET, uniAppConfigDTO.getAlterSheetId(), uniAppConfigDTO.getPermission(), userId);
        uniAppConfigManager.recordCache(EntityType.ALTER_SHEET, uniAppConfigDTO.getAlterSheetId(), uniAppConfigDTO.getCache(), userId);
        uniAppConfigManager.recordVariables(EntityType.ALTER_SHEET, uniAppConfigDTO.getAlterSheetId(), uniAppConfigDTO.getVariables(), userId);
        return WebResult.ok(true);
    }

    @ApiOperation(value = "同步变更单成员权限")
    @PostMapping("/syncAlterSheetMemberPermission")
    @ResponseBody
    public WebResult<List<Long>> syncAlterSheetMemberPermission(@RequestParam Long alterSheetId, HttpServletRequest request) {
        String ssoTicket = WebRequestUtil.fetchSsoTicket(request);
        String empId = WebRequestUtil.getEmpIdFromRequest(request);
        return WebResult.ok(uniAppMemberManager.syncAlterSheetMember(alterSheetId, empId, ssoTicket));
    }

    private UniAppExecuteInstanceVO completeInstance(MultipleInstanceMonitor monitor, boolean isSimple) {
        if (isSimple) {
            uniAppConfigManager.syncTaskStatus(monitor);
        }

        UniAppExecuteInstanceVO vo = new UniAppExecuteInstanceVO();
        UniAppExecuteStatus executeStatus = UniAppExecuteStatus.parseFromStr(monitor.getTotalStatus());
        vo.setExecuteId(monitor.getId());
        vo.setTotalStatus(executeStatus.getTotalStatus());
        vo.setModuleBuildStatus(executeStatus.getModuleBuildStatus());
        vo.setManifestBuildStatus(executeStatus.getManifestBuildStatus());
        vo.setPublishStatus(executeStatus.getPublishStatus());
        vo.setStartTime(monitor.getGmtCreate());
        String pubEnv = monitor.getExtraInfoValue(UniAppConstants.PUB_ENV);
        vo.setPubEnv(pubEnv);
        Optional.ofNullable(monitor.getExtraInfoValue(MODULE_BUILD_FINISHED_TIME))
            .ifPresent(finishedTime -> {
                vo.setEndTime(new Date(Long.parseLong(finishedTime)));
            });
        vo.setOperator(monitor.getExtraInfoValue(EXECUTOR));
        vo.setErrorMessage(monitor.getExtraInfoValue(UniAppConstants.ERROR_MESSAGE));

        if (!isSimple) {
            vo.setModuleExecuteResults(completeResult(monitor, ApplicationType.MODULE));
            vo.setManifestExecuteResults(completeResult(monitor, ApplicationType.UNI_APP_CLIENT));
            if (DAILY.equals(pubEnv)) {
                vo.setPageResults(completePageResult(monitor, null, UniAppPubType.DAILY));
            }
        }

        return vo;
    }

    private List<UniAppExecuteResultVO> completeResult(MultipleInstanceMonitor monitor,
        ApplicationType applicationType) {

        Map<String, String> taskMap;
        String endTimeString = null;
        if (ApplicationType.UNI_APP_CLIENT.equals(applicationType)) {
            taskMap = uniAppConfigManager.getManifestTaskMap(monitor);
            endTimeString = monitor.getExtraInfoValue(MANIFEST_BUILD_FINISHED_TIME);
        } else if (ApplicationType.MODULE.equals(applicationType)) {
            taskMap = uniAppConfigManager.getModuleTaskMap(monitor);
            endTimeString = monitor.getExtraInfoValue(MODULE_BUILD_FINISHED_TIME);
        } else {
            taskMap = new HashMap<>();
        }
        Set<String> taskKeys = taskMap.keySet();

        Long executeCost;
        if (StringUtils.isNotBlank(endTimeString) && null != monitor.getGmtCreate()) {
            executeCost = Long.parseLong(endTimeString) - monitor.getGmtCreate().getTime();
        } else {
            executeCost = null;
        }

        List<UniAppExecuteResultVO> result = new ArrayList<>();

        uniAppAssetService.findAllByQuery(UniAppAssetQuery.builder()
                .monitorId(monitor.getId())
                .applicationType(applicationType)
                .build())
            .forEach(uniAppAsset -> {
                ApplicationBO applicationBO = serviceFacade.getAppInnerService().findApplication(
                    uniAppAsset.getApplicationId());

                UniAppExecuteResultVO vo = new UniAppExecuteResultVO();
                vo.setVersion(uniAppAsset.getVersion());
                vo.setSystem(uniAppAsset.getTaskSystem());
                String taskId = taskMap.get(uniAppAsset.genTaskIdentifier());
                vo.setTaskId(taskId);
                vo.setIterationId(o2Service.getTaskIterationId(taskId));
                vo.setApplicationId(uniAppAsset.getApplicationId());
                vo.setApplicationName(applicationBO.getName());
                vo.setCodeLibraryAddress(applicationBO.getCodeLibraryAddress());
                vo.setCommitId(uniAppAsset.getCommitId());
                vo.setExecuteCost(executeCost);
                vo.setExecuteStatus(UniAppConstants.DONE);

                taskKeys.remove(uniAppAsset.genTaskIdentifier());

                result.add(vo);
            });

        //补偿一下没有完成的任务
        if (CollectionUtils.isNotEmpty(taskKeys)) {
            if (ApplicationType.UNI_APP_CLIENT.equals(applicationType)) {
                AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService().findSimpleAlterSheetById(
                    monitor.getRelatedEntityId());
                ApplicationBO applicationBO = serviceFacade.getAppInnerService().findApplication(
                    alterSheetBO.getApplicationId());

                taskKeys.forEach(taskKey -> {
                    String system = UniAppIterationTask.parseSystem(taskKey);
                    String taskId = taskMap.get(taskKey);
                    UniAppExecuteResultVO vo = new UniAppExecuteResultVO();
                    vo.setVersion(alterSheetBO.getVersion());
                    vo.setSystem(system);
                    vo.setTaskId(taskId);
                    vo.setIterationId(o2Service.getTaskIterationId(taskId));
                    vo.setApplicationId(alterSheetBO.getApplicationId());
                    vo.setApplicationName(applicationBO.getName());
                    vo.setCodeLibraryAddress(applicationBO.getCodeLibraryAddress());
                    vo.setExecuteStatus(monitor.getInstanceStatus(taskKey));

                    result.add(vo);
                });

            } else if (ApplicationType.MODULE.equals(applicationType)) {
                String instanceAlterSheetModuleString = monitor.getExtraInfoValue(INSTANCE_ALTER_SHEET_MODULE_MAP);

                if (StringUtils.isNotBlank(instanceAlterSheetModuleString)) {
                    HashMap<String, String> instanceAlterSheetModuleMap = StringUtils.isNotBlank(
                        instanceAlterSheetModuleString) ?
                        JSON.parseObject(instanceAlterSheetModuleString,
                            new TypeReference<HashMap<String, String>>() {}) : new HashMap<>();

                    taskKeys.forEach(taskKey -> {
                        Long alterSheetModuleId = Long.parseLong(instanceAlterSheetModuleMap.get(taskKey));
                        AlterSheetModuleBO alterSheetModuleBO = serviceFacade.getAlterSheetModuleService()
                            .getAlterSheetModule(alterSheetModuleId);
                        ApplicationBO module = serviceFacade.getAppInnerService().findApplication(
                            alterSheetModuleBO.getModuleId());

                        String system = UniAppIterationTask.parseSystem(taskKey);
                        String taskId = taskMap.get(taskKey);
                        UniAppExecuteResultVO vo = new UniAppExecuteResultVO();
                        vo.setVersion(alterSheetModuleBO.getVersion());
                        vo.setSystem(system);
                        vo.setTaskId(taskId);
                        vo.setIterationId(o2Service.getTaskIterationId(taskId));
                        vo.setApplicationId(alterSheetModuleBO.getModuleId());
                        vo.setApplicationName(module.getName());
                        vo.setCodeLibraryAddress(module.getCodeLibraryAddress());
                        vo.setExecuteStatus(monitor.getInstanceStatus(taskKey));

                        result.add(vo);
                    });
                }
            }
        }

        return result;
    }

    private List<UniAppPageResultVO> completePageResult(MultipleInstanceMonitor monitor,
        ApplicationType applicationType, UniAppPubType pubType) {
        List<UniAppPageResultVO> result = new ArrayList<>();
        List<UniAppAsset> uniAppAssets = uniAppAssetService.findAllByQuery(UniAppAssetQuery.builder()
            .monitorId(monitor.getId())
            .applicationType(ApplicationType.UNI_APP_CLIENT)
            .build());

        if (CollectionUtils.isNotEmpty(uniAppAssets)) {
            UniAppAsset uniAppAsset = uniAppAssets.get(0);
            if (StringUtils.isNotBlank(uniAppAsset.getAppManifest())) {
                JSONObject manifest = JSON.parseObject(uniAppAsset.getAppManifest());
                if (null != manifest) {
                    String host = "https://pre-pages-fast.m.taobao.com";
                    if (PUBLISH.equals(pubType.getPubEnv())) {
                        host = "https://pages-fast.m.taobao.com";
                    }

                    JSONObject container = manifest.getJSONObject("container");
                    if (null != container) {
                        JSONArray pages = container.getJSONArray("pages");
                        if (null != pages) {
                            for (int i = 0; i < pages.size(); i++) {
                                JSONObject page = pages.getJSONObject(i);
                                String id = page.getString("id");
                                String module = page.getString("module");
                                String external = page.getString("external");
                                String url = page.getString("url");
                                String connector = hasQueryParameters(url) ? "&" : "?";

                                if (StringUtils.isNotBlank(id)) {
                                    if (Objects.nonNull(external) && Objects.equals(external, "true") && Objects.nonNull(url)) {
                                        UniAppPageResultVO vo = new UniAppPageResultVO();
                                        vo.setUrl(String.format("%s%suniapp_id=%s&uniapp_page=%s", url, connector, uniAppAsset.getApplicationId(), id));
                                        vo.setPubType(pubType);
                                        result.add(vo);
                                    } else {
                                        UniAppPageResultVO vo = new UniAppPageResultVO();
                                        vo.setUrl(String.format("%s/wow/z/uniapp/%s/%s", host, uniAppAsset.getApplicationId(), id));
                                        vo.setPubType(pubType);

                                        //List<ApplicationBO> applicationBOS = serviceFacade.getAppInnerService().findSimpleApplications(AppQuery.builder()
                                        //    .codeLibraryAddress(String.format("**************************:%s.git", module))
                                        //    .build());
                                        //
                                        //if (CollectionUtils.isNotEmpty(applicationBOS)) {
                                        //    ApplicationBO applicationBO = applicationBOS.get(0);
                                        //    vo.setApplicationName(applicationBO.getName());
                                        //    vo.setApplicationId(applicationBO.getId());
                                        //    vo.setApplicationType(applicationBO.getType());
                                        //}

                                        result.add(vo);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return result;

        //return uniAppPageService.findAllByQuery(UniAppPageQuery.builder()
        //        .monitorId(monitor.getId())
        //        .applicationType(applicationType)
        //        .pubType(pubType)
        //        .build())
        //    .stream()
        //    .map(uniAppPage -> {
        //        UniAppPageResultVO vo = new UniAppPageResultVO();
        //        vo.setUrl(uniAppPage.getPageUrl());
        //
        //        ApplicationBO applicationBO = serviceFacade.getAppInnerService().findApplication(
        //            uniAppPage.getApplicationId());
        //        vo.setApplicationName(applicationBO.getName());
        //        vo.setApplicationId(uniAppPage.getApplicationId());
        //        vo.setApplicationType(uniAppPage.getApplicationType());
        //        vo.setPubType(uniAppPage.getPubType());
        //
        //        return vo;
        //    }).collect(Collectors.toList());
    }

    @ApiOperation(value = "获取UniApp离线推送优先级")
    @GetMapping("/fetchUniAppPushPriority")
    @ResponseBody
    public WebResult<String> fetchUniAppPushPriority(@RequestParam Long applicationId) {
        return WebResult.ok(serviceFacade.getConfigurationService().findConfiguration(EntityType.APPLICATION,
            applicationId, ConfigType.APPLICATION_PUBLISH_CONFIG, ConfigAttributeName.UNI_APP_PUSH_PRIORITY).getData());
    }

    @ApiOperation(value = "构建回调")
    @PostMapping("/buildCallback")
    @ResponseBody
    public WebResult<Boolean> buildCallback(@RequestBody JSONObject body) {
        log.info(">>>buildCallback:{}", body.toString());
        executorService.submit(() -> {
            try {
                uniAppExecutor.callbackFromO2(body);
            } catch (Exception e) {
                log.error("buildCallback error", e);
            }
        });
        return WebResult.ok(true);
    }

    @ApiOperation(value = "根据O2迭代Id获取Manifest内容")
    @GetMapping("/fetchManifestByO2IterationId")
    @ResponseBody
    public WebResult<UniAppManifestMeta> fetchManifestByO2IterationId(@RequestParam Long o2IterationId,
        @RequestParam(required = false) String pubEnv) {
        try {
            UniAppManifestMeta manifestMeta = uniAppAssembler.fetchManifestByO2IterationId(o2IterationId, pubEnv);
            log.error(">>>fetchManifestByO2IterationId:{}", JSON.toJSONString(manifestMeta));
            return WebResult.ok(manifestMeta);
        } catch (Exception e) {
            return WebResult.err(ErrorCode.SYSTEM_ERROR.getErrorNo(), ErrorCode.SYSTEM_ERROR.getErrorCode(),
                e.getMessage());
        }
    }

    @ApiOperation(value = "根据O2 FlowId获取Manifest内容")
    @GetMapping("/fetchManifestByO2FlowId")
    @ResponseBody
    public WebResult<UniAppManifestMeta> fetchManifestByO2FlowId(@RequestParam Long flowId) {
        try {
            UniAppManifestMeta manifestMeta = uniAppAssembler.fetchManifestByO2FlowId(flowId);
            log.error(">>>fetchManifestFromO2FlowId:{}", JSON.toJSONString(manifestMeta));
            return WebResult.ok(manifestMeta);
        } catch (Exception e) {
            return WebResult.err(ErrorCode.SYSTEM_ERROR.getErrorNo(), ErrorCode.SYSTEM_ERROR.getErrorCode(),
                e.getMessage());
        }
    }

    @ApiOperation(value = "根据变更单Id获取Manifest内容")
    @GetMapping("/fetchManifestFromAlterSheetId")
    @ResponseBody
    public WebResult<UniAppManifestMeta> fetchManifestFromAlterSheetId(@RequestParam Long alterSheetId,
        @RequestParam(required = false) String pubEnv) {
        try {
            UniAppManifestMeta manifestMeta = uniAppAssembler.fetchManifestFromAlterSheetId(alterSheetId, pubEnv);
            log.error(">>>fetchManifestFromAlterSheetId:{}", JSON.toJSONString(manifestMeta));
            return WebResult.ok(manifestMeta);
        } catch (Exception e) {
            return WebResult.err(ErrorCode.SYSTEM_ERROR.getErrorNo(), ErrorCode.SYSTEM_ERROR.getErrorCode(),
                e.getMessage());
        }
    }

    @ApiOperation(value = "根据迭代Id获取Manifest内容")
    @GetMapping("/fetchManifestFromIterationId")
    @ResponseBody
    public WebResult<UniAppManifestMeta> fetchManifestFromIterationId(@RequestParam Long iterationId,
        @RequestParam(required = false) String pubEnv) {
        try {
            UniAppManifestMeta manifestMeta = uniAppAssembler.fetchManifestFromIterationId(iterationId, pubEnv);
            log.error(">>>fetchManifestFromIterationId:{}", JSON.toJSONString(manifestMeta));
            return WebResult.ok(manifestMeta);
        } catch (Exception e) {
            return WebResult.err(ErrorCode.SYSTEM_ERROR.getErrorNo(), ErrorCode.SYSTEM_ERROR.getErrorCode(),
                e.getMessage());
        }
    }

    @ApiOperation(value = "测试创建CF")
    @GetMapping("/testCreateCf")
    @ResponseBody
    public WebResult<Boolean> testCreateCf(@RequestParam Long alterSheetId, @RequestParam String operator) {
        uniAppPublisher.testCreateUniappCf(alterSheetId, operator);
        return WebResult.ok(Boolean.TRUE);
    }

    /**
     * 判断给定的URL字符串是否含有查询参数。
     *
     * @param urlString URL字符串
     * @return 如果URL包含查询参数，则返回true；否则返回false。
     * @throws Exception 当URL格式不正确时抛出异常。
     */
    public static boolean hasQueryParameters(String urlString) {
        URL url = null;
        try {
            url = new URL(urlString);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        String query = url.getQuery();
        return query != null;
    }
}
