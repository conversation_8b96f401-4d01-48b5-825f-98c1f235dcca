package com.alibaba.emas.mtl4.services.dev.alter.model.vo;

import java.util.Date;
import java.util.List;

import com.alibaba.emas.mtl4.services.dev.api.enums.AlterReason;
import com.alibaba.emas.mtl4.services.dev.api.enums.AlterSheetMode;
import com.alibaba.emas.mtl4.services.dev.api.enums.AlterSheetStatus;
import com.alibaba.emas.mtl4.services.dev.api.enums.AlterSheetType;
import com.alibaba.emas.mtl4.services.dev.api.model.AlterSheetExtraInfo;
import com.alibaba.emas.mtl4.services.dev.api.model.AlterSheetModuleBO;
import com.alibaba.emas.mtl4.services.dev.api.model.ApplicationBO;
import com.alibaba.emas.mtl4.services.dev.api.model.BaseDependencyBO;
import com.alibaba.emas.mtl4.services.dev.api.model.CollaborationSpaceBO;
import com.alibaba.emas.mtl4.services.dev.api.model.RequirementRelationBO;
import com.alibaba.emas.mtl4.services.dev.api.model.SubVersionBO;

import lombok.Data;

@Data
public class AlterSheetVO {
    private Long id;
    private Date gmtCreate;
    private Date gmtModified;
    private String creator;
    private String modifier;
    private Long applicationId;
    private Long integrateAreaId;
    private String integrateAreaName;
    private String branch;
    private ApplicationBO application;
    private String name;
    private AlterSheetStatus status;
    private AlterSheetType type;
    private AlterSheetMode mode;
    private String description;
    private String developers;
    private String testers;
    private String testOwner;
    private String releaseOwner;

    /**
     * 变更单模块列表
     */
    private List<AlterSheetModuleVO> alterSheetModuleList;

    /**
     * 关联需求列表
     */
    private List<RequirementRelationBO> requirementRelationList;

    /**
     * 关联的研发协作空间id
     */
    private Long collaborationSpaceId;

    /**
     * 关联的发布协作空间id
     */
    private Long releaseSpaceId;

    /**
     * 关联的研发协作空间
     */
    private CollaborationSpaceBO collaborationSpace;

    /**
     * 被复制的变更单id
     */
    private Long copiedAlterSheetId;

    /**
     * 是否复制流水线配置
     */
    private boolean copyPipelineConfig;

    /**
     * 来源变更单id
     */
    private Long sourceAlterSheetId;

    /**
     * 动态化变更单指定的发布单id(动态化变更专用，其他为空)
     */
    private Long targetReleaseId;

    /**
     * 动态化变更单指定的发布单版本(动态化变更单专用，其他为空)
     */
    private String targetReleaseVersion;

    /**
     * 多目标发布单id
     */
    private List<Long> targetReleaseIdList;

    /**
     * 动态化变更单指定的发布单版本(动态化变更单专用，其他为空)
     */
    private List<String> targetReleaseVersionList;

    /**
     * 多版本关联关系
     */
    private List<SubVersionBO> subVersionBOList;

    /**
     * 关联的版本计划
     */
    private Long versionPlanId;

    /**
     * 基础依赖
     */
    private BaseDependencyBO baseDependency;

    /**
     * 额外信息
     */
    private AlterSheetExtraInfo extraInfo;

    /**
     * 变更原因: 动态化专用
     */
    private AlterReason alterReason;

    /**
     * 动态化变更专用：标识变更分组信息
     */
    private String groupId;

    /**
     * 版本号
     */
    private String version;

    /**
     * 发布日期
     */
    private Date publishDate;

    /**
     * 是否自动创建
     */
    private boolean automated;

    /**
     * 迭代结束代码是否回流
     */
    private boolean codeReflow;

    private Long o2IterationId;

    private String o2Version;

    private boolean dynamicAlter;

    private boolean dynamicMulVersionModel;
}
