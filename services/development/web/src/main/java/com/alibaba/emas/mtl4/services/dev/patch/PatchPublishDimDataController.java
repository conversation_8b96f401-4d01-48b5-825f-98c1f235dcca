package com.alibaba.emas.mtl4.services.dev.patch;

import com.alibaba.buc.sso.client.util.SimpleUserUtil;
import com.alibaba.buc.sso.client.vo.BucSSOUser;
import org.apache.commons.collections4.CollectionUtils;
import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.commons.utils.WebResult;
import com.alibaba.emas.mtl4.patch.valueobject.release.PatchReleaseType;
import com.alibaba.emas.mtl4.services.dev.api.enums.PlatformType;
import com.alibaba.emas.mtl4.services.dev.publish.model.DimData;
import com.alibaba.emas.mtl4.services.dev.publish.model.PublishError;
import com.alibaba.emas.mtl4.services.dev.publish.service.DimDefineService;
import com.alibaba.emas.mtl4.services.dev.publish.service.PublishDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Author: qiulibin
 * @Date: 2020/2/24 9:58 PM
 * 说明：发布系统维表条件数据（这里返回的是维度下面的维度值列表）
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/patch/publish/data/dim/values/")
@Api
public class PatchPublishDimDataController {

    @Autowired
    private DimDefineService dimDefineService;
    @Autowired
    private PublishDataService publishDataService;

    @ApiOperation(value = "获取维度值列表")
    @GetMapping("/getDimDatas")
    public WebResult<List<DimData>> getDimDatas(@RequestParam Long productId, HttpServletRequest request) {
        try {
            BucSSOUser bucUser = SimpleUserUtil.getBucSSOUser(request);
            PlatformType platformType = publishDataService.getPlatform(productId);
            if (platformType == null) {
                return WebResult.err(1, PublishError.c100011, PublishError.v100011);
            }

            List<DimData> dimDatas = new ArrayList<>();
            if (platformType.equals(PlatformType.ANDROID)) {
                // android得维表数据
                BizResult<DimData> channelData = dimDefineService.getDefaultChannels();
                if (channelData.isSuccess()) {
                    dimDatas.add(channelData.getData());
                }

                //Long startTime = System.currentTimeMillis();
                //BizResult<DimData> areaData = dimDefineService.getArea();
                //if (areaData.isSuccess()) {
                //    dimDatas.add(areaData.getData());
                //}
                //log.error("get area spend time " + (System.currentTimeMillis() - startTime) + "ms");

                //startTime = System.currentTimeMillis();
                //BizResult<DimData> brandData = dimDefineService.getBrandAndOs(platformType);
                //if (brandData.isSuccess()) {
                //    dimDatas.add(brandData.getData());
                //}
                //log.error("get brand and os spend time " + (System.currentTimeMillis() - startTime) + "ms");

                BizResult<DimData> apiLevelData = dimDefineService.getDefaultApiLevel(platformType);
                if (apiLevelData.isSuccess()) {
                    dimDatas.add(apiLevelData.getData());
                }

                BizResult<DimData> osVersionsData = dimDefineService.getOsVersions(platformType);
                if (osVersionsData.isSuccess()) {
                    DimData dimData = osVersionsData.getData();
                    dimData.setDefaultValues(Collections.emptyList());
                    dimData.setDefaultValueType(null);
                    dimDatas.add(dimData);
                }

//                BizResult<DimData> experiment = dimDefineService.getABExperiment(productId, bucUser.getEmpId());
//                if (experiment.isSuccess()) {
//                    dimDatas.add(experiment.getData());
//                }

                BizResult<DimData> deviceModel = dimDefineService.getDeviceModel();
                if (deviceModel.isSuccess()) {
                    dimDatas.add(deviceModel.getData());
                }
            } else if (platformType.equals(PlatformType.IOS)) {
                // iOS 得为表示数据
                BizResult<DimData> channelData = dimDefineService.getDefaultChannels();
                if (channelData.isSuccess()) {
                    dimDatas.add(channelData.getData());
                }
                BizResult<DimData> areaData = dimDefineService.getArea();
                if (areaData.isSuccess()) {
                    dimDatas.add(areaData.getData());
                }

                BizResult<DimData> osVersionsData = dimDefineService.getOsVersions(platformType);
                if (osVersionsData.isSuccess()) {
                    DimData dimData = osVersionsData.getData();
                    dimData.setDefaultValues(Collections.emptyList());
                    dimData.setDefaultValueType(null);
                    dimDatas.add(dimData);
                }

                BizResult<DimData> deviceModel = dimDefineService.getDeviceModel();
                if (deviceModel.isSuccess()) {
                    dimDatas.add(deviceModel.getData());
                }

                /**
                 * ios 需要这个么？
                 */
//                BizResult<DimData> brandData = dimDefineService.getBrandAndOs(platformType);
//                if (brandData.isSuccess()){
//                    dimDatas.add(brandData.getData());
//                }
//                BizResult<DimData> testflightData = dimDefineService.getIsInstallTestflight();
//                if (testflightData.isSuccess()) {
//                    dimDatas.add(testflightData.getData());
//                }
            }
            return WebResult.ok(dimDatas);
        } catch (Exception e) {
            log.error("get dim datas error ", e);
        }
        return WebResult.err(1, PublishError.c100002, PublishError.v100002);
    }
}
