package com.alibaba.emas.mtl4.services.dev.application;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class StaticDiamondConfig {

    private static final Logger logger = LoggerFactory.getLogger(StaticDiamondConfig.class);

    @Value("${static.cdn.url}")
    private String cdnUrl;

    @Value("${static.project.group}")
    private String projectGroup;

    @Value("${static.project.name}")
    private String projectName;

    @Value("${static.project4mc.group}")
    private String projectGroup4mc;

    @Value("${static.project4mc.name}")
    private String projectName4mc;

    @Value("${static.project4Mobile.group}")
    private String projectGroup4Mobile;

    @Value("${static.project4Mobile.name}")
    private String projectName4Mobile;

    @Value("${static.projectCli.group}")
    private String projectGroup4Cli;

    @Value("${static.projectCli.name}")
    private String projectName4Cli;

    @Value("${static.diamond.dataId}")
    private String diamondDataId;

    @Value("${static.diamond.group}")
    private String diamondGroup;

    private String version;

    private String version4mc;

    private String version4mc2;

    private String version4mobile;

    private String version4Cli;

    private String ng4mc;

    private String ng4mc2;

    private String[] ngDeps;

    @PostConstruct
    public void initConfig() {
        String configInfo = null;
        try {
            configInfo = Diamond.getConfig(diamondDataId, diamondGroup, 1000);
            logger.error("initConfig, dataId+group:" + configInfo);
            if (StringUtils.isNotBlank(configInfo)) {
                JSONObject jsonObject = JSON.parseObject(configInfo);
                version = jsonObject.getString("version");
                version4mc = jsonObject.getString("version4mc");
                version4mc2 = jsonObject.getString("version4mc2");
                version4mobile = jsonObject.getString("version4mobile");
                version4Cli = jsonObject.getString("version4Cli");
                ng4mc = jsonObject.getString("ng4mc");
                ng4mc2 = jsonObject.getString("ng4mc2");
                JSONArray jsonArray = jsonObject.getJSONArray("ngDeps");
                ngDeps = jsonArray.toArray(new String[jsonArray.size()]);
                logger.error("DiamondConfig>>initConfig>>version:" + version);
            }
        } catch (Exception e1) {
            logger.error("initConfig error, configInfo:" + configInfo, e1);
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(diamondDataId, diamondGroup,
                new ManagerListenerAdapter() {
                    @Override
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            if (StringUtils.isNotBlank(configInfo)) {
                                JSONObject jsonObject = JSON.parseObject(configInfo);
                                logger.error("diamond update, dataId+group:" + configInfo);
                                version = jsonObject.getString("version");
                                version4mc = jsonObject.getString("version4mc");
                                version4mc2 = jsonObject.getString("version4mc2");
                                version4mobile = jsonObject.getString("version4mobile");
                                version4Cli = jsonObject.getString("version4Cli");
                                ng4mc = jsonObject.getString("ng4mc");
                                ng4mc2 = jsonObject.getString("ng4mc2");

                                JSONArray jsonArray = jsonObject.getJSONArray("ngDeps");
                                ngDeps = jsonArray.toArray(new String[jsonArray.size()]);
                                logger.error("DiamondConfig>>diamond update>>version:" + version);
                            }
                        } catch (Exception e) {
                            logger.error("update config error, configInfo:" + configInfo, e);
                        }
                    }
                });
    }

    public String getMCVersion () {
        return version4mc;
    }

    public String getMC2Version() {
        return version4mc2;
    }

    public String getCDNUrlPrefix() {
        return "//" + cdnUrl + "/" + projectGroup + "/" + projectName + "/" + version ;
    }

    public String getMcCDNUrlPrefix() {
        return "//" + cdnUrl + "/" + projectGroup4mc + "/" + projectName4mc + "/" + version4mc ;
    }

    public String getMc2CDNUrlPrefix() {
        return "//" + cdnUrl + "/" + projectGroup4mc + "/" + projectName4mc + "/" + version4mc2 ;
    }

    public String getCliCDNUrlPrefix(){
        return "//" + cdnUrl + "/" + projectGroup4Cli + "/" + projectName4Cli + "/" + version4Cli ;
    }

    public String getMobileCDNUrlPrefix() {
        return "//" + cdnUrl + "/" + projectGroup4Mobile + "/" + projectName4Mobile + "/" + version4mobile ;
    }

    public String getNG4MCCDNUrlPrefix() {
        return "//" + cdnUrl + "/code/npm/@ali/mc-web/" + ng4mc;
    }

    public String getNG4MC2CDNUrlPrefix() {
        return "//" + cdnUrl + "/code/npm/@ali/mc-web/" + ng4mc2 ;
    }

    public String getNG4MCVersion() {
        return ng4mc;
    }

    public String getNG4MC2Version() {
        return ng4mc2;
    }

    public String[] getNGDeps() {
        return ngDeps;
    }
}
