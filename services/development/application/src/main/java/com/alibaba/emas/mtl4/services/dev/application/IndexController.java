package com.alibaba.emas.mtl4.services.dev.application;

import com.alibaba.boot.velocity.annotation.VelocityLayout;
import com.alibaba.emas.mtl4.services.dev.user.User;
import com.alibaba.emas.mtl4.services.dev.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

@Controller
@Slf4j
public class IndexController {

    @Autowired
    private StaticDiamondConfig staticDiamondConfig;

    @Autowired
    private MicroModuleDiamondConfig microModuleDiamondConfig;

    @Autowired
    private UniAppDiamondConfig uniAppDiamondConfig;

    @Autowired
    UserService userService;

    private static final List<String> MC_DOMAIN = Arrays.asList(
            "mtl-cloud.alibaba.net",
            "mc.alibaba-inc.com",
            "dev-mtl4.taobao.net",
            "pre-mc.alibaba-inc.com"
    );

    private static final List<String> CLI_DOMAIN = Arrays.asList(
            "cli.alibaba.net",
            "pre-cli.alibaba-inc.com",
            "cli.alibaba-inc.com"
    );

    @GetMapping("/")
    @VelocityLayout("/templates/layout/index.vm")
    public String index(Model model, HttpServletRequest request) {
        String cdnUrlPrefix = staticDiamondConfig.getCDNUrlPrefix();
        String serverName = request.getServerName();
        String mcVersion = staticDiamondConfig.getMCVersion();
        String ngVersion = staticDiamondConfig.getNG4MCVersion();
        String[] ngDeps = staticDiamondConfig.getNGDeps();

        User user;
        Boolean isNG = true;
        Boolean isMTL4 = true;
        try {
            user = userService.getCurrentUser(request);

            // 获取要切换的部门ID列表
            //String toFind = user.getBuDeptNo();
            //isNG = Arrays.stream(ngDeps).anyMatch(toFind::equals);
        } catch (Exception e) {
            user = new User();
        }

        // 如果有读取到 Cookie，就用 Cookie 的来覆盖，用于老版本功能的回归
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("_mc_ng".equals(cookie.getName())) {
                    isNG = "true".equals(cookie.getValue());
                    break;
                }
            }
        }

        // AI 场景会有老版本页面，因为要使用 AI 功能打开新版本的场景，这里通过 Query 参数强制开启，不用 Cookie
        String ngQuery = request.getParameter("ng");
        if (ngQuery != null && "true".equals(ngQuery)) {
            isNG = true;
        }

        try {
            model.addAttribute("user", user);

            if (MC_DOMAIN.contains(serverName)) {
                model.addAttribute("microModuleInfo", microModuleDiamondConfig.getMicroModuleInfo());
                if (isNG) {
                    cdnUrlPrefix = staticDiamondConfig.getNG4MCCDNUrlPrefix();
                } else {
                    cdnUrlPrefix = staticDiamondConfig.getMcCDNUrlPrefix();
                }

                isMTL4 = false;
            } else if ("pre-mc2.alibaba-inc.com".equals(serverName)) {
                model.addAttribute("microModuleInfo", microModuleDiamondConfig.getMicroModuleInfo());
                if (isNG) {
                    cdnUrlPrefix = staticDiamondConfig.getNG4MC2CDNUrlPrefix();
                } else {
                    cdnUrlPrefix = staticDiamondConfig.getMc2CDNUrlPrefix();
                }
                mcVersion = staticDiamondConfig.getMC2Version();
                ngVersion = staticDiamondConfig.getNG4MC2Version();


                isMTL4 = false;
            } else if (CLI_DOMAIN.contains(serverName)){
                cdnUrlPrefix = staticDiamondConfig.getCliCDNUrlPrefix();
                model.addAttribute("isCli", true);
            }

        } catch (Exception e) {
            log.error("index error", e);
        }

        model.addAttribute("CDNUrlPrefix", cdnUrlPrefix);
        model.addAttribute("isNG", isNG && !isMTL4);
        model.addAttribute("mcVersion", mcVersion);
        model.addAttribute("ngVersion", ngVersion);

        return "index";
    }

    @GetMapping("/mobile")
    @VelocityLayout("/templates/layout/mobile.vm")
    public String mobileIndex(Model model, HttpServletRequest request) {
        try {
            User user = userService.getCurrentUser(request);
            model.addAttribute("user", user);
            model.addAttribute("CDNUrlPrefix", staticDiamondConfig.getMobileCDNUrlPrefix());
        } catch (Exception e) {
            log.error("mobileIndex error", e);
        }

        return "index";
    }

    @GetMapping("/uniapp")
    @VelocityLayout("/templates/layout/uniapp.vm")
    public String uniappIndex(Model model, HttpServletRequest request) {
        try {
            User user = userService.getCurrentUser(request);
            model.addAttribute("user", user);
            model.addAttribute("CDNUrlPrefix", uniAppDiamondConfig.getCDNUrlPrefix());
        } catch (Exception e) {
            log.error("uniappIndex error", e);
        }
        return "index";
    }
}
