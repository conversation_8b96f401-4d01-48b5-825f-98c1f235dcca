package com.alibaba.emas.mtl4.services.dev.module;

import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.services.dev.collaboration.model.CollaborationSpaceModuleSize;
import com.alibaba.emas.mtl4.services.dev.collaboration.query.CollaborationSpaceModuleSizeQuery;
import com.alibaba.emas.mtl4.services.dev.module.command.ModuleSizeListQry;
import com.alibaba.emas.mtl4.services.dev.module.dto.ModuleSizeDTO;
import com.alibaba.emas.mtl4.services.dev.module.model.ModuleDeliveryEfficiency;
import com.alibaba.motu.utils.result.Page;

import java.util.List;
import java.util.Optional;

public interface ModuleSizeService {

    //void recordTeamSpaceModuleSizeQuota(Long spaceId, Double quota, String userId);
    //
    //Double getTeamSpaceModuleSizeQuota(Long spaceId);

    //void recordTeamSpaceModuleSizeBalance(Long spaceId, Long balance, String userId);
    //
    //Long getTeamSpaceModuleSizeBalance(Long spaceId);

    void recordModuleOptimizationSuggestions(Long moduleId, String value, String userId);

    void recordModuleDeprecationSuggestions(Long moduleId, String value, String userId);

    Long recordSpaceModuleSizeWhenReleasePublished(Long spaceId, Long appId, String appVersion, Long releaseId, Long moduleSize, Long moduleNum);

    Long recordSpaceModuleSizeWhenGateExecuted(Long spaceId, Long appId, Long versionPlanId, Long moduleSize, Long moduleNum, Long spaceBalanceDiff, Long spaceBalance, CollaborationSpaceModuleSize spaceBalanceBaseline);

    String applyChangeSpaceModuleSizeBalance(Long spaceId, Long appId, Long balanceValue, String reason, String operator);

    Long changeSpaceModuleSizeBalance(Long spaceId, Long appId, Long balanceValue, String operator);

    CollaborationSpaceModuleSize getSpaceLastReleaseModuleSize(Long spaceId, Long appId);

    List<CollaborationSpaceModuleSize> getSpaceLastReleaseModuleSizes(Long spaceId);

    Integer updateReleaseModuleSizeBalance(CollaborationSpaceModuleSizeQuery query, Long balance);

    Optional<CollaborationSpaceModuleSize> getSpaceReleaseModuleSize(Long spaceId, Long appId, Long releaseId);

    Optional<CollaborationSpaceModuleSize> getSpaceReleaseModuleSize(Long spaceId, Long appId, String appVersion);

    Optional<CollaborationSpaceModuleSize> getSpaceVersionPlanModuleSize(Long spaceId, Long appId, Long versionPlanId);

    Page<CollaborationSpaceModuleSize> querySpaceModuleSize(CollaborationSpaceModuleSizeQuery query, Integer pageNum, Integer pageSize);

    List<Long> deleteSpaceModuleSize(CollaborationSpaceModuleSizeQuery query);

    void deleteBySpaceIdAndReleaseId(Long spaceId, Long releaseId);

    BizResult<List<ModuleSizeDTO>> listSpaceModuleSize(ModuleSizeListQry query);

    /**
     * 查询唯一归属模块所在业务空间或者团队空间的包大小
     * @param spaceId
     * @param appId
     * @param versionPlanId
     * @return
     */
    List<CollaborationSpaceModuleSize> getOwnedModuleBizSpaceOrDefaultTeamModuleSizes(Long spaceId, Long appId,
                                                                                      Long versionPlanId, String empId);

    /**
     * 查询团队空间下所有业务分组的包大小
     */
    List<CollaborationSpaceModuleSize> getAllBizSpaceModuleSizeOfTeamSpace(Long spaceId, Long appId, Long versionPlanId);


   static ModuleDeliveryEfficiency calculateDeliveryEfficiency(Boolean isDynamic, Long moduleSizeBalance) {
       if (null == moduleSizeBalance) {
           moduleSizeBalance = 0L;
       }
       if (Boolean.TRUE.equals(isDynamic)) {
           return ModuleDeliveryEfficiency.EXTREME;
       } else if (moduleSizeBalance > 0) {
           return ModuleDeliveryEfficiency.HIGH;
       } else {
           return ModuleDeliveryEfficiency.LOW;
       }
   }

    Optional<CollaborationSpaceModuleSize> querySpaceModuleSizeById(Long id);

    void deleteByAppIdAndVersionPlanId(Long applicationId, Long versionPlanId);
}
