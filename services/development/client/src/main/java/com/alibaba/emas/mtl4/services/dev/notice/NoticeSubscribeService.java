package com.alibaba.emas.mtl4.services.dev.notice;

import com.alibaba.emas.mtl4.services.dev.notice.enums.NoticeItem;
import com.alibaba.emas.mtl4.services.dev.notice.enums.NoticeType;
import com.alibaba.emas.mtl4.services.dev.notice.model.NoticeSubscribeBO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-03-30
 */
public interface NoticeSubscribeService {


    /**
     * 保存消息是否订阅
     * @param noticeSubscribeBO
     * @return
     */
    Long subscribeNotice(NoticeSubscribeBO noticeSubscribeBO);

    /**
     * 批量保存消息是否订阅
     * @param noticeSubscribeBOList
     * @return
     */
    List<Long> batchSubscribeNotice(List<NoticeSubscribeBO> noticeSubscribeBOList);

    /**
     * 获取用户退订消息列表
     * @param empId
     * @return
     */
    List<NoticeSubscribeBO> getNoticeUnsubscribeList(String empId);


    /**
     * 获取用户针对特定消息的退订记录
     * @param item
     * @param type
     * @param empId
     * @return
     */
    NoticeSubscribeBO getNoticeUnsubscribe(NoticeItem item, NoticeType type, String empId);

    /**
     * 批量获取用户退订消息列表
     * @param item
     * @param type
     * @param empIds
     * @return
     */
    List<NoticeSubscribeBO> getNoticeUnsubscribeList(NoticeItem item, NoticeType type, List<String> empIds);
}
