package com.alibaba.emas.mtl4.patch.open.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.patch.dto.publish.area.PatchPublishArea;
import com.alibaba.emas.mtl4.patch.dto.publish.area.PatchPublishAreaCreate;
import com.alibaba.emas.mtl4.patch.dto.publish.area.PatchPublishAreaUpdate;
import com.alibaba.emas.mtl4.patch.dto.publish.area.PublishAreaQuery;
import com.alibaba.emas.mtl4.patch.open.PatchPublishAreaOpenService;
import com.alibaba.emas.mtl4.patch.service.PatchPublishAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/11/4
 */
@HSFProvider(serviceInterface = PatchPublishAreaOpenService.class)
public class PatchPublishAreaOpenServiceImpl implements PatchPublishAreaOpenService {


    @Autowired
    PatchPublishAreaService patchPublishAreaService;

    @Override
    public BizResult<Long> create(@Valid PatchPublishAreaCreate create) {
        Long id = patchPublishAreaService.create(create);
        return BizResult.success(id);
    }

    @Override
    public BizResult<Void> update(@Valid PatchPublishAreaUpdate update) {
        patchPublishAreaService.update(update);
        return BizResult.success(null);
    }

    @Override
    public BizResult<PatchPublishArea> findById(@NotNull(message = "发布区ID不能为空") Long id) {
        PatchPublishArea patchPublishArea = patchPublishAreaService.findById(id);
        return BizResult.success(patchPublishArea);
    }

    @Override
    public BizResult<Page<PatchPublishArea>> findByQuery(PublishAreaQuery query, Pageable pageable) {
        Page<PatchPublishArea> patchPublishAreaPage = patchPublishAreaService.findByQuery(query, pageable);
        return BizResult.success(patchPublishAreaPage);
    }

    @Override
    public BizResult<PatchPublishArea> findByCrId(@NotNull(message = "变更单ID不能为空") Long crId) {
        PatchPublishArea patchPublishArea = patchPublishAreaService.findByCrId(crId);
        return BizResult.success(patchPublishArea);
    }
}
