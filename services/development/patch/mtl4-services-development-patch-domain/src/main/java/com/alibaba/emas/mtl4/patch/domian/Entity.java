package com.alibaba.emas.mtl4.patch.domian;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 聚合内的实体
 * <AUTHOR>
 * @date 2020/4/28
 */
@Getter
@Setter
public abstract class Entity {


    protected Long id;

    /**
     * 创建时间
     */
    protected Date gmtCreate;

    /**
     * 修改时间
     */
    protected Date gmtModified;

    /**
     * 创建者
     */
    protected String creator;

    /**
     * 修改者
     */
    protected String modifier;



    /**
     * 扩展字段
     */
    protected Map<String, Object> extValues = new ConcurrentHashMap<String, Object>();

    public<T> T getExtField(String key){
        if(extValues != null){
            return (T)extValues.get(key);
        }
        return null;
    }

    public void putExtField(String fieldName, Object value){
        this.extValues.put(fieldName, value);
    }



}
