package com.alibaba.emas.mtl4.patch.domian.validator.release.validator;

import org.apache.commons.collections4.CollectionUtils;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.patch.domian.entity.PatchPublishBatchE;
import com.alibaba.emas.mtl4.patch.domian.validator.ValidatorI;
import com.alibaba.emas.mtl4.patch.service.PatchReleaseService;
import com.alibaba.emas.mtl4.patch.valueobject.TargetApp;
import com.alibaba.emas.mtl4.services.dev.api.enums.PlatformType;
import com.alibaba.emas.mtl4.services.dev.publish.enums.CrowdType;
import com.alibaba.emas.mtl4.services.dev.publish.model.CrowdConditionConfig;
import com.alibaba.emas.mtl4.services.dev.publish.model.CrowdConditionValue;
import com.alibaba.emas.mtl4.services.dev.publish.model.CrowdConfig;
import com.alibaba.emas.mtl4.services.dev.publish.model.WhiteListConfig;
import com.alibaba.emas.publish.meta.constant.DeviceInfo;
import com.alibaba.emas.publish.meta.type.PublishBatchType;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/6/4
 */
@Component
public class PatchPublishBatchCrowdValidator implements ValidatorI<PatchPublishBatchE> {

    @Autowired
    PatchReleaseService patchReleaseService;

    @Override
    public void validate(PatchPublishBatchE patchPublishBatchE) {
        PublishBatchType batchType = patchPublishBatchE.getBatchType();
        if (batchType != PublishBatchType.BETA){
            return;
        }
        TargetApp targetApp = patchPublishBatchE.getPatchReleaseE().getPatchChangeRequestE().getPatchPublishAreaE().getTargetApp();
        CrowdConfig crowdConfig = patchPublishBatchE.getConfig().getCrowdConfig();
        if (crowdConfig.getCrowdType() == CrowdType.CONDITION) {
            CrowdConditionConfig conditionConfig = (CrowdConditionConfig) crowdConfig;

            //安卓应用: 人群配置必须指定apiLevel
            if (targetApp.getPlatformType() == PlatformType.ANDROID) {
                Optional<CrowdConditionValue> apiLevelCondition = conditionConfig.getConditions().stream()
                        .flatMap(condition -> condition.getValues().stream())
                        .filter(value -> value.getType() != null && value.getType().equals(DeviceInfo.API_LEVEL) && value.getValue() != null && NumberUtils.isDigits(value.getValue()))
                        .findFirst();
                Assert.isTrue(apiLevelCondition.isPresent(), "安卓应用必须指定'apiLevel'条件");
            }
        } else if (crowdConfig.getCrowdType() == CrowdType.WHITE_LIST) {
            WhiteListConfig whiteListConfig = (WhiteListConfig) crowdConfig;
            Assert.notNull(whiteListConfig.getType(), "白名单配置请指定字段类型");
            Assert.isTrue(CollectionUtils.isNotEmpty(whiteListConfig.getValues())
                    || whiteListConfig.getFileUrl() != null, "白名单配置请指定白名单列表或者url");
        }





    }
}
