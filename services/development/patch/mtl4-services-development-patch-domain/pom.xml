<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>mtl4-services-development-patch</artifactId>
        <groupId>com.alibaba.emas</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>mtl4-services-development-patch-domain</artifactId>
    <version>${revision}</version>
    <name>MTL4 Services :: Development :: Patch :: Domain</name>


    <dependencies>

        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-services-development-patch-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-services-development-patch-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-services-development-patch-open-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-services-development-service</artifactId>
        </dependency>

        <!-- 内存缓存 框架-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>com.taobao.pandora</groupId>
            <artifactId>pandora-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>16.0.2</version>
            <scope>compile</scope>
        </dependency>


    </dependencies>


</project>