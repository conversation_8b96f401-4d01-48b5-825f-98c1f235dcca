package com.alibaba.emas.mtl4.patch.infrastructure.tunnel.dataobject;

import com.alibaba.emas.mtl4.patch.valueobject.dependency.Dependency;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/5/27
 */
@Data
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "emas_mtl4_patch_dependency_record")
public class PatchDependencyChangeRecordDO {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    @Column(name = "dependency_id")
    private Long dependencyId;

    @Column(name = "creator")
    private String creator;

    @Column(name = "version")
    private String version;

    @Column(name = "scm_address")
    private String scmAddress;

    @Column(name = "scm_branch")
    private String scmBranch;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private Dependency.DependencyStatus status;

    @CreatedDate
    @Column(name = "gmt_create")
    private Date gmtCreate;

    @LastModifiedDate
    @Column(name = "gmt_modified")
    private Date gmtModified;


}
