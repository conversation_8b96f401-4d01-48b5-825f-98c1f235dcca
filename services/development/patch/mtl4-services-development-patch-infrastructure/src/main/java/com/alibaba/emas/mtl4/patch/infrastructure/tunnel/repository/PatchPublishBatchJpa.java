package com.alibaba.emas.mtl4.patch.infrastructure.tunnel.repository;

import com.alibaba.emas.mtl4.patch.infrastructure.tunnel.dataobject.PatchPublishBatchDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/3
 */
@Repository
public interface PatchPublishBatchJpa extends JpaRepository<PatchPublishBatchDO, Long>, JpaSpecificationExecutor<PatchPublishBatchDO> {

    /**
     * 根据发布单ID查询
     * @param releaseId
     * @return
     */
    List<PatchPublishBatchDO> findByPatchReleaseIdOrderByIdDesc(Long releaseId);
}
