package com.alibaba.emas.mtl4.services.dev.alter.domain;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "emas_mtl4_alter_sheet_module_branch")
public class AlterSheetModuleBranchDO {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "creator")
    private String creator;

    @Column(name = "modifier")
    private String modifier;

    @Column(name = "gmt_create")
    private Date gmtCreate;

    @Column(name = "gmt_modified")
    private Date gmtModified;

    @Column(name = "alter_sheet_module_id")
    private Long alterSheetModuleId;

    @Column(name = "scm_address")
    private String scmAddress;

    @Column(name = "source_branch")
    private String sourceBranch;

    @Column(name = "branch")
    private String branch;

}
