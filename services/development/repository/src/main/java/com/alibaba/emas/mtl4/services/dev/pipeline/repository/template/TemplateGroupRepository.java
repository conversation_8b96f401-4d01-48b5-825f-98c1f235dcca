package com.alibaba.emas.mtl4.services.dev.pipeline.repository.template;

import com.alibaba.emas.mtl4.services.dev.pipeline.domain.template.TemplateGroupDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-09-22
 */
@Repository
public interface TemplateGroupRepository extends JpaRepository<TemplateGroupDO, Long>, JpaSpecificationExecutor<TemplateGroupDO> {

    /**
     * 根据分组名称模糊查询
     * @param name
     * @param pageable
     * @return page
     */
    Page<TemplateGroupDO> findByNameLike(String name, Pageable pageable);

    /**
     * 根据分组名称准确查询
     * @param name
     * @return List
     */
    List<TemplateGroupDO> findByName(String name);

    /**
     * 根据分组名称列表查询
     * @param names
     * @return List
     */
    List<TemplateGroupDO> findByNameIn(List<String> names);




}
