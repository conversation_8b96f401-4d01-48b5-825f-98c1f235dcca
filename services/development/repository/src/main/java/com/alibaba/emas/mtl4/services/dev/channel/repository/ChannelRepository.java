package com.alibaba.emas.mtl4.services.dev.channel.repository;

import com.alibaba.emas.mtl4.services.dev.api.release.model.PublishWay;
import com.alibaba.emas.mtl4.services.dev.channel.domain.ChannelDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by 箫枫 on 2019/9/3.
 */
@Repository
public interface ChannelRepository extends JpaRepository<ChannelDO, Long>,
        JpaSpecificationExecutor<ChannelDO> {

    List<ChannelDO> findByIdIn(List<Long> ids);

    List<ChannelDO> findByAppKeyAndChannelNumber(String appKey,String channelNumber);

    List<ChannelDO> findByAppKeyAndPublishWayIsNull(String appKey);

    @Query(value = "select * from emas_mtl4_channel a where a.appkey = :appKey and (a.app_id is null or a.app_id != :appId)", nativeQuery = true)
    List<ChannelDO> findByAppKeyAndAppIdNot(String appKey, Long appId);

    List<ChannelDO> findByAppKeyAndIdGreaterThanAndPublishWayIsNull(String appKey, Long id);


    @Query(value = "select * from emas_mtl4_channel a where (a.appkey,a.channel_number ) in (select appkey,channel_number  from emas_mtl4_channel group by appkey,channel_number   having count(*) > 1)",
            nativeQuery = true)
    List<ChannelDO> findRepetition();

    @Query(value = "select * from emas_mtl4_channel a where (a.app_id, a.channel_number ) in (select app_id, channel_number from emas_mtl4_channel where app_id = :appId group by app_id, channel_number having count(*) > 1)",
            nativeQuery = true)
    List<ChannelDO> findRepetition(Long appId);


    boolean existsByAppIdAndPartner(Long appId,String partner);

    /**
     * 根据appId和channelNumber查找渠道数据
     * @param appId
     * @param appKey
     * @param channelNumber
     * @return List
     */
    List<ChannelDO> findByAppIdAndAppKeyAndChannelNumber(Long appId, String appKey, String channelNumber);

    /**
     * 根据appId和channelNumber查找渠道数据
     * @param appId
     * @param channelNumber
     * @return List
     */
    List<ChannelDO> findByAppIdAndChannelNumber(Long appId, String channelNumber);

    /**
     * 根据appId、id和creator查找渠道数据
     * @param appId
     * @param creator
     * @param id
     * @return List
     */
    List<ChannelDO> findByAppIdAndCreatorAndIdGreaterThan(Long appId, String creator, Long id);

    /**
     * 根据appId和发布方式查询渠道
     * @param appId
     * @param publishWay
     * @return
     */
    List<ChannelDO> findByAppIdAndPublishWay(Long appId, PublishWay publishWay);


}
