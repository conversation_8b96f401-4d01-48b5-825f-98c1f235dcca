package com.alibaba.emas.mtl4.services.dev.report.repository;

import java.util.List;

import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.report.domain.ReportRelationDO;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface ReportRelationDORepository extends JpaRepository<ReportRelationDO, Long>,
    JpaSpecificationExecutor<ReportRelationDO> {
    List<ReportRelationDO> findAllByReportId(Long reportId);
    ReportRelationDO findFirstByEntityTypeAndEntityId(EntityType entityType, Long entityId);
}
