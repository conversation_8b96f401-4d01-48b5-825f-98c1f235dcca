package com.alibaba.emas.mtl4.services.dev.remote.repository;

import com.alibaba.emas.mtl4.services.dev.remote.RemoteBatch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Transactional
public interface RemoteBatchRepository extends JpaRepository<RemoteBatch, Long>, JpaSpecificationExecutor<RemoteBatch> {

    /**
     * 根据远端化发布单id查询所有远端化批次
     * @param remoteReleaseId
     * @return
     */
    List<RemoteBatch> findByRemoteReleaseId(Long remoteReleaseId);
}
