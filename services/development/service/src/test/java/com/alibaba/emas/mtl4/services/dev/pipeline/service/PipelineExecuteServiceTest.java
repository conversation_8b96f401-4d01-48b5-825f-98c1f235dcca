package com.alibaba.emas.mtl4.services.dev.pipeline.service;

import com.alibaba.emas.mtl4.core.message.job.PipelineJobInstance;
import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.common.DetailUrlService;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineJobExecuteService;
import com.alibaba.emas.mtl4.services.dev.notice.service.ding.PipelineDingTalkNoticeService;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineRelationService;
import com.alibaba.emas.mtl4.services.dev.pipeline.domain.pipeline.PipelineInstanceDO;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineRelation;
import com.alibaba.emas.mtl4.services.dev.pipeline.repository.pipeline.PipelineInstanceRepository;
import com.alibaba.emas.mtl4.services.dev.pipeline.service.impl.PipelineExecuteServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2023-04-12
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class PipelineExecuteServiceTest {

    @InjectMocks
    private PipelineExecuteServiceImpl executeService;
    @Mock
    private PipelineJobExecuteService pipelineJobExecuteService;
    @Mock
    private PipelineInstanceRepository pipelineInstanceRepository;
    @Mock
    private PipelineRelationService pipelineRelationService;
    @Mock
    private DetailUrlService detailUrlService;

    @Test
    public void testGetJobInstanceResultLink() {
        Long pipelineJobInstanceId = 1L;
        Long pipelineInstanceId = 1L;
        PipelineJobInstance jobInstance = new PipelineJobInstance();
        jobInstance.setPipelineInstanceId(pipelineInstanceId);
        Mockito.when(pipelineJobExecuteService.findById(pipelineJobInstanceId)).thenReturn(jobInstance);

        PipelineInstanceDO pipelineInstanceDO = new PipelineInstanceDO();
        pipelineInstanceDO.setId(pipelineInstanceId);
        pipelineInstanceDO.setPipelineId(1L);
        Mockito.when(pipelineInstanceRepository.findById(pipelineInstanceId)).thenReturn(Optional.of(pipelineInstanceDO));

        PipelineRelation relation = new PipelineRelation();
        relation.setEntityType(EntityType.RELEASE);
        relation.setEntityId(1L);
        relation.setPipelineId(1L);
        Mockito.when(pipelineRelationService.queryPipelineRelationByPipelineId(1L)).thenReturn(relation);

        String linkUrl = executeService.getJobInstanceResultLink(pipelineJobInstanceId);
        Mockito.verify(detailUrlService, Mockito.times(1))
                .getPipelineInstanceDetailUrl(Mockito.any(), Mockito.any());
    }
}
