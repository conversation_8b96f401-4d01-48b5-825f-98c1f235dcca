package com.alibaba.emas.mtl4.services.dev.alter.service;

import com.alibaba.emas.mtl4.services.dev.alter.service.impl.AlterSheetModuleCodeReflowServiceImpl;
import com.alibaba.emas.mtl4.services.dev.api.model.AlterSheetBO;
import com.alibaba.emas.mtl4.services.dev.api.model.ApplicationBO;
import com.alibaba.emas.mtl4.services.dev.app.model.ModuleDeployRecordBO;
import com.alibaba.emas.mtl4.services.dev.notice.service.ding.DingRobotMessageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @Date 2022-11-28
 */
@RunWith(MockitoJUnitRunner.class)
public class AlterSheetModuleCodeReflowServiceTest {

    @InjectMocks
    private AlterSheetModuleCodeReflowServiceImpl codeReflowService;
    @Mock
    private DingRobotMessageService dingRobotMessageService;
    @Mock
    private AlterSheetService alterSheetService;

    @Test
    public void testSendCodeReflowFailedMessage() {
        ApplicationBO module = new ApplicationBO();
        module.setName("mtl_android_test1");
        module.setId(1002455L);
        ModuleDeployRecordBO moduleDeployRecordBO = new ModuleDeployRecordBO();
        moduleDeployRecordBO.setCreator("163277");
        moduleDeployRecordBO.setScmBranch("release/integrationArea_232950");

        AlterSheetBO alterSheetBO = new AlterSheetBO();
        alterSheetBO.setCollaborationSpaceId(1037338L);
        alterSheetBO.setId(239431L);
        Mockito.when(alterSheetService.findSimpleAlterSheetById(Mockito.any()))
                .thenReturn(alterSheetBO);
        codeReflowService.sendCodeReflowFailedMessage(module, moduleDeployRecordBO,
                "0111test1", 239431L);
        Mockito.verify(dingRobotMessageService, Mockito.times(1))
                .sendRobotMessage(Mockito.any(), Mockito.any());
    }

}
