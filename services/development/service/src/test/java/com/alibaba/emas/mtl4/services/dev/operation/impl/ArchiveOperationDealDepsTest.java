package com.alibaba.emas.mtl4.services.dev.operation.impl;

import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.services.dev.api.model.DepVersionProp;
import com.alibaba.emas.mtl4.services.dev.api.release.model.PublishType;
import com.alibaba.emas.mtl4.services.dev.api.service.ApplicationDependencyDetailService;
import com.alibaba.emas.mtl4.services.dev.api.service.ApplicationDependencyService;
import com.alibaba.emas.mtl4.services.dev.api.service.BaseDependencyService;
import com.alibaba.emas.mtl4.services.dev.configuration.ConfigurationInnerService;
import com.alibaba.emas.mtl4.services.dev.configuration.service.AppConfigurationService;
import com.alibaba.emas.mtl4.services.dev.dependency.gradle.DepTree;
import com.alibaba.emas.mtl4.services.dev.dependency.service.ApplicationDependencyInnerService;
import com.alibaba.emas.mtl4.services.dev.dependency.service.GradleDependencyService;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineService;
import com.alibaba.emas.mtl4.services.dev.release.domain.Release;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2023/3/13
 */
@RunWith(MockitoJUnitRunner.class)
public class ArchiveOperationDealDepsTest {

    @InjectMocks
    ArchiveOperationDealDeps archiveOperationDealDeps;

    @Mock
    ApplicationDependencyService applicationDependencyService;

    @Mock
    BaseDependencyService baseDependencyService;

    @Mock
    ApplicationDependencyInnerService appDepInnerService;

    @Mock
    ApplicationDependencyDetailService applicationDependencyDetailService;

    @Mock
    ConfigurationInnerService configurationInnerService;

    @Mock
    AppConfigurationService appConfigurationService;

    @Mock
    private GradleDependencyService gradleDependencyService;

    @Mock
    private PipelineService pipelineService;

    @Test
    public void operate() {
        Mockito.when(configurationInnerService.getSmallAppConfig(Mockito.any())).thenReturn(false);
        Mockito.when(appDepInnerService.findApplicationDependency(Mockito.anyLong(), Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(applicationDependencyService.saveApplicationDependency(Mockito.any())).thenReturn(
                BizResult.success(0L));
        Mockito.when(baseDependencyService.findDepsDetail(
                        Mockito.any(), Mockito.anyLong(), Mockito.any(), Mockito.anyString(), Mockito.anyLong()))
                .thenReturn(new HashMap<String, DepVersionProp>());
        Mockito.when(appConfigurationService.useMtlGradlePlugin(Mockito.anyLong())).thenReturn(false);
        Mockito.when(gradleDependencyService.getGradleDeps(Mockito.any())).thenReturn(new DepTree());
        Mockito.when(applicationDependencyDetailService.saveApplicationDependencyDetail(Mockito.anyList()))
                .thenReturn(BizResult.success(new ArrayList<>()));
        Mockito.when(pipelineService.findPipelineById(Mockito.anyLong())).thenReturn(MockUtil.mockPipeline());
        archiveOperationDealDeps.operate(mockRelease(), "86898");
    }

    private Release mockRelease() {
        Release release = new Release();
        release.setId(1L);
        release.setVersion("1.0");
        release.setApplicationId(1L);
        release.setIntegrationAreaId(1L);
        release.setBuildPipelineId(1L);
        release.setBuildChannelPipelineId(1L);
        release.setPublishType(PublishType.NORMAL);
        return release;
    }
}