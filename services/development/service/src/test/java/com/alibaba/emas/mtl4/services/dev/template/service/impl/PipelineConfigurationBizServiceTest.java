package com.alibaba.emas.mtl4.services.dev.template.service.impl;

import com.alibaba.emas.mtl4.core.message.task.PluginConfigs;
import com.alibaba.emas.mtl4.services.dev.ServiceFacade;
import com.alibaba.emas.mtl4.services.dev.api.enums.ApplicationType;
import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.api.enums.OperationType;
import com.alibaba.emas.mtl4.services.dev.api.model.ApplicationBO;
import com.alibaba.emas.mtl4.services.dev.api.model.OperationLogBO;
import com.alibaba.emas.mtl4.services.dev.app.service.AppInnerService;
import com.alibaba.emas.mtl4.services.dev.operationlog.service.OperationLogService;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineService;
import com.alibaba.emas.mtl4.services.dev.pipeline.domain.params.BuildParams;
import com.alibaba.emas.mtl4.services.dev.pipeline.domain.pipeline.PipelineDO;
import com.alibaba.emas.mtl4.services.dev.pipeline.domain.stage.PipelineStageDO;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.Pipeline;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineNodeExtendedConstant;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineStage;
import com.alibaba.emas.mtl4.services.dev.api.enums.stage.StageType;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.template.*;
import com.alibaba.emas.mtl4.services.dev.pipeline.repository.PipelineJobRepository;
import com.alibaba.emas.mtl4.services.dev.pipeline.repository.PipelineRepository;
import com.alibaba.emas.mtl4.services.dev.pipeline.repository.PipelineStageRepository;
import com.alibaba.emas.mtl4.services.dev.pipeline.repository.PipelineTaskRepository;
import com.alibaba.emas.mtl4.services.dev.pipeline.service.PipelineNodeExecutionOrderService;
import com.alibaba.emas.mtl4.services.dev.template.service.biz.impl.AlterSheetPipelineServiceImpl;
import com.alibaba.emas.mtl4.services.plugin.api.model.PluginInfo;
import com.alibaba.emas.mtl4.services.plugin.api.model.PluginVersion;
import com.alibaba.emas.mtl4.services.plugin.api.service.PluginVersionApi;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023-04-18
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class PipelineConfigurationBizServiceTest {

    @InjectMocks
    private AlterSheetPipelineServiceImpl impl;
    @Mock
    private PipelineService pipelineService;
    @Mock
    private OperationLogService operationLogService;
    @Mock
    private PipelineNodeExecutionOrderService executionOrderService;
    @Mock
    private PipelineRepository pipelineRepository;
    @Mock
    private PipelineJobRepository pipelineJobRepository;
    @Mock
    private PipelineStageRepository pipelineStageRepository;
    @Mock
    private PipelineTaskRepository pipelineTaskRepository;
    @Mock
    private ServiceFacade serviceFacade;
    @Mock
    private AppInnerService appInnerService;
    @Mock
    private PluginVersionApi pluginVersionApi;


    @Test
    public void testAddModuleToMixPipeline() {
        Pipeline pipeline = mockPipeline();
        Mockito.when(pipelineService.findPipelineById(Mockito.anyLong())).thenReturn(pipeline);

        PipelineTemplate pipelineTemplate = mockPipelineTemplate();

        Mockito.when(serviceFacade.getPluginVersionApi()).thenReturn(pluginVersionApi);
        PluginVersion pluginVersion = new PluginVersion();
        PluginInfo pluginInfo = new PluginInfo();
        pluginInfo.setName("git plugin");
        pluginVersion.setPluginInfo(pluginInfo);
        Mockito.when(pluginVersionApi.queryPluginVersion(141L)).thenReturn(pluginVersion);

        OperationLogBO operationLogBO = new OperationLogBO();
        Mockito.when(operationLogService.findOperationLogs(Mockito.any(EntityType.class), Mockito.anyLong(),
                Mockito.any(OperationType.class)))
                .thenReturn(Collections.singletonList(operationLogBO));

        List<Set<String>> orderedExecutionNodes = new ArrayList<>();
        orderedExecutionNodes.add(new HashSet<>(Arrays.asList("807a850362d94cb2a3dd7cb3458cfb38")));
        orderedExecutionNodes.add(new HashSet<>(Arrays.asList("807a850362d94cb2a3dd7cb3458cfb39")));
        Mockito.when(executionOrderService.getLatestOrderedNodeExecutions(Mockito.any(Map.class)))
                .thenReturn(orderedExecutionNodes);

        ApplicationBO client = new ApplicationBO();
        client.setType(ApplicationType.CLIENT);
        Mockito.when(serviceFacade.getAppInnerService()).thenReturn(appInnerService);
        Mockito.when(appInnerService.findApplication(Mockito.anyLong()))
                .thenReturn(client);

        impl.addModuleToMixPipeline("163277", pipeline.getId(), pipelineTemplate, new BuildParams());
        Mockito.verify(pipelineRepository, Mockito.times(1)).save(Mockito.any(PipelineDO.class));
    }

    @Test
    public void testRemoveModuleFromMixPipeline() {
        Pipeline pipeline = mockPipelineWhenDeleteModule();
        Mockito.when(pipelineService.findPipelineById(Mockito.anyLong())).thenReturn(pipeline);
        impl.removeModuleFromMixPipeline("163277", 1320598L, 1002455L);
        Mockito.verify(pipelineStageRepository, Mockito.times(1))
                .save(Mockito.any(PipelineStageDO.class));
    }



    private Pipeline mockPipeline() {
        Long pipelineId = 1320598L;
        Pipeline pipeline = new Pipeline();
        pipeline.setCreateTime(System.currentTimeMillis());
        pipeline.setModifiedTime(System.currentTimeMillis());
        pipeline.setId(pipelineId);

        List<PipelineStage> stages = new ArrayList<>();
        PipelineStage emptyStage = new PipelineStage();
        emptyStage.setStageType(StageType.EMPTY);
        String stageUuid = "807a850362d94cb2a3dd7cb3458cfb38";
        emptyStage.setUuid(stageUuid);
        emptyStage.setExtended(new HashMap<>(2));
        emptyStage.setFormerStages(new HashSet<>(Arrays.asList(stageUuid)));
        emptyStage.setCreateTime(System.currentTimeMillis());
        emptyStage.setModifiedTime(System.currentTimeMillis());
        stages.add(emptyStage);

        PipelineStage appStage = new PipelineStage();
        Map<String, String> extended = new HashMap<>(2);
        extended.put(PipelineNodeExtendedConstant.APP_ID, "1");
        appStage.setExtended(extended);
        appStage.setStageType(StageType.COMMON);
        String appStageUuid = "807a850362d94cb2a3dd7cb3458cfb39";
        appStage.setFormerStages(new HashSet<>(Arrays.asList(stageUuid)));
        appStage.setUuid(appStageUuid);
        appStage.setCreateTime(System.currentTimeMillis());
        appStage.setModifiedTime(System.currentTimeMillis());

        stages.add(appStage);
        pipeline.setStages(stages);

        Map<String, Set<String>> formerStageMap = new HashMap<>();
        Set<String> emptyFormerStages = new HashSet<>();
        emptyFormerStages.add(stageUuid);
        formerStageMap.put(stageUuid, emptyFormerStages);

        Set<String> appFormerStages = new HashSet<>();
        appFormerStages.add(stageUuid);
        formerStageMap.put(stageUuid, appFormerStages);
        formerStageMap.put(appStageUuid, new HashSet<>(Arrays.asList(stageUuid)));
        pipeline.setFormerStageMap(formerStageMap);

        return pipeline;
    }

    private Pipeline mockPipelineWhenDeleteModule() {
        Long pipelineId = 1320598L;
        Pipeline pipeline = new Pipeline();
        pipeline.setCreateTime(System.currentTimeMillis());
        pipeline.setModifiedTime(System.currentTimeMillis());
        pipeline.setId(pipelineId);

        List<PipelineStage> stages = new ArrayList<>();

        PipelineStage moduleStage = new PipelineStage();
        moduleStage.setStageType(StageType.COMMON);
        String moduleStageUuid = "807a850362d94cb2a3dd7cb3458cfb37";
        moduleStage.setUuid(moduleStageUuid);
        moduleStage.setExtended(new HashMap<>(2));
        moduleStage.getExtended().put(PipelineNodeExtendedConstant.APP_ID, String.valueOf(1002455L));
        moduleStage.setFormerStages(new HashSet<>(Arrays.asList(moduleStageUuid)));
        moduleStage.setCreateTime(System.currentTimeMillis());
        moduleStage.setModifiedTime(System.currentTimeMillis());
        stages.add(moduleStage);

        PipelineStage emptyStage = new PipelineStage();
        emptyStage.setStageType(StageType.EMPTY);
        String emptyStageUuid = "807a850362d94cb2a3dd7cb3458cfb38";
        emptyStage.setUuid(emptyStageUuid);
        emptyStage.setExtended(new HashMap<>(2));
        emptyStage.setFormerStages(new HashSet<>(Arrays.asList(moduleStageUuid)));
        emptyStage.setCreateTime(System.currentTimeMillis());
        emptyStage.setModifiedTime(System.currentTimeMillis());
        stages.add(emptyStage);

        PipelineStage appStage = new PipelineStage();
        Map<String, String> extended = new HashMap<>(2);
        extended.put(PipelineNodeExtendedConstant.APP_ID, "1");
        appStage.setExtended(extended);
        appStage.setStageType(StageType.COMMON);
        String appStageUuid = "807a850362d94cb2a3dd7cb3458cfb39";
        appStage.setFormerStages(new HashSet<>(Arrays.asList(emptyStageUuid)));
        appStage.setUuid(appStageUuid);
        appStage.setCreateTime(System.currentTimeMillis());
        appStage.setModifiedTime(System.currentTimeMillis());

        stages.add(appStage);
        pipeline.setStages(stages);

        Map<String, Set<String>> formerStageMap = new HashMap<>();
        formerStageMap.put(moduleStageUuid, moduleStage.getFormerStages());
        formerStageMap.put(emptyStageUuid, emptyStage.getFormerStages());
        formerStageMap.put(appStageUuid, appStage.getFormerStages());
        pipeline.setFormerStageMap(formerStageMap);

        return pipeline;
    }


    private PipelineTemplate mockPipelineTemplate() {
        PipelineJobTemplate jobTemplate = new PipelineJobTemplate();
        jobTemplate.setId(16193L);
        jobTemplate.setName("模块发布模版");

        PipelineTaskTemplate taskTemplate = new PipelineTaskTemplate();
        taskTemplate.setIsSystem(false);
        taskTemplate.setPluginId(1L);
        taskTemplate.setPluginVersionId(141L);
        taskTemplate.setPluginConfigs(new PluginConfigs());
        jobTemplate.setTaskTemplates(Collections.singletonList(taskTemplate));

        PipelineStageTemplate pipelineStageTemplate = new PipelineStageTemplate();
        pipelineStageTemplate.setName("模块发布snapshot");
        pipelineStageTemplate.setRequired(true);
        pipelineStageTemplate.setUsage(PipelineStageUsage.MODULE_BUILD);
        pipelineStageTemplate.setBuildJobTemplate(jobTemplate);

        PipelineTemplate pipelineTemplate = new PipelineTemplate();
        pipelineTemplate.setStageTemplates(Collections.singletonList(pipelineStageTemplate));

        return pipelineTemplate;
    }


}
