package com.alibaba.emas.mtl4.services.dev.notice.service.mail;

import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.services.dev.notice.model.MailboxContentVO;
import com.alibaba.emas.mtl4.services.dev.notice.model.ReceiveMessageQueryDTO;
import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class MailReceiveServiceTest {

    @InjectMocks
    private MailReceiveService mailReceiveService;

    @Mock
    private ReceiveMailProperties receiveMailProperties;

    @Before
    public void init() {
        /**
         * mail-receive:
         *   host: imap.alibaba-inc.com
         *   port: 993
         *   protocol: imap
         *   username: <EMAIL>
         *   password: Aa1234++
         */
        Mockito.when(receiveMailProperties.getHost()).thenReturn("imap.alibaba-inc.com");
        Mockito.when(receiveMailProperties.getPort()).thenReturn(993);
        Mockito.when(receiveMailProperties.getProtocol()).thenReturn("imaps");
        Mockito.when(receiveMailProperties.getUsername()).thenReturn("<EMAIL>");
        Mockito.when(receiveMailProperties.getPassword()).thenReturn("Aa1234++");
    }

    @Test
    public void getCommonMailboxMessageList() {
        ReceiveMessageQueryDTO receiveMessageQueryDTO = new ReceiveMessageQueryDTO();
        receiveMessageQueryDTO.setToAddress(Lists.newArrayList("<EMAIL>"));
        receiveMessageQueryDTO.setLimit(10);
        MailboxContentVO contentVO = mailReceiveService.getCommonMailboxMessageList(receiveMessageQueryDTO);
        Assert.isTrue(CollectionUtils.isNotEmpty(contentVO.getMessageList()));
    }

}