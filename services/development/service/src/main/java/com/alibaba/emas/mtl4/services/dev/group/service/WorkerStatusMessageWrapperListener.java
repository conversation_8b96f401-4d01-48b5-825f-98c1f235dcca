package com.alibaba.emas.mtl4.services.dev.group.service;

import com.alibaba.emas.mtl4.commons.msgcenter.ConsumerResultWrapper;
import com.alibaba.emas.mtl4.commons.msgcenter.MessageContent;
import com.alibaba.emas.mtl4.commons.msgcenter.MessageWrapper;
import com.alibaba.emas.mtl4.commons.msgcenter.MessageWrapperListener;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.plugin.core.pf4j.utils.StringUtils;
import com.alibaba.emas.mtl4.scheduler.api.WorkerClientService;
import com.alibaba.emas.mtl4.services.dev.api.model.ApplicationBO;
import com.alibaba.emas.mtl4.services.dev.api.service.AppService;
import com.alibaba.emas.mtl4.services.dev.api.service.ConfigurationService;
import com.alibaba.emas.mtl4.services.dev.notice.DingRobotMessage;
import com.alibaba.emas.mtl4.services.dev.notice.DingRobotMessageKey;
import com.alibaba.emas.mtl4.services.dev.notice.service.ding.DingRobotMessageService;
import com.alibaba.emas.mtl4.services.scheduler.enums.WorkerOnlineStatus;
import com.alibaba.emas.mtl4.services.scheduler.model.WorkerBO;
import com.alibaba.emas.mtl4.services.scheduler.model.WorkerMachineInfo;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class WorkerStatusMessageWrapperListener implements MessageWrapperListener {

    @Autowired
    DingRobotMessageService dingRobotMessageService;

    @Autowired
    DevGroupService devGroupService;

    @Autowired
    AppService appService;

    @Autowired
    ConfigurationService configurationService;

    @Autowired
    WorkerClientService workerClientService;

    private final static String MACHINE_NOTICE_TITLE = "MTL4 机器状态变化提醒";


    @Override
    public ConsumerResultWrapper consume(MessageWrapper messageWrapper, MessageContent messageContent) {
        Assert.notNull(messageWrapper);
        Assert.notNull(messageWrapper.getBody());
        Assert.notNull(messageWrapper.getTag());
        //内容
        WorkerOnlineStatus workerOnlineStatus = WorkerOnlineStatus.valueOf(messageWrapper.getTag());
        WorkerBO workerBO = JSON.parseObject(new String(messageWrapper.getBody()), WorkerBO.class);
        WorkerMachineInfo workerMachineInfo = workerClientService.getWorkerMachineInfo(workerBO.getId());
        Set<String> notifier = new HashSet<>();
        //系统管理员
        String mtl4GroupAdmin = devGroupService.getMtl4GroupAdmin();
        if (StringUtils.isNotNullOrEmpty(mtl4GroupAdmin)) {
            notifier.addAll(Arrays.asList(mtl4GroupAdmin.split(",").clone()));
        }
        Set<Long> applicationIdSet = new HashSet<>();
        if (workerBO.getTags() != null && !workerBO.getTags().isEmpty()) {
            workerBO.getTags().forEach(tag -> applicationIdSet.add(devGroupService.getApplicationIdByTag(tag)));
        }

        Set<String> applicationsName = new HashSet<>();
        Set<String> applicationsMachineAdmins = new HashSet<>();
        if (!applicationIdSet.isEmpty()) {
            BizResult<List<ApplicationBO>> applications = appService.findApplicationByIds(applicationIdSet);
            if (applications.isSuccess() && applications.getData() != null && !applications.getData().isEmpty()) {
                applications.getData().forEach(app -> {
                    applicationsName.add(app.getName());
                    applicationsMachineAdmins.addAll(getMachineAdmins(app.getId()));
                });
            }
        }
        notifier.addAll(applicationsMachineAdmins);

        DingRobotMessage dingRobotMessage = new DingRobotMessage();
        dingRobotMessage.setMsgKey(DingRobotMessageKey.SAMPLE_ACTION_CARD1);
        dingRobotMessage.setTitle(MACHINE_NOTICE_TITLE);
        dingRobotMessage.setText(generateContent(workerOnlineStatus, workerMachineInfo, applicationsName));
        dingRobotMessage.addActionTitle("查看详情");
        dingRobotMessage.addActionUrl("");
        dingRobotMessageService.sendRobotMessage(new ArrayList<>(notifier), dingRobotMessage);

        return ConsumerResultWrapper.ok();
    }

    private String generateContent(WorkerOnlineStatus workerOnlineStatus, WorkerMachineInfo workerMachineInfo, Set<String> applicationsName) {
        return String.format("有机器%s: %s %s",
                WorkerOnlineStatus.WORKER_ONLINE.equals(workerOnlineStatus) ? "上线" : "异常",
                workerMachineInfo.getMachine().getHostAddress(),
                "影响业务 " + (applicationsName.isEmpty() ? "default集群" : applicationsName));
    }

    private Set<String> getMachineAdmins(Long id) {
        Set<String> result = new HashSet<>();
        String admins = devGroupService.getAppBuildMachineAdmins(id);
        if (StringUtils.isNotNullOrEmpty(admins)) {
            result.addAll(Arrays.asList(admins.split(",")));
        }
        return result;
    }
}
