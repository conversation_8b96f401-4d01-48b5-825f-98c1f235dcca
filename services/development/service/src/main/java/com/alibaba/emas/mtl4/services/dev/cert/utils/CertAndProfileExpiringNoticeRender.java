package com.alibaba.emas.mtl4.services.dev.cert.utils;


import com.alibaba.emas.mtl4.services.dev.cert.model.IOSCert;
import com.alibaba.emas.mtl4.services.dev.cert.model.IOSProfile;
import com.alibaba.emas.mtl4.services.dev.notice.DingRobotMessage;
import com.alibaba.emas.mtl4.services.dev.notice.DingRobotMessageKey;
import com.alibaba.emas.mtl4.services.dev.notice.DingTalkUtil;

import java.text.DateFormat;
import java.text.SimpleDateFormat;

/**
 * iOS应用证书和profile即将过期通知文案渲染
 * <AUTHOR>
 * @date 2021.07.27
 */
public class CertAndProfileExpiringNoticeRender {

    /**
     * 处理证书和Profile地址
     */
    public static final String DETAIL_URL = "https://mtl4.alibaba-inc.com/#/setting/cert/iOS";
    static final DateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd hh:mm");

    public static String renderCertExpiringMailText(IOSCert iosCert) {
        return String.format(
                "<p>您好，您管理的iOS应用证书【%s】即将过期，过期时间：%s，请尽快处理。" +
                        "<a href='%s' target='_blank'>摩天轮-证书中心</a></p>" +
                        "<p>如果您不再是该应用的管理员，或有其它问题，请联系摩天轮同学钱雨。</p>",
                iosCert.getName(), DATE_FORMAT.format(iosCert.getExpirationTime()), DETAIL_URL);
    }

    public static String renderCertExpiringDingText(IOSCert iosCert) {
        return String.format(
                "您管理的iOS应用证书【%s】即将过期，过期时间：%s，请尽快处理。\n" +
                        "如果您不再是该应用的管理员，或有其它问题，请联系摩天轮同学钱雨。",
                iosCert.getName(), DATE_FORMAT.format(iosCert.getExpirationTime()));
    }

    public static DingRobotMessage renderCertExpiringDingMessage(IOSCert iosCert) {
        DingRobotMessage dingRobotMessage = new DingRobotMessage();
        dingRobotMessage.setTitle("证书过期");
        String text = renderCertExpiringDingText(iosCert);
        dingRobotMessage.setText(text);
        dingRobotMessage.setMsgKey(DingRobotMessageKey.SAMPLE_ACTION_CARD1);
        dingRobotMessage.addActionTitle("查看详情");
        dingRobotMessage.addActionUrl(DingTalkUtil.getDingTalkUrlWithoutPcSlide(DETAIL_URL));
        return dingRobotMessage;
    }

    public static String renderProfileExpiringMailText(IOSProfile iosProfile) {
        return String.format(
                "<p>您好，您管理的iOS应用profile【%s】即将过期，过期时间：%s，请尽快处理。" +
                        "<a href='%s' target='_blank'>摩天轮-证书中心</a></p>" +
                        "<p>如果您不再是该应用的管理员，或有其它问题，请联系摩天轮同学钱雨。</p>",
                iosProfile.getName(), DATE_FORMAT.format(iosProfile.getExpirationTime()), DETAIL_URL);
    }

    public static String renderProfileExpiringDingText(IOSProfile iosProfile) {
        return String.format(
                "您管理的iOS应用profile【%s】即将过期，过期时间：%s，请尽快处理。\n" +
                        "如果您不再是该应用的管理员，或有其它问题，请联系摩天轮同学钱雨。",
                iosProfile.getName(), DATE_FORMAT.format(iosProfile.getExpirationTime()));
    }

    public static DingRobotMessage renderProfileExpiringDingMessage(IOSProfile iosProfile) {
        DingRobotMessage dingRobotMessage = new DingRobotMessage();
        dingRobotMessage.setTitle("profile过期");
        String text = renderProfileExpiringDingText(iosProfile);
        dingRobotMessage.setText(text);
        dingRobotMessage.setMsgKey(DingRobotMessageKey.SAMPLE_ACTION_CARD1);
        dingRobotMessage.addActionTitle("查看详情");
        dingRobotMessage.addActionUrl(DingTalkUtil.getDingTalkUrlWithoutPcSlide(DETAIL_URL));
        return dingRobotMessage;
    }

}
