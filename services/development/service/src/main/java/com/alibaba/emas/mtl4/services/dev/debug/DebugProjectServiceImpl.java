package com.alibaba.emas.mtl4.services.dev.debug;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.commons.utils.GitUtils;
import com.alibaba.emas.mtl4.services.dev.api.model.git.GitProject;
import com.alibaba.emas.mtl4.services.dev.code.AoneCodeService;
import com.alibaba.emas.mtl4.services.dev.debug.domain.DebugProjectDO;
import com.alibaba.emas.mtl4.services.dev.debug.domain.DebugProjectRelationDO;
import com.alibaba.emas.mtl4.services.dev.debug.repository.DebugProjectDORepository;
import com.alibaba.emas.mtl4.services.dev.debug.repository.DebugProjectRelationDORepository;
import com.alibaba.emas.mtl4.services.dev.gitlab.model.GitGroup;
import com.alibaba.emas.mtl4.services.dev.gitlab.model.request.AddMemberRequest;
import com.alibaba.emas.mtl4.services.dev.gitlab.model.request.CreateProjectRequest;
import com.alibaba.emas.mtl4.services.dev.code.GitlabService;
import com.alibaba.emas.mtl4.services.dev.scaffold.model.ScaffoldCreateRequest;
import com.alibaba.emas.mtl4.services.dev.scaffold.service.**********************;
import com.alibaba.motu.utils.result.Page;

import com.google.common.base.Preconditions;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

@Component
public class DebugProjectServiceImpl implements DebugProjectService {
    @Autowired
    private DebugProjectDORepository debugProjectDORepository;

    @Autowired
    private DebugProjectRelationDORepository debugProjectRelationDORepository;

    @Autowired
    private AoneCodeService aoneCodeService;

    @Autowired
    private GitlabService gitlabService;

    @Autowired
    private ********************** scaffoldProjectService;


    @Override
    public Long createDebugProject(DebugProject project) {
        Preconditions.checkNotNull(project.getName());
        Preconditions.checkNotNull(project.getGitUrl());
        Preconditions.checkArgument(project.getId() == null);

        // 非系统模板时，新建代码库 或者 检查已有代码库，并进行代码克隆
        if (!"system".equals(project.getCreator())) {
            Preconditions.checkNotNull(project.getScaffoldId());
            Preconditions.checkNotNull(project.getGitNameSpaceId());

            DebugProject scaffold = getDebugProjectById(project.getScaffoldId());
            project.setTag(scaffold.getTag());
            project.setPlatform(scaffold.getPlatform());

            if (project.isNeedCreateGitProject()) {
                createGitProject(project);
            } else {
                checkGitProject(project);
            }
            cloneGitProject(project, scaffold);
        }

        if (StringUtils.isBlank(project.getIdentifier())) {
            project.setIdentifier(project.getGitUrl());
        }

        project.setGmtCreate(new Date());
        project.setGmtModified(new Date());
        DebugProjectDO projectDO = new DebugProjectDO();
        BeanUtils.copyProperties(project, projectDO);
        debugProjectDORepository.save(projectDO);

        if (null != project.getSpaceId()) {
            addDebugProjectRelation(DebugProjectRelation.builder()
                .creator(project.getCreator())
                .debugProjectId(projectDO.getId())
                .spaceId(project.getSpaceId())
                .gmtCreate(new Date())
                .build());
        }
        return projectDO.getId();
    }

    private void checkGitProject(DebugProject debugProject) {
        String gitUrl = debugProject.getGitUrl();
        String pathWithNameSpace = GitUtils.getPathWithNamespace(gitUrl);
        List<GitProject> gitProjects = aoneCodeService.getAuthorizedProjects(false, null, 1,
            10, pathWithNameSpace, null, null);
        Assert.isTrue(CollectionUtils.isNotEmpty(gitProjects), "代码库:" + gitUrl +
            "请添加wirelessread为管理员");
    }

    private void createGitProject(DebugProject debugProject) {
        // 提前校验wirelessread的权限
        String nameSpace = GitUtils.getNamespace(debugProject.getGitUrl());
        List<GitGroup> gitGroups = aoneCodeService.getGitGroups(null, true, 1,
            10, nameSpace, null);
        Assert.isTrue(CollectionUtils.isNotEmpty(gitGroups), "代码组:" + nameSpace + "请添加wirelessread为管理员");

        // 创建代码库
        CreateProjectRequest createProjectRequest = new CreateProjectRequest();
        createProjectRequest.setName(GitUtils.getPathWithoutNamespace(debugProject.getGitUrl()));
        createProjectRequest.setNameSpaceId(debugProject.getGitNameSpaceId());
        BizResult<GitProject> projectCreateResult = aoneCodeService.createProject(createProjectRequest);
        Assert.isTrue(projectCreateResult != null && projectCreateResult.isSuccess(),
            "应用代码仓库创建失败：" + projectCreateResult.getErrorMsg());

        //授权
        AddMemberRequest addMemberRequest = new AddMemberRequest();
        addMemberRequest.setAccessLevel(40);
        addMemberRequest.setExternUids(debugProject.getCreator());
        aoneCodeService.addProjectMember(debugProject.getGitUrl(), addMemberRequest);
    }

    private void cloneGitProject(DebugProject debugProject, DebugProject scaffold) {
        ScaffoldCreateRequest request = new ScaffoldCreateRequest();
        request.setTargetProjectName(GitUtils.getPathWithoutNamespace(debugProject.getGitUrl()));
        request.setTargetGitUrl(debugProject.getGitUrl());
        request.setSourceGitUrl(scaffold.getGitUrl());
        request.setSourceGitTag(scaffold.getTag());
        request.setGitNameSpaceId(debugProject.getGitNameSpaceId());
        request.setNeedCreateGitProject(debugProject.isNeedCreateGitProject());
        request.setAppScaffoldId(debugProject.getScaffoldId());
        request.setCustomMode("FORK_MODE");
        request.setEmpId(debugProject.getCreator());
        if (StringUtils.isNotBlank(debugProject.getDesc())) {
            request.setDesc(debugProject.getDesc());
        } else {
            request.setDesc(scaffold.getDesc());
        }
        BizResult<Boolean> result = scaffoldProjectService.generateScaffoldProject(request);
        if (!result.isSuccess()) {
            throw new RuntimeException(result.getErrorMsg());
        }
    }

    @Override
    public Long updateDebugProject(DebugProject project) {
        Preconditions.checkNotNull(project.getId());
        project.setGmtModified(new Date());

        DebugProjectDO projectDO = new DebugProjectDO();
        BeanUtils.copyProperties(project, projectDO);
        debugProjectDORepository.save(projectDO);

        return project.getId();
    }

    @Override
    public List<DebugProject> queryDebugProject(DebugProjectQuery query) {
        if (null != query.getSpaceId()) {
            List<Long> projectIds = debugProjectRelationDORepository.findAllBySpaceId(query.getSpaceId())
                .stream()
                .map(DebugProjectRelationDO::getDebugProjectId)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(projectIds)) {
                return Collections.emptyList();
            } else {
                query.setDebugProjectIds(projectIds);
            }
        }

        return debugProjectDORepository.findAll(toSpecification(query))
            .stream()
            .map(this::toBO)
            .collect(Collectors.toList());
    }

    @Override
    public Page<DebugProject> queryDebugProject(DebugProjectQuery query, int pageNum, int pageSize) {
        Page<DebugProject> result = new Page<>();

        if (null != query.getSpaceId()) {
            List<Long> projectIds = debugProjectRelationDORepository.findAllBySpaceId(query.getSpaceId())
                .stream()
                .map(DebugProjectRelationDO::getDebugProjectId)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(projectIds)) {
                result.setPage(pageNum);
                result.setPageSize(pageSize);
                result.setTotalCount(0);
                result.setItems(Collections.emptyList());
                return result;
            } else {
                query.setDebugProjectIds(projectIds);
            }
        }

        PageRequest pageRequest = PageRequest.of(pageNum, pageSize,
            Sort.by(new Order(Direction.DESC, "id")));
        org.springframework.data.domain.Page<DebugProjectDO> doPage = debugProjectDORepository
            .findAll(toSpecification(query), pageRequest);

        result.setPageSize(doPage.getSize());
        result.setPage(doPage.getNumber());
        result.setTotalCount((int)doPage.getTotalElements());
        result.setItems(doPage.get().map(this::toBO).collect(Collectors.toList()));

        return result;
    }

    @Override
    public DebugProject getDebugProjectById(Long id) {
        return debugProjectDORepository.findById(id).map(this::toBO).orElse(null);
    }

    @Override
    public List<DebugProjectRelation> findRelationsByProjectId(Long projectId) {
        return debugProjectRelationDORepository
            .findAllByDebugProjectId(projectId)
            .stream()
            .map(debugProjectRelationDO -> {
                DebugProjectRelation debugProjectRelation = new DebugProjectRelation();
                BeanUtils.copyProperties(debugProjectRelationDO, debugProjectRelation);
                return debugProjectRelation;
            }).collect(Collectors.toList());
    }

    public Specification<DebugProjectDO> toSpecification(DebugProjectQuery query) {
        return (Root<DebugProjectDO> root, CriteriaQuery<?> criteriaQuery,
            CriteriaBuilder criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(query.getDebugProjectIds())) {
                predicates.add(root.get("id").in(query.getDebugProjectIds()));
            }
            if (CollectionUtils.isNotEmpty(query.getCreators())) {
                predicates.add(root.get("creator").in(query.getCreators()));
            }
            if (null != query.getIsSystem()) {
                if (query.getIsSystem()) {
                    predicates.add(criteriaBuilder.equal(root.get("creator"), "system"));
                } else {
                    predicates.add(criteriaBuilder.notEqual(root.get("creator"), "system"));
                }
            }
            if (StringUtils.isNotBlank(query.getKeyword())) {
                predicates.add(criteriaBuilder.like(root.get("name"), "%" + query.getKeyword() + "%"));
            }
            if (StringUtils.isNotBlank(query.getGitUrl())) {
                predicates.add(criteriaBuilder.equal(root.get("url"), query.getGitUrl()));
            }
            if (StringUtils.isNotBlank(query.getIdentifier())) {
                predicates.add(criteriaBuilder.equal(root.get("identifier"), query.getIdentifier()));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    @Override
    public Long addDebugProjectRelation(DebugProjectRelation relation) {
        relation.setGmtCreate(new Date());
        DebugProjectRelationDO relationDO = new DebugProjectRelationDO();
        BeanUtils.copyProperties(relation, relationDO);
        debugProjectRelationDORepository.save(relationDO);
        return relationDO.getId();
    }

    @Override
    public Long deleteDebugProjectRelation(Long id) {
        debugProjectRelationDORepository.deleteById(id);
        return id;
    }

    private DebugProject toBO(DebugProjectDO projectDO) {
        DebugProject debugProject = new DebugProject();
        BeanUtils.copyProperties(projectDO, debugProject);
        if (null != debugProject.getScaffoldId()) {
            debugProject.setScaffold(getDebugProjectById(debugProject.getScaffoldId()));
        }
        return debugProject;
    }
}
