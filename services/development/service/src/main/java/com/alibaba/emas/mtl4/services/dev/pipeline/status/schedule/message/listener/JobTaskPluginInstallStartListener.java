package com.alibaba.emas.mtl4.services.dev.pipeline.status.schedule.message.listener;

import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.core.message.task.PipelineTaskInstance;
import com.alibaba.emas.mtl4.scheduler.api.event.JobTaskPluginInstallStart;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineJobExecuteService;
import com.alibaba.emas.mtl4.services.dev.pipeline.service.PipelineLoggerService;
import com.alibaba.emas.mtl4.services.dev.pipeline.service.PipelineTaskExecuteService;
import com.alibaba.emas.mtl4.services.dev.pipeline.status.schedule.message.PipeStatusListener;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class JobTaskPluginInstallStartListener extends PipeStatusListener {

    @Autowired
    PipelineJobExecuteService pipelineJobExecuteService;

    @Autowired
    PipelineTaskExecuteService pipelineTaskExecuteService;

    @Autowired
    private PipelineLoggerService pipelineLoggerService;

    @Override
    public String getType() {
        return "JOB_TASK_PLUGIN_INSTALL_START";
    }

    @Override
    public boolean consume(String message) {
        Assert.notNull(message);
        try {
            JobTaskPluginInstallStart jobTaskPluginInstallStart = JSON.parseObject(message,
                    JobTaskPluginInstallStart.class);
            Assert.notNull(jobTaskPluginInstallStart);
            Assert.notNull(jobTaskPluginInstallStart.getTaskId());

            PipelineTaskInstance pipelineTaskInstance =
                    pipelineJobExecuteService.findPipelineTaskInstanceByJobScheduleIdAndTaskUuid(
                            jobTaskPluginInstallStart.getJobScheduleId(), jobTaskPluginInstallStart.getTaskId());
            Assert.notNull(pipelineTaskInstance);
            Assert.notNull(pipelineTaskInstance.getId());

            pipelineTaskExecuteService.onTaskPluginInstallStart(pipelineTaskInstance.getId(),
                    jobTaskPluginInstallStart.getTaskPluginInstallStartTime());

            pipelineLoggerService.onTaskPluginInstallStart(pipelineTaskInstance);
            return true;
        } catch (Exception e) {
            log.error("error when update JOB_TASK_PLUGIN_INSTALL_START status for task. message :{}. error as below",
                    message, e);
            return true;
        }
    }
}
