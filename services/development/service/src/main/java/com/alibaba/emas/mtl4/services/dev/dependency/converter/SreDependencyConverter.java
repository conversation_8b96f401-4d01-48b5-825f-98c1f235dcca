package com.alibaba.emas.mtl4.services.dev.dependency.converter;

import com.alibaba.emas.mtl4.patch.valueobject.dependency.Dependency;
import com.alibaba.emas.mtl4.services.dev.api.model.ApplicationDependencyDetailBO;
import com.alibaba.emas.mtl4.services.dev.api.model.DepVersionProp;
import com.alibaba.emas.mtl4.services.dev.app.domain.Application;
import com.alibaba.emas.mtl4.services.dev.app.repository.AppRepository;
import com.alibaba.emas.mtl4.services.dev.dependency.dto.SreDependencyModuleDTO;
import com.alibaba.emas.mtl4.services.dev.dependency.dto.SreDependencyModuleDiffDTO;
import lombok.Setter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public abstract class SreDependencyConverter {
    @Setter(onMethod_ = {@Autowired})
    private AppRepository appRepository;

    public abstract List<SreDependencyModuleDTO> toDTO(List<ApplicationDependencyDetailBO> applicationDependencyDetails);


    @Mapping(target = "moduleName", ignore = true)
    @Mapping(target = "moduleVersion", source = "dependencyVersion")
    @Mapping(target = "moduleId", source = "applicationId")
    @Mapping(target = "depKey", source = "dependencyKey")
    public abstract SreDependencyModuleDTO toDTO(ApplicationDependencyDetailBO applicationDependencyDetails);

    public void batchFillModuleName(List<SreDependencyModuleDTO> sreDependencyModuleDTOS) {
        List<Long> ids = sreDependencyModuleDTOS.stream()
                .map(SreDependencyModuleDTO::getModuleId)
                .collect(Collectors.toList());

        Map<Long, Application> applicationMap = appRepository.findAllById(ids)
                .stream()
                .collect(Collectors.toMap(Application::getId, Function.identity()));

        sreDependencyModuleDTOS.forEach(sreDependencyModuleDTO -> {
            if (!applicationMap.containsKey(sreDependencyModuleDTO.getModuleId())) {
                return;
            }

            Application application = applicationMap.get(sreDependencyModuleDTO.getModuleId());
            sreDependencyModuleDTO.setModuleName(application.getName());
        });
    }

    public void batchFillModuleName4Diff(List<SreDependencyModuleDiffDTO> sreDependencyModuleDiffDTOS) {
        List<Long> ids = sreDependencyModuleDiffDTOS.stream()
                .map(SreDependencyModuleDiffDTO::getModuleId)
                .collect(Collectors.toList());

        Map<Long, Application> applicationMap = appRepository.findAllById(ids)
                .stream()
                .collect(Collectors.toMap(Application::getId, Function.identity()));

        sreDependencyModuleDiffDTOS.forEach(sreDependencyModuleDTO -> {
            if (!applicationMap.containsKey(sreDependencyModuleDTO.getModuleId())) {
                return;
            }

            Application application = applicationMap.get(sreDependencyModuleDTO.getModuleId());
            sreDependencyModuleDTO.setModuleName(application.getName());
        });
    }

    public abstract List<SreDependencyModuleDTO> fromPatch(List<Dependency> dependencies);

    @Mapping(target = "moduleName", source = "module.name")
    @Mapping(target = "moduleId", source = "module.id")
    @Mapping(target = "depKey", source = "module.depKey")
    @Mapping(target = "moduleVersion", source = "version")
    public abstract SreDependencyModuleDTO fromPatch(Dependency source);

    public abstract List<SreDependencyModuleDTO> fromDepList(Collection<DepVersionProp> values);

    @Mapping(target = "moduleVersion", source = "depVersion")
    @Mapping(target = "moduleName", source = "productName")
    @Mapping(target = "moduleId", source = "applicationId")
    public abstract SreDependencyModuleDTO fromDepList(DepVersionProp source);

    public abstract List<ApplicationDependencyDetailBO> toApplicationDependencyDetailBO(Collection<DepVersionProp> source);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "gmtCreate", ignore = true)
    @Mapping(target = "publishProjectId", ignore = true)
    @Mapping(target = "dependencyId", source = "depsId")
    @Mapping(target = "dependencyVersion", source = "depVersion")
    @Mapping(target = "dependencyType", source = "gradleDepType")
    @Mapping(target = "dependencyKey", source = "depKey")
    public abstract ApplicationDependencyDetailBO toApplicationDependencyDetailBO(DepVersionProp source);
}
