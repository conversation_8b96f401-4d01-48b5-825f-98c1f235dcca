package com.alibaba.emas.mtl4.services.dev.operationlog.context;

import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.ParameterNameDiscoverer;

import java.lang.reflect.Method;

/**
 * MethodBasedEvaluationContext 基于方法的上下文，主要作用：将方法参数放入到上下文中
 *
 * <AUTHOR>
 * @Date 2022/06/10
 */
public class OperationLogEvaluationContext extends MethodBasedEvaluationContext {

    public OperationLogEvaluationContext(Method method, Object[] arguments, ParameterNameDiscoverer parameterNameDiscoverer) {
        super(null, method, arguments, parameterNameDiscoverer);
        super.lazyLoadArguments();
    }
}
