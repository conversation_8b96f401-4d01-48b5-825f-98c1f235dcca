package com.alibaba.emas.mtl4.services.dev.workflow.adapter;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.emas.mtl4.commons.pipes.RunStatus;
import com.alibaba.emas.mtl4.commons.utils.Pair;
import com.alibaba.emas.mtl4.services.dev.ServiceFacade;
import com.alibaba.emas.mtl4.services.dev.api.enums.AlterSheetType;
import com.alibaba.emas.mtl4.services.dev.api.enums.AlterType;
import com.alibaba.emas.mtl4.services.dev.api.enums.TriggerType;
import com.alibaba.emas.mtl4.services.dev.api.model.AlterSheetBO;
import com.alibaba.emas.mtl4.services.dev.api.model.AlterSheetModuleBO;
import com.alibaba.emas.mtl4.services.dev.api.model.ApplicationBO;
import com.alibaba.emas.mtl4.services.dev.api.query.AlterSheetModuleQuery;
import com.alibaba.emas.mtl4.services.dev.app.model.BranchModelInfo;
import com.alibaba.emas.mtl4.services.dev.app.ClientModuleIntegrationBranchService;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.pipeline.PipelineInstance;
import com.alibaba.emas.mtl4.services.flow.model.work.status.WorkflowStepStatus;
import com.alibaba.emas.mtl4.services.flow.model.work.step.WorkflowStep;
import com.alibaba.emas.mtl4.services.flow.model.work.step.WorkflowStepStatusAdapter;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DevSpaceIntegrateSdkCodeMergeInitStatusAdapter implements WorkflowStepStatusAdapter {
    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private DevSpaceAdapterService devSpaceAdapterService;

    @Autowired
    private ClientModuleIntegrationBranchService clientModuleIntegrationBranchService;

    @Override
    public Pair<WorkflowStepStatus, String> adapt(WorkflowStep step, String value) {
        if (WorkflowStepStatus.UNEXECUTED.equals(step.getStatus())) {
            Long alterSheetId = devSpaceAdapterService.getAlterSheetId(step);
            List<AlterSheetModuleBO> alterSheetModules = serviceFacade.getAlterSheetModuleService()
                .queryAlterSheetModuleByQuery(AlterSheetModuleQuery.builder()
                    .isDeleted(false)
                    .alterSheetId(alterSheetId)
                    .build());
            //当前没有模块，则返回未初始化
            if (CollectionUtils.isEmpty(alterSheetModules)) {
                return new Pair<>(WorkflowStepStatus.UNINITIALIZED, "当前没有添加模块，请先在'变更模块'节点新增模块");
            }
            List<AlterSheetModuleBO> sourceAlterSheetModules =  alterSheetModules.stream()
                .filter(alterSheetModuleBO -> AlterType.SOURCE.equals(alterSheetModuleBO.getAlterType()))
                .collect(Collectors.toList());
            //没有源码依赖模块，无需代码评审和合并节点
            if (CollectionUtils.isEmpty(sourceAlterSheetModules)) {
                return new Pair<>(WorkflowStepStatus.SKIPPED, "没有源码依赖模块，无需代码评审和合并节点");
            }
            boolean allIntegrateBranch = sourceAlterSheetModules.stream()
                .allMatch(alterSheetModuleBO -> {
                    BranchModelInfo branchModelInfo = clientModuleIntegrationBranchService.getBranchModelInfoByAlterSheetId(alterSheetId, alterSheetModuleBO.getModuleId());
                    String integrateBranch = branchModelInfo.getIntegrationBranch();
                    return alterSheetModuleBO.getBranch().equals(integrateBranch);
                });
            //所有源码依赖模块的分支都是集成分支，无需代码评审和合并节点
            if (allIntegrateBranch) {
                return new Pair<>(WorkflowStepStatus.SKIPPED, "所有源码依赖模块的分支都是集成分支，无需代码评审和合并节点");
            }

            boolean allPublishBranch = sourceAlterSheetModules.stream()
                    .allMatch(alterSheetModuleBO -> {
                        ApplicationBO module = serviceFacade.getAppService().findApplication(alterSheetModuleBO.getModuleId());
                        return alterSheetModuleBO.getBranch().equals(module.getPublishBranch());
                    });
            //所有源码依赖模块的分支都是发布分支，无需代码评审和合并节点
            if (allPublishBranch) {
                AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService().findSimpleAlterSheetById(alterSheetId);
                if(Objects.equals(alterSheetBO.getType(), AlterSheetType.NATIVE_DYNAMIC)) {
                    return new Pair<>(WorkflowStepStatus.SKIPPED, "所有源码依赖模块的分支都是发布分支，无需代码评审和合并节点");
                }
            }
            //动态发布单 存在源码依赖模块 要求最新客户端流水线执行成功
            AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService().findSimpleAlterSheetById(alterSheetId);
            if (AlterSheetType.NATIVE_DYNAMIC.equals(alterSheetBO.getType())) {
                //动态发布单 存在源码依赖模块 要求最新客户端流水线执行成功
                PipelineInstance latestInstance = serviceFacade.getPipelineExecuteService()
                        .getLatestAppBuildInstance(alterSheetId, TriggerType.unVisibleTriggerType());
                if (latestInstance == null) {
                    return new Pair<>(WorkflowStepStatus.UNINITIALIZED, "当前没有执行成功的客户端产物，请先在'构建'节点构建成功");
                }
                if (!RunStatus.SUCCEEDED.equals(latestInstance.getPipelineBizStatus())) {
                    return new Pair<>(WorkflowStepStatus.UNINITIALIZED, "最新客户端流水线还未执行成功，请先在'构建'节点构建成功");
                }
            }
            return new Pair<>(WorkflowStepStatus.UNEXECUTED, null);
        }
        return null;
    }

    @Override
    public String getListenerName() {
        return DevSpaceIntegrateSdkCodeMergeInitStatusAdapter.class.getSimpleName();
    }
}
