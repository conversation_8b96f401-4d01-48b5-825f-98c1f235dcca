package com.alibaba.emas.mtl4.services.dev.integrate.service;

/**
 * <AUTHOR>
 * @Date 2023-07-14
 */
public interface IntegrateAreaPipelineExecuteService {

    /**
     * 用默认参数执行流水线
     * @param pipelineId
     * @return
     */
    Long executePipelineWithDefaultParams(Long pipelineId);


    /**
     * 用默认参数执行流水线
     * @param pipelineId
     * @param user
     * @return
     */
    Long executePipelineWithDefaultParams(Long pipelineId, String user);

    /**
     * 停止已经删除的流水线实例
     * @param integrateAreaId
     * @return
     */
    boolean stopDeletedPipelineInstance(Long integrateAreaId);



}
