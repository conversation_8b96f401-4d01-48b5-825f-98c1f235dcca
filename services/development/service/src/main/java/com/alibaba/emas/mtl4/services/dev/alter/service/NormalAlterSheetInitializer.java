package com.alibaba.emas.mtl4.services.dev.alter.service;

import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.services.dev.api.enums.AlterSheetType;
import com.alibaba.emas.mtl4.services.dev.api.enums.AlterType;
import com.alibaba.emas.mtl4.services.dev.api.model.AlterSheetBO;
import com.alibaba.emas.mtl4.services.dev.api.model.AlterSheetModuleBO;
import com.alibaba.emas.mtl4.services.dev.api.model.ApplicationBO;
import com.alibaba.emas.mtl4.services.dev.api.model.ModuleClientRelationBO;
import com.alibaba.emas.mtl4.services.dev.app.service.AppInnerService;
import com.alibaba.emas.mtl4.services.dev.app.ModuleClientRelationService;
import com.alibaba.emas.mtl4.services.dev.configuration.ConfigurationInnerService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022-08-01
 */
@Service
public class NormalAlterSheetInitializer implements AlterSheetInitializer  {
    @Autowired
    private AppInnerService appInnerService;
    @Autowired
    private ConfigurationInnerService configurationInnerService;
    @Autowired
    private ModuleClientRelationService moduleClientRelationService;

    @Override
    public AlterSheetBO initAlterSheet(Long applicationId, List<Long> moduleIds, AlterSheetType type) {
        Assert.isTrue(AlterSheetType.NORMAL.equals(type), "unsupported alterSheetType in NormalAlterSheetInitializer");
        Assert.notNull(applicationId, "请勾选一个客户端创建普通变更");

        AlterSheetBO alterSheetBO = new AlterSheetBO();
        ApplicationBO application = appInnerService.findApplication(applicationId);
        alterSheetBO.setApplication(application);
        alterSheetBO.setApplicationId(applicationId);
        alterSheetBO.setType(type);
        Boolean smallAppConfig = configurationInnerService.getSmallAppConfig(applicationId);
        if (smallAppConfig == null || !smallAppConfig) {
            if (CollectionUtils.isNotEmpty(moduleIds)) {
                List<ModuleClientRelationBO> relations =
                        moduleClientRelationService.findByClientIdAndModuleIdIn(applicationId, moduleIds);
                if (CollectionUtils.isEmpty(relations)) {
                    return alterSheetBO;
                }

                List<Long> relatedModuleIds = relations.stream()
                        .map(ModuleClientRelationBO::getModuleId).collect(Collectors.toList());
                List<ApplicationBO> modules = appInnerService.findApplications(relatedModuleIds);
                Map<Long, ApplicationBO> moduleMap = modules.stream()
                        .collect(Collectors.toMap(ApplicationBO::getId, ApplicationBO -> ApplicationBO));

                List<AlterSheetModuleBO> alterSheetModuleList = relatedModuleIds.stream().map(relatedModuleId -> {
                    AlterSheetModuleBO alterSheetModule = new AlterSheetModuleBO();
                    alterSheetModule.setModuleId(relatedModuleId);
                    alterSheetModule.setAlterType(AlterType.SOURCE);
                    alterSheetModule.setModule(moduleMap.get(relatedModuleId));
                    return alterSheetModule;
                }).collect(Collectors.toList());
                alterSheetBO.setAlterSheetModuleList(alterSheetModuleList);
            }
        }
        return alterSheetBO;
    }

}
