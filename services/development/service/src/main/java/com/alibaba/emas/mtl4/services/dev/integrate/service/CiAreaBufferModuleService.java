package com.alibaba.emas.mtl4.services.dev.integrate.service;

import com.alibaba.emas.mtl4.services.dev.integrate.command.CiBufferModuleRemoveCmd;
import com.alibaba.emas.mtl4.services.dev.integrate.model.CiAreaBufferModuleBO;
import com.alibaba.emas.mtl4.services.dev.integrate.model.IntegrateAreaModuleExtBO;

import java.util.List;
import java.util.Optional;

public interface CiAreaBufferModuleService {
    Long save(CiAreaBufferModuleBO integrateAreaModuleBO);

    List<CiAreaBufferModuleBO> findAllByVersionPlanId(Long versionPlanId);

    List<CiAreaBufferModuleBO> findAllByVersionPlanIdWithModule(Long versionPlanId);

    List<IntegrateAreaModuleExtBO> getCiAreaModules(Long versionPlanId);

    void remove(CiBufferModuleRemoveCmd build);

    Optional<CiAreaBufferModuleBO> findByVersionPlanIdAndModuleId(Long versionPlanId, Long moduleId);

    /**
     * 根据主键查找缓冲区模块
     * @param ciBufferModuleId
     * @return
     */
    Optional<CiAreaBufferModuleBO> getCiAreaModule(Long ciBufferModuleId);

    /**
     * 删除缓冲区子模块
     * @param versionPlanId
     * @param moduleId
     * @param submoduleIds
     * @param empId
     */
    void deleteSubAreaModules(Long versionPlanId, Long moduleId, List<Long> submoduleIds, String empId);
}
