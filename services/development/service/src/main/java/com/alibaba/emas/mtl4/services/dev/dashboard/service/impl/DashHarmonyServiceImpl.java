package com.alibaba.emas.mtl4.services.dev.dashboard.service.impl;

import com.alibaba.emas.appgallery.api.AppGalleryConnect;
import com.alibaba.emas.appgallery.api.Utils.FileUtils;
import com.alibaba.emas.appgallery.api.enums.*;
import com.alibaba.emas.appgallery.api.model.publishing.DownloadReportResponse;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.commons.utils.TimeUtil;
import com.alibaba.emas.mtl4.services.dev.channel.model.AndroidAppStoreType;
import com.alibaba.emas.mtl4.services.dev.channelBuild.model.AppChannelResourceInfo;
import com.alibaba.emas.mtl4.services.dev.channelBuild.model.HarmonyPublishRecordBO;
import com.alibaba.emas.mtl4.services.dev.channelBuild.service.AppChannelResourceInfoService;
import com.alibaba.emas.mtl4.services.dev.channelBuild.service.HarmonyPublishHelper;
import com.alibaba.emas.mtl4.services.dev.channelBuild.service.HarmonyPublishRecordService;
import com.alibaba.emas.mtl4.services.dev.dashboard.service.DashHarmonyService;
import com.alibaba.emas.mtl4.services.dev.dashboard.service.model.HarmonyDownloadData;
import com.alibaba.excel.context.AnalysisContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.ReadListener;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import java.util.*;

/**
 * 鸿蒙报表相关服务
 */
@Service
@Slf4j
public class DashHarmonyServiceImpl implements DashHarmonyService {

    @Resource
    private HarmonyPublishRecordService harmonyPublishRecordService;

    @Resource
    private HarmonyPublishHelper harmonyPublishHelper;

    @Autowired
    private AppChannelResourceInfoService resourceInfoService;

    /**
     * 鸿蒙报表表头索引
     */
    private static final Integer RowHeaderIndex = 5;

    /**
     * 鸿蒙报表内容索引
     */
    private static final Integer RowContentIndex = 6;

    @Override
    public Map<String, HarmonyDownloadData> getDownloadReport(Long releaseId) {
        HarmonyPublishRecordBO recordBO = harmonyPublishRecordService.getHarmonyPublishRecord(releaseId);

        // 只有发布成功才可以获取下载记录
        if (recordBO == null || !(ReleaseDisplayStatusEnum.UPDATING.name().equals(recordBO.getPublishStatus()) ||
                ReleaseDisplayStatusEnum.PHASED_RELEASE.name().equals(recordBO.getPublishStatus()) ||
                ReleaseDisplayStatusEnum.PUBLIC_TEST.name().equals(recordBO.getPublishStatus()))) {
            return Collections.emptyMap();
        }

        // 获取app的资源渠道信息和连接池
        List<AppChannelResourceInfo> resourceInfoList = resourceInfoService.queryChannelResourceInfo(recordBO.getApplicationId(),
                AndroidAppStoreType.HARMONY);
        Assert.isFalse(resourceInfoList.isEmpty(), "当前app没有AGC信息");
        AppChannelResourceInfo resourceInfo = resourceInfoList.get(0);
        String appId = harmonyPublishHelper.getAppId(resourceInfo);
        AppGalleryConnect appGalleryConnect = harmonyPublishHelper.getAppGalleryConnect(resourceInfo);

        // 获取当前app版本180天内的下载报表
        DownloadReportResponse response = appGalleryConnect.getPublishService().getDownloadReport(appId,
                LangEnum.CN.getDescription(), TimeUtil.getDateDaysAgo(180), TimeUtil.getDayTimeStr(System.currentTimeMillis()),
                ReportFileEnum.EXCEL.name(), FileConditionEnum.APP_VERSION.getDescription(),
                Collections.singletonList(recordBO.getVersionNumber()), ReportGroupEnum.APP_VERSION.getDescription());
        Assert.isTrue(0 == response.getRet().getCode(), "获取鸿蒙下载报表失败:" + response.getRet().getMsg());

        return getDownloadReport(response.getFileURL(), recordBO.getApplicationId() + "-" + recordBO.getVersionId() + ".xlsx");
    }

    /**
     * 根据下载链接和文件名获取下载报表的map
     * @param downloadUrl
     * @param fileName
     * @return
     */
    private Map<String, HarmonyDownloadData> getDownloadReport(String downloadUrl, String fileName) {
        File file = FileUtils.downloadFile(downloadUrl, fileName);
        Assert.isTrue(file != null && file.exists(), "从" + downloadUrl + "下载鸿蒙报表失败");
        try {
            return readExcel(file);
        }catch (Exception exception) {
            log.error("getDownloadReport readExcel exception", exception);
            throw new RuntimeException("读取鸿蒙下载报表失败", exception);
        } finally {
            // 删除文件
            FileUtils.deleteFile(file);
        }
    }

    /**
     * 根据文件获取报表的map,读取excel文件内容
     * @param file
     * @return
     */
    private static Map<String, HarmonyDownloadData> readExcel(File file) {
        Map<String, HarmonyDownloadData> result = new HashMap<>();

        EasyExcel.read(file, new ReadListener<Object>() {
            LinkedHashMap<Integer, Object> rowDataKeyMap = new LinkedHashMap<>();
            LinkedHashMap<Integer, Object> rowDataValueMap = new LinkedHashMap<>();

            @Override
            public void invoke(Object rowData, AnalysisContext context) {
                if (Objects.equals(context.getCurrentRowNum(), RowHeaderIndex)) {
                    rowDataKeyMap = (LinkedHashMap<Integer, Object>) rowData;
                }

                if (Objects.equals(context.getCurrentRowNum(), RowContentIndex)) {
                    rowDataValueMap = (LinkedHashMap<Integer, Object>) rowData;
                    for (Map.Entry<Integer, Object> entry : rowDataKeyMap.entrySet()) {
                        Integer key = entry.getKey();
                        String name = entry.getValue() != null ? entry.getValue().toString() : ""; // 对应的值
                        String value = rowDataValueMap.get(key) != null ? rowDataValueMap.get(key).toString() : "";
                        // 根据类型名称创建 HarmonyDownloadData
                        ReportDataType type = ReportDataType.getTypeByName(name);
                        if (type != null) {
                            HarmonyDownloadData data = new HarmonyDownloadData(value, type.getName(),
                                    type.getDescription() + "(T+1时效)");
                            result.put(type.name(), data);
                        }
                    }
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().doRead();

        return result;
    }

}
