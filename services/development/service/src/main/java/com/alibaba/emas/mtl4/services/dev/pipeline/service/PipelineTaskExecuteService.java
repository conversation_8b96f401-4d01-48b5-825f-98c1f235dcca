package com.alibaba.emas.mtl4.services.dev.pipeline.service;

import com.alibaba.emas.mtl4.commons.pipes.RunStatus;
import com.alibaba.emas.mtl4.core.message.job.PipelineJobInstance;
import com.alibaba.emas.mtl4.core.message.task.PipelineTask;
import com.alibaba.emas.mtl4.core.message.task.PipelineTaskInstance;
import com.alibaba.emas.mtl4.services.dev.api.enums.gate.GateCheckResult;
import com.alibaba.emas.mtl4.services.dev.api.model.task.TaskInstanceExecuteSummary;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface PipelineTaskExecuteService {

    void onTaskInstanceStart(Long taskInstanceId, long startTime);

    void onTaskPluginDownloadStart(Long taskInstanceId, long taskPluginDownloadStartTime);

    void onTaskPluginDownloadFinish(Long taskInstanceId, long taskPluginDownloadFinishTime);

    void onTaskPluginLoadStart(Long taskInstanceId, long taskPluginLoadStartTime);

    void onTaskPluginLoadFinish(Long taskInstanceId, long taskPluginLoadFinishTime);

    void onTaskPluginInstallStart(Long taskInstanceId, long taskPluginInstallStartTime);

    void onTaskPluginInstallFinish(Long taskInstanceId, long taskPluginInstallFinishTime);

    void onTaskInstanceFinish(Long taskInstanceId, long endTime, RunStatus runStatus, RunStatus taskBizStatus);

    PipelineTaskInstance createPipelineTaskInstance(PipelineTask pipelineTask,
                                                    PipelineJobInstance pipelineJobInstance,
                                                    boolean skip);

    /**
     * 根据jobInstanceId查找所有的taskInstance
     *
     * @param jobInstanceId
     * @return
     */
    List<PipelineTaskInstance> findTaskInstanceListByJobInstanceId(long jobInstanceId);

    /**
     * 根据jobInstanceId查找所有的taskInstance
     * taskInstance只包含自身的信息
     *
     * @param jobInstanceId
     * @return
     */
    List<PipelineTaskInstance> findSimpleTaskInstanceByJobInstanceId(long jobInstanceId);

    /**
     * 根据jobInstanceId查找所有的taskInstance
     * taskInstance不包含PluginTask的信息
     * @param jobInstanceId
     * @return
     */
    List<PipelineTaskInstance> findTaskInstancesWithoutPluginTask(Long jobInstanceId);


    Optional<PipelineTaskInstance> findTaskInstanceById(long taskInstanceId);

    /**
     * 根据pipelineTaskId和pipelineInstanceId查找PipelineTaskInstance
     *
     * @param pipelineTaskId
     * @param pipelineInstanceId
     * @return PipelineTaskInstance
     */
    PipelineTaskInstance findTaskByTaskIdAndPipelineInstanceId(Long pipelineTaskId, Long pipelineInstanceId);

    /**
     * 保存taskInstance
     *
     * @param pipelineTaskInstance
     * @return
     */
    PipelineTaskInstance save(PipelineTaskInstance pipelineTaskInstance);


    /**
     * 批量保存taskInstance
     *
     * @param taskInstances
     * @return
     */
    List<Long> batchSave(List<PipelineTaskInstance> taskInstances);

    /**
     * 查找流水线任务的实例运行情况
     *
     * @param jobInstanceId
     * @param jobScheduleId
     * @return TaskInstanceExecuteSummary list
     */
    List<TaskInstanceExecuteSummary> queryTaskInstanceSummary(Long jobInstanceId, Long jobScheduleId, boolean isSimple);

    /**
     * 插件卡口结果变化触发流水线实例业务态更新
     *
     * @param pipelineInstanceId
     * @param taskUuid
     * @param checkResult
     * @return
     */
    PipelineTaskInstance updateBizStatusOnGateCheckResultChange(Long pipelineInstanceId,
                                                                String taskUuid,
                                                                GateCheckResult checkResult);
}
