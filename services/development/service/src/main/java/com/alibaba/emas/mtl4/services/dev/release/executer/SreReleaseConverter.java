package com.alibaba.emas.mtl4.services.dev.release.executer;

import com.alibaba.emas.mtl4.services.dev.release.domain.Release;
import com.alibaba.emas.mtl4.services.dev.release.model.SreReleaseDTO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SreReleaseConverter {
    SreReleaseDTO toDTO(Release release);

    List<SreReleaseDTO> toDTO(List<Release> release);
}
