package com.alibaba.emas.mtl4.services.dev.ios.service;

import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.services.dev.api.release.model.ReleaseBO;

import java.util.List;

/**
 * <AUTHOR>
 * TestFlight服务接口
 */
public interface TestFlightService {

    /**
     * 获取内外测试组的所有测试员额度总和
     * @param applicationId
     * @return
     */
    BizResult<Integer> getTotalBetaTesters(Long applicationId);

    /**
     * 获取使用TestFlight的所有applicationId
     * @return
     */
    List<Long> getApplicationIdByTestFlightApp();

    /**
     * 检查应用的testflight beta测试员是否超额，超额发出告警
     * @param applicationId
     */
    void alarmTesterExceedByApplicationId(Long applicationId);

    /**
     * iOS 灰度发布单创建时判断是否发出测试员超额告警
     * @param applicationId
     * @param releaseBO
     */
    void alarmTesterExceedByApplicationId(Long applicationId, ReleaseBO releaseBO);
}
