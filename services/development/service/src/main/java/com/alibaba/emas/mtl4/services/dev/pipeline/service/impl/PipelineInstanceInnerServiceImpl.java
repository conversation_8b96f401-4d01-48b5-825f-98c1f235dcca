package com.alibaba.emas.mtl4.services.dev.pipeline.service.impl;

import com.alibaba.emas.mtl4.commons.pipes.RunStatus;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.commons.utils.GitUtils;
import com.alibaba.emas.mtl4.core.message.job.EnvVariables;
import com.alibaba.emas.mtl4.core.message.job.PipelineJobInstance;
import com.alibaba.emas.mtl4.services.dev.alter.AlterPipelineInstanceModuleService;
import com.alibaba.emas.mtl4.services.dev.alter.AlterSheetModuleService;
import com.alibaba.emas.mtl4.services.dev.alter.model.AlterPipelineInstanceModuleBO;
import com.alibaba.emas.mtl4.services.dev.alter.service.AlterSheetService;
import com.alibaba.emas.mtl4.services.dev.api.enums.AlterMode;
import com.alibaba.emas.mtl4.services.dev.api.enums.AlterType;
import com.alibaba.emas.mtl4.services.dev.api.enums.ApplicationType;
import com.alibaba.emas.mtl4.services.dev.api.enums.BranchModel;
import com.alibaba.emas.mtl4.services.dev.api.model.*;
import com.alibaba.emas.mtl4.services.dev.api.model.git.GitCommit;
import com.alibaba.emas.mtl4.services.dev.api.model.git.GitCommitBO;
import com.alibaba.emas.mtl4.services.dev.api.service.BaseDependencyService;
import com.alibaba.emas.mtl4.services.dev.api.service.PipelineApiService;
import com.alibaba.emas.mtl4.services.dev.app.enums.ModuleDeployStatus;
import com.alibaba.emas.mtl4.services.dev.app.model.BranchModelInfo;
import com.alibaba.emas.mtl4.services.dev.app.model.ModuleDeployRecordBO;
import com.alibaba.emas.mtl4.services.dev.app.model.ModuleDeployRecordQuery;
import com.alibaba.emas.mtl4.services.dev.app.service.AppInnerService;
import com.alibaba.emas.mtl4.services.dev.app.ClientModuleIntegrationBranchService;
import com.alibaba.emas.mtl4.services.dev.app.service.ModuleDeployRecordService;
import com.alibaba.emas.mtl4.services.dev.build.model.BuildParamName;
import com.alibaba.emas.mtl4.services.dev.code.CodeService;
import com.alibaba.emas.mtl4.services.dev.code.service.GitCommitConverter;
import com.alibaba.emas.mtl4.services.dev.dependency.constants.DependencyConstant;
import com.alibaba.emas.mtl4.services.dev.dependency.service.BaseDependencyInnerService;
import com.alibaba.emas.mtl4.services.dev.module.ModuleSourceCodeBuildService;
import com.alibaba.emas.mtl4.services.dev.module.dto.SourceCodeModuleBuildDTO;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineJobExecuteService;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineExecuteService;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineConstants.Type;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineStage;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.job.JobInstanceExecuteSummary;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.pipeline.PipelineInstance;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.pipeline.PipelineInstanceExecuteSummary;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.stage.StageInstanceExecuteSummary;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineInstanceInnerService;
import com.alibaba.emas.mtl4.services.dev.pipeline.service.PipelineStageService;

import com.alibaba.emas.mtl4.services.dev.pipeline.utils.VariableUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/27
 */
@Service
@Slf4j
public class PipelineInstanceInnerServiceImpl implements PipelineInstanceInnerService {

    @Autowired
    private AlterSheetService alterSheetService;

    @Autowired
    private AlterSheetModuleService alterSheetModuleService;

    @Autowired
    private PipelineExecuteService pipelineExecuteService;

    @Autowired
    private PipelineApiService pipelineApiService;

    @Autowired
    private PipelineStageService pipelineStageService;

    @Autowired
    private BaseDependencyService baseDependencyService;

    @Autowired
    private BaseDependencyInnerService baseDependencyInnerService;

    @Autowired
    private PipelineJobExecuteService pipelineJobExecuteService;

    @Autowired
    private ModuleDeployRecordService moduleDeployRecordService;

    @Autowired
    private AppInnerService appInnerService;

    @Autowired
    private CodeService codeService;

    @Autowired
    private AlterPipelineInstanceModuleService moduleService;

    @Autowired
    private ClientModuleIntegrationBranchService integrationBranchService;

    @Autowired
    ModuleSourceCodeBuildService moduleSourceCodeBuildService;

    @Override
    public List<PipelineInstanceModuleBO> getPipelineInstanceModuleList(Long alterSheetId,
                                                                        Long pipelineInstanceId,
                                                                        boolean needUpdateDetail) {
        if (alterSheetId == null || pipelineInstanceId == null) {
            return Collections.emptyList();
        }
        AlterSheetBO alterSheet = alterSheetService.findAlterSheetById(alterSheetId);
        if (alterSheet == null) {
            return Collections.emptyList();
        }

        List<AlterSheetModuleBO> alterSheetModuleList = alterSheet.getAlterSheetModuleList();
        Map<Long, AlterSheetModuleBO> alterSheetModuleMap = alterSheetModuleList.stream()
                .collect(Collectors.toMap(AlterSheetModuleBO::getModuleId, Function.identity(), (v1,v2) -> v2));
        AlterPipelineInstanceModuleBO instanceModuleBO =
                moduleService.getAlterPipelineInstanceModules(pipelineInstanceId);
        if (instanceModuleBO != null) {
            //提测模块排除当前变更单中已经被删除的模块
            alterSheetModuleList = instanceModuleBO.getAlterSheetModules();
            if (CollectionUtils.isNotEmpty(alterSheetModuleList)) {
                alterSheetModuleList = alterSheetModuleList.stream()
                        .filter(alterModule -> alterSheetModuleMap.containsKey(alterModule.getModuleId()))
                        .peek(alterModule -> alterModule.setRequirementRelations(
                                alterSheetModuleMap.get(alterModule.getModuleId()).getRequirementRelations()))
                        .collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(alterSheetModuleList)) {
            return Collections.emptyList();
        }

        PipelineInstance pipelineInstance = pipelineExecuteService.findPipelineInstanceById(pipelineInstanceId);
        if (pipelineInstance == null) {
            return Collections.emptyList();
        }

        BaseDependencyBO baseDependency = baseDependencyInnerService.getPipelineInstanceBaseDependency(pipelineInstanceId);
        if (baseDependency == null) {
            return Collections.emptyList();
        }
        Long depApplicationId = baseDependency.getDependencyApplicationId();
        if (depApplicationId == null) {
            depApplicationId = alterSheet.getApplicationId();
        }

        Map<String, DepVersionProp> baseDepsDetail = baseDependencyService.findBaseDepsDetail(depApplicationId,
                baseDependency.getType(), baseDependency.getVersion(), baseDependency.getDependencyIntegrateId());
        Map<Long, GitCommitBO> mixPipelineInstanceModuleCommits = getMixPipelineInstanceModuleCommits(pipelineInstance);
        Date startTime = getClientBuildStartTime(pipelineInstanceId);
        Date judgeTime = startTime != null ? startTime : pipelineInstance.getStartTime();
        Map<Long, SourceCodeModuleBuildDTO> sourceCodeModuleCommitMap = new HashMap<>();
        // 增加一个限制减少请求
        if (moduleSourceCodeBuildService.getSupportSourceCodeBuildStatus(new ArrayList<>(alterSheetModuleMap.keySet())).values().stream()
                .anyMatch(support -> support)) {
            sourceCodeModuleCommitMap = getSourceCodeModuleMap(pipelineInstanceId);
        }
        List<PipelineInstanceModuleBO> resultList = new ArrayList<>();
        try {
            for (AlterSheetModuleBO alterSheetModule : alterSheetModuleList) {
                ApplicationBO moduleInfo = alterSheetModule.getModule();
                if (moduleInfo == null) {
                    return null;
                }
                String depKey = moduleInfo.getDepKey();
                DepVersionProp depVersionProp = baseDepsDetail.get(depKey);
                PipelineInstanceModuleBO moduleBO = generateInstanceModule(alterSheetModule, depVersionProp,
                        mixPipelineInstanceModuleCommits, judgeTime, needUpdateDetail, sourceCodeModuleCommitMap);
                resultList.add(moduleBO);
            }
        } catch (Exception e) {
            return Collections.emptyList();
        }
        return resultList;
    }

    @Override
    public List<PipelineInstanceModuleBO> getSimplePipelineInstanceModules(Long alterSheetId, Long pipelineInstanceId) {
        Assert.notNull(pipelineInstanceId, "pipelineInstanceId cannot be null");
        AlterPipelineInstanceModuleBO instanceModuleBO =
                moduleService.getAlterPipelineInstanceModules(pipelineInstanceId);
        List<AlterSheetModuleBO> alterSheetModuleList = null;
        if (instanceModuleBO != null) {
            alterSheetModuleList = instanceModuleBO.getAlterSheetModules();
        } else if (alterSheetId != null) {
            alterSheetModuleList = alterSheetModuleService.findAlterSheetModuleBOList(alterSheetId);
        }
        if (alterSheetModuleList == null) {
            return Collections.emptyList();
        }

        return alterSheetModuleList.stream().map(alterSheetModule -> PipelineInstanceModuleBO.builder()
                .moduleId(alterSheetModule.getModuleId())
                .alterMode(alterSheetModule.getAlterMode())
                .alterType(alterSheetModule.getAlterType())
                .alterModuleId(alterSheetModule.getId())
                .version(alterSheetModule.getVersion())
                .build()).collect(Collectors.toList());
    }

    @Override
    public Map<Long, String> getMixedPipelineInstanceModuleCommits(Long alterSheetId, Long instanceId) {
        Assert.notNull(alterSheetId, "alterSheetId cannot be null");
        Assert.notNull(instanceId, "instanceId cannot be null");

        Map<Long, String> result = new HashMap<>(16);
        AlterSheetBO alterSheet = alterSheetService.findAlterSheetById(alterSheetId);
        List<AlterSheetModuleBO> alterSheetModules = alterSheet.getAlterSheetModuleList();
        AlterPipelineInstanceModuleBO instanceModuleBO = moduleService.getAlterPipelineInstanceModules(instanceId);
        if (instanceModuleBO != null) {
            alterSheetModules = instanceModuleBO.getAlterSheetModules();
        }
        if (CollectionUtils.isEmpty(alterSheetModules)) {
            return result;
        }

        List<AlterSheetModuleBO> sourceAlterModules = alterSheetModules.stream()
                .filter(alterModule -> AlterType.SOURCE.equals(alterModule.getAlterType()) &&
                        !AlterMode.DELETE.equals(alterModule.getAlterMode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sourceAlterModules)) {
            return result;
        }

        Map<Long, AlterSheetModuleBO> alterSheetModuleMap = sourceAlterModules.stream()
                .collect(Collectors.toMap(AlterSheetModuleBO::getModuleId, Function.identity(), (v1,v2) -> v2));
        PipelineInstance pipelineInstance = pipelineExecuteService.findPipelineInstanceById(instanceId);
        Map<Long, GitCommitBO> moduleCommits = getMixPipelineInstanceModuleCommits(pipelineInstance);
        Map<Long, SourceCodeModuleBuildDTO> sourceCodeModuleCommitMap = new HashMap<>();
        if (moduleSourceCodeBuildService.getSupportSourceCodeBuildStatus(new ArrayList<>(alterSheetModuleMap.keySet()))
                .values()
                .stream()
                .anyMatch(support -> support)) {
            sourceCodeModuleCommitMap = getSourceCodeModuleMap(instanceId);
        }

        Date startTime = getClientBuildStartTime(instanceId);
        Date judgeTime = startTime != null ? startTime : pipelineInstance.getStartTime();
        for (AlterSheetModuleBO alterSheetModule : sourceAlterModules) {
            GitCommitBO gitCommit = moduleCommits.get(alterSheetModule.getModuleId());
            if (gitCommit == null) {
                if (sourceCodeModuleCommitMap.get(alterSheetModule.getModuleId()) != null) {
                    log.error("moduleId {} sourceCommit {}", alterSheetModule.getModuleId(),
                            sourceCodeModuleCommitMap.get(alterSheetModule.getModuleId()).getScmCommit());
                    result.put(alterSheetModule.getModuleId(),
                            sourceCodeModuleCommitMap.get(alterSheetModule.getModuleId()).getScmCommit());
                } else {
                    String version = alterSheetModule.getVersion() + DependencyConstant.SNAPSHOT_POSTFIX;
                    ModuleDeployRecordBO latestDeployRecord = getLatestDeployRecord(alterSheetModule.getModuleId(),
                            version, judgeTime);
                    if (latestDeployRecord != null && StringUtils.isNotEmpty(latestDeployRecord.getCommitNumber())) {
                        result.put(alterSheetModule.getModuleId(), latestDeployRecord.getCommitNumber());
                    }
                }
            } else {
                result.put(alterSheetModule.getModuleId(), gitCommit.getId());
            }
        }
        return result;
    }

    private PipelineInstanceModuleBO generateInstanceModule(AlterSheetModuleBO alterSheetModule,
                                                            DepVersionProp depVersionProp,
                                                            Map<Long, GitCommitBO> mixPipelineInstanceModuleCommits,
                                                            Date judgeTime, boolean needUpdateDetail,
                                                            Map<Long, SourceCodeModuleBuildDTO> sourceCodeModuleCommitMap) {
        ApplicationBO moduleInfo = alterSheetModule.getModule();
        AlterType alterType = alterSheetModule.getAlterType();
        String codeLibraryAddress = moduleInfo.getCodeLibraryAddress();

        BranchModelInfo branchModelInfo = integrationBranchService.getBranchModelInfo(alterSheetModule.getAlterSheetId(),
                moduleInfo);
        BranchModel branchModel = branchModelInfo.getBranchModel();

        PipelineInstanceModuleBO moduleBO = PipelineInstanceModuleBO.builder()
                .moduleId(alterSheetModule.getModuleId())
                .moduleName(moduleInfo.getName())
                .alterMode(alterSheetModule.getAlterMode())
                .alterType(alterType)
                .gitUrl(moduleInfo.getCodeLibraryAddress())
                .gitPath(GitUtils.getPathWithNamespace(moduleInfo.getCodeLibraryAddress()))
                .noticeList(new ArrayList<>())
                .branchModel(branchModel)
                .alterModuleId(alterSheetModule.getId())
                .mainModuleId(alterSheetModule.getMainModuleId())
                .module(moduleInfo)
                .requirementRelations(alterSheetModule.getRequirementRelations())
                .build();

        if (depVersionProp != null) {
            String originVersion = depVersionProp.getDepVersion();
            moduleBO.setDepVersion(originVersion);
            if (AlterType.SOURCE.equals(alterType)) {
                List<GitCommit> commitList = codeService.getCommits(codeLibraryAddress, originVersion, 0, 1);
                if (!CollectionUtils.isEmpty(commitList)) {
                    moduleBO.setDepCommitId(commitList.get(0).getId());
                }
            }
        }

        if (AlterType.SOURCE.equals(alterType)) {
            moduleBO.setVersion(alterSheetModule.getVersion() + DependencyConstant.SNAPSHOT_POSTFIX);
            GitCommitBO gitCommit = mixPipelineInstanceModuleCommits.get(alterSheetModule.getModuleId());
            if (gitCommit == null) {
                // 找一下源码构建模块的commitId
                if (sourceCodeModuleCommitMap.get(alterSheetModule.getModuleId()) != null) {
                    moduleBO.setCommitId(sourceCodeModuleCommitMap.get(alterSheetModule.getModuleId()).getScmCommit());
                    moduleBO.setBranch(sourceCodeModuleCommitMap.get(alterSheetModule.getModuleId()).getScmBranch());
                } else {
                    ModuleDeployRecordBO latestDeployRecord = getLatestDeployRecord(alterSheetModule.getModuleId(),
                            moduleBO.getVersion(), judgeTime);
                    if (latestDeployRecord == null) {
                        moduleBO.getNoticeList().add("CAN_NOT_FIND_MODULE_DEPLOY_RECORD_ON_MTL");
                    } else {
                        moduleBO.setCommitId(latestDeployRecord.getCommitNumber());
                        moduleBO.setBranch(latestDeployRecord.getScmBranch());
                    }
                }
            } else {
                moduleBO.setCommitId(gitCommit.getId());
                moduleBO.setBranch(gitCommit.getBranch());
            }
        } else if (AlterType.BINARY.equals(alterType)) {
            moduleBO.setVersion(alterSheetModule.getVersion());
        }
        if (needUpdateDetail) {
            updatePipelineInstanceModuleDetail(moduleBO);
        }
        return moduleBO;
    }

    @Override
    public void updatePipelineInstanceModuleDetail(PipelineInstanceModuleBO moduleBO) {
        if (!AlterType.SOURCE.equals(moduleBO.getAlterType())) {
            return;
        }
        if (StringUtils.isBlank(moduleBO.getGitUrl())) {
            log.warn("update submit test module detail error: git url is null");
            return;
        }

        if (StringUtils.isNotBlank(moduleBO.getCommitId())) {
            GitCommit singleCommit = codeService.getSingleCommit(moduleBO.getGitUrl(), moduleBO.getCommitId());
            if (singleCommit != null) {
                GitCommitBO gitCommitBO = GitCommitConverter.convertToGitCommitBO(singleCommit, moduleBO.getGitUrl(),
                        moduleBO.getBranch());
                moduleBO.setGitCommit(gitCommitBO);
            }
        }
        if (StringUtils.isNotBlank(moduleBO.getBranch())) {
            List<GitCommit> gitCommits = codeService.getCommits(moduleBO.getGitUrl(), moduleBO.getBranch(),
                    0, 1);
            if (!CollectionUtils.isEmpty(gitCommits)) {
                GitCommitBO gitCommitBO = GitCommitConverter.convertToGitCommitBO(gitCommits.get(0), moduleBO.getGitUrl(),
                        moduleBO.getBranch());
                moduleBO.setLatestCommitOnBranch(gitCommitBO);
            }
        }
    }

    private ModuleDeployRecordBO getLatestDeployRecord(Long moduleId, String version, Date refDate) {
        if (refDate == null) {
            return null;
        }
        ModuleDeployRecordQuery query = ModuleDeployRecordQuery.builder().moduleId(moduleId).version(version)
                .status(ModuleDeployStatus.SUCCESS).build();
        int pageNum = 0, totalPages = 1;
        while (pageNum < totalPages) {
            Page<ModuleDeployRecordBO> pageResp = moduleDeployRecordService.queryDeployRecord(query, pageNum, 10);
            totalPages = pageResp.getTotalPages();
            List<ModuleDeployRecordBO> recordList = pageResp.getContent();
            for (ModuleDeployRecordBO deployRecordBO : recordList) {
                Date gmtCreate = deployRecordBO.getGmtCreate();
                if (gmtCreate.before(refDate)) {
                    return deployRecordBO;
                }
            }
            pageNum++;
        }
        return null;
    }

    private Map<Long, GitCommitBO> getMixPipelineInstanceModuleCommits(PipelineInstance pipelineInstance) {
        Map<Long, GitCommitBO> moduleCommitMap = new HashMap<>(16);
        try {
            Long pipelineSnapshotId = pipelineInstance.getPipelineSnapshotId();
            Long pipelineId = pipelineInstance.getPipelineId();
            String pipelineType = pipelineApiService.getPipelineType(pipelineSnapshotId, pipelineId);
            if (!Type.Mixed.equals(pipelineType)) {
                return new HashMap<>(16);
            }
            List<PipelineStage> pipelineStages = null;
            if (pipelineSnapshotId != null) {
                pipelineStages = pipelineStageService.findSimpleStages(pipelineSnapshotId);
            } else if (pipelineId != null) {
                pipelineStages = pipelineStageService.findSimpleStages(pipelineId);
            }
            if (CollectionUtils.isEmpty(pipelineStages)) {
                return new HashMap<>(16);
            }
            Map<Long, Long> stageAppMap = new HashMap<>(8);
            for (PipelineStage stage : pipelineStages) {
                Map<String, String> extended = stage.getExtended();
                if (extended == null) {
                    continue;
                }
                String nodeUsage = extended.get("nodeUsage");
                String appIdValue = extended.get("appId");
                if (!"MODULE_BUILD".equals(nodeUsage) || StringUtils.isEmpty(appIdValue)) {
                    continue;
                }
                Long appId = Long.parseLong(appIdValue);
                stageAppMap.put(stage.getId(), appId);
            }

            PipelineInstanceExecuteSummary instanceExecuteSummary =
                    pipelineExecuteService.queryPipelineInstanceSummaryById(pipelineInstance.getId());
            List<StageInstanceExecuteSummary> stageExecuteSummaries = instanceExecuteSummary.getStageExecuteSummaries();
            for (StageInstanceExecuteSummary item : stageExecuteSummaries) {
                Long pipelineStageId = item.getPipelineStageId();
                Long appId = stageAppMap.get(pipelineStageId);
                if (appId == null) {
                    continue;
                }
                List<JobInstanceExecuteSummary> jobExecuteSummaries = item.getJobExecuteSummaries();
                if (CollectionUtils.isEmpty(jobExecuteSummaries)) {
                    continue;
                }
                GitCommitBO gitCommit = jobExecuteSummaries.get(0).getGitCommit();
                if (gitCommit == null) {
                    continue;
                }
                moduleCommitMap.put(appId, gitCommit);
            }
        } catch (Exception e) {
            log.error("getMixPipelineInstanceModuleCommits error:", e);
        }
        return moduleCommitMap;
    }

    private Date getClientBuildStartTime(Long pipelineInstanceId) {
        try {
            List<PipelineJobInstance> jobInstanceList = pipelineJobExecuteService
                    .findJobInstanceListByPipelineInstanceId(pipelineInstanceId);
            if (CollectionUtils.isEmpty(jobInstanceList)) {
                return null;
            }
            List<PipelineJobInstance> filterList = jobInstanceList.stream()
                    .filter(item -> RunStatus.SUCCEEDED.equals(item.getJobExecuteStatus()) && item.getStartTime() != null)
                    .sorted((o1, o2) -> {
                        if (o1.getStartTime().after(o2.getStartTime())) {
                            return 1;
                        }
                        return -1;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterList)) {
                return null;
            }
            for (PipelineJobInstance instance : filterList) {
                EnvVariables envVariables = instance.getEnvVariables();
                if (envVariables == null || envVariables.isEmpty()) {
                    continue;
                }
                String applicationIdValue = envVariables.get("application_id");
                if (StringUtils.isEmpty(applicationIdValue)) {
                    continue;
                }
                Long applicationId = Long.valueOf(applicationIdValue);
                ApplicationBO application = appInnerService.findApplication(applicationId);
                if (application != null && ApplicationType.CLIENT.equals(application.getType())) {
                    return instance.getStartTimeServer();

                }
            }
        } catch (Exception e) {
            log.error("getClientBuildStartTime error:", e);
        }
        return null;
    }

    private Map<Long, SourceCodeModuleBuildDTO> getSourceCodeModuleMap(Long pipelineId) {
        Map<Long, SourceCodeModuleBuildDTO> sourceCodeModuleMap = new HashMap<>();
        List<PipelineJobInstance> jobInstanceList = pipelineJobExecuteService
                .findJobInstanceListByPipelineInstanceId(pipelineId);
        for (PipelineJobInstance jobInstance : jobInstanceList) {
            if (jobInstance.getJobExecuteStatus() != RunStatus.SUCCEEDED) {
                continue;
            }
            String sourceCodeBuildSupportModuleListString = jobInstance.getEnvVariables().get(
                    VariableUtils.assembleBasicVariableKey("build",
                            BuildParamName.SOURCE_CODE_BUILD_SUPPORT_MODULE_LIST.getKey()));
            if (StringUtils.isEmpty(sourceCodeBuildSupportModuleListString)) {
                continue;
            }
            List<SourceCodeModuleBuildDTO> sourceCodeModuleBuildDTOList = JSON.parseArray(
                    sourceCodeBuildSupportModuleListString, SourceCodeModuleBuildDTO.class);
            sourceCodeModuleMap.putAll(sourceCodeModuleBuildDTOList.stream()
                    .collect(Collectors.toMap(SourceCodeModuleBuildDTO::getId,
                            sourceCodeModuleBuildDTO -> sourceCodeModuleBuildDTO)));
            break;
        }
        return sourceCodeModuleMap;
    }
}
