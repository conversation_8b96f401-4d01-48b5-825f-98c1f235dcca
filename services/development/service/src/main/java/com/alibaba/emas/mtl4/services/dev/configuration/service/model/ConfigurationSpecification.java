package com.alibaba.emas.mtl4.services.dev.configuration.service.model;

import com.alibaba.emas.mtl4.services.dev.api.query.ConfigurationQuery;
import com.alibaba.emas.mtl4.services.dev.configuration.domain.Configuration;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-05-07
 */
public class ConfigurationSpecification implements Specification<Configuration> {

    private ConfigurationQuery configurationQuery;

    public ConfigurationSpecification(ConfigurationQuery configurationQuery) {
        this.configurationQuery = configurationQuery;
    }

    @Override
    public Predicate toPredicate(Root<Configuration> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        if (configurationQuery.getId() != null) {
            predicates.add(criteriaBuilder.equal(root.get("id"), configurationQuery.getId()));
        }
        if (configurationQuery.getEntityType() != null) {
            predicates.add(criteriaBuilder.equal(root.get("entityType"), configurationQuery.getEntityType()));
        }
        if (configurationQuery.getEntityId() != null) {
            predicates.add(criteriaBuilder.equal(root.get("entityId"), configurationQuery.getEntityId()));
        }
        if (CollectionUtils.isNotEmpty(configurationQuery.getEntityIds())) {
            predicates.add(criteriaBuilder.in(root.get("entityId")).value(configurationQuery.getEntityIds()));
        }
        if (configurationQuery.getConfigType() != null) {
            predicates.add(criteriaBuilder.equal(root.get("configType"), configurationQuery.getConfigType()));
        }
        if (configurationQuery.getConfigAttributeName() != null) {
            predicates.add(criteriaBuilder.equal(root.get("configAttributeName"), configurationQuery.getConfigAttributeName()));
        }
        if (StringUtils.isNotEmpty(configurationQuery.getValue())) {
            predicates.add(criteriaBuilder.equal(root.get("configAttributeValue"), configurationQuery.getValue()));
        }
        if (CollectionUtils.isNotEmpty(configurationQuery.getValues())) {
            predicates.add(criteriaBuilder.in(root.get("configAttributeValue")).value(configurationQuery.getValues()));
        }

        query.orderBy(criteriaBuilder.desc(root.get("id")));
        if (predicates.isEmpty()) {
            return null;
        }
        return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
    }
}
