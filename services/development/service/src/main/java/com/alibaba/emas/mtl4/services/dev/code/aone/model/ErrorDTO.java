package com.alibaba.emas.mtl4.services.dev.code.aone.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ErrorDTO {

    @JsonProperty("error")
    @JsonAlias("errorCode")
    private String code;

    @JsonProperty("error_description")
    @JsonAlias("message")
    private String message;

    @JsonProperty("error_uri")
    private String uri;

    @JsonProperty("status")
    private Boolean status;

    @JsonProperty("trace_id")
    @JsonAlias("traceId")
    private String traceId;

}
