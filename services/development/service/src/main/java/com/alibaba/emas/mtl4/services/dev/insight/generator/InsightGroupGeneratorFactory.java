package com.alibaba.emas.mtl4.services.dev.insight.generator;

import com.alibaba.emas.mtl4.services.dev.insight.model.InsightGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class InsightGroupGeneratorFactory {

    @Autowired
    private CrashInsightGroupGenerator crashInsightGroupGenerator;

    @Autowired
    private NormalInsightGroupGenerator normalInsightGroupGenerator;

    public Optional<InsightGroupGenerator> genGroupGenerator(InsightGroup group) {
        switch (group.getName()) {
            case "crash":
                return Optional.of(crashInsightGroupGenerator);
            case "versionCover":
                return Optional.of(normalInsightGroupGenerator);
            default:
                return Optional.empty();
        }
    }
}
