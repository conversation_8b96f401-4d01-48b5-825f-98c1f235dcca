package com.alibaba.emas.mtl4.services.dev.ios.job;


import com.alibaba.emas.apple.agent.api.model.IpaUploadStatus;
import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.api.model.IpaUploadRecord;
import com.alibaba.emas.mtl4.services.dev.ios.query.IpaUploadRecordQuery;
import com.alibaba.emas.mtl4.services.dev.ios.service.IosConfigService;
import com.alibaba.emas.mtl4.services.dev.ios.service.IpaUploadManageService;
import com.alibaba.emas.mtl4.services.dev.api.enums.IpaUploadType;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.template.PipelineSceneType;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineRelationService;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 将审核通过的ipa包关联测试组
 * Created by 箫枫 on 2020/1/31.
 */
@Component
@Slf4j
public class BindTesterGroupJob extends JavaProcessor {

    @Autowired
    private IpaUploadManageService ipaUploadManageService;

    @Autowired
    private PipelineRelationService pipelineRelationService;

    @Autowired
    private IosConfigService iosConfigService;

    @Override
    public ProcessResult process(JobContext context) {
        log.error("begin to bind test group.");
        try {
            IpaUploadRecordQuery recordQuery = new IpaUploadRecordQuery();
            recordQuery.setStatuses(Arrays.asList(IpaUploadStatus.REVIEW_PASS, IpaUploadStatus.AFTER_SUBMIT_REVIEW));
            recordQuery.setType(IpaUploadType.TEST_FLIGHT);
            recordQuery.setDiscard(false);
            recordQuery.setAutoBindTestGroup(true);

            List<IpaUploadRecord> ipaUploadRecords = ipaUploadManageService.getIpaUploadRecordByQuery(recordQuery);

            log.error("begin to bind ipa. ipaUploadRecords:" + ipaUploadRecords.size());

            if (Collections.isEmpty(ipaUploadRecords)) {
                return new ProcessResult(true);
            }
            // 处理关于提审屏蔽的情况，不打开提审屏蔽时，REVIEW_PASS 状态就可以自动关联测试组，打开提审验证时，需要在 AFTER_SUBMIT_REVIEW 状态时才能自动关联测试组
            List<Long> releaseIdList = ipaUploadRecords.stream()
                    .filter(ipaUploadRecord -> Objects.equals(IpaUploadStatus.REVIEW_PASS, ipaUploadRecord.getStatus()))
                    .map(IpaUploadRecord::getReleaseId)
                    .collect(Collectors.toList());
            Map<Long, Long> releasePipelineIdMap = CollectionUtils.isEmpty(releaseIdList) ? new HashMap<>()
                : pipelineRelationService.getPipelineIdMapByEntityAndScope(EntityType.RELEASE, releaseIdList, PipelineSceneType.RELEASE_BETA_BEFORE_REVIEW.toString());

            int bindIpaCount = ipaUploadRecords.size();
            int bindIpaErrorCount = 0;
            for (IpaUploadRecord ipaUploadRecord : ipaUploadRecords) {
                // 如果时 REVIEW_PASS 状态并且包含提审验证流水线，此时这个状态不能直接自动关联到测试组，需要等待审后验证完成
                if (Objects.equals(IpaUploadStatus.REVIEW_PASS, ipaUploadRecord.getStatus()) && Objects.nonNull(
                        releasePipelineIdMap.get(ipaUploadRecord.getReleaseId()))) {
                    continue;
                }

                try {
                    BizResult<Void> result =  ipaUploadManageService.bindTestGroup(ipaUploadRecord.getCreator(), ipaUploadRecord);
                    if (!result.isSuccess()){
                        log.error("fail to bind test group. errorMsg: {}", result.getErrorMsg());
                        bindIpaErrorCount++;
                    }
                } catch (Exception ex){
                    bindIpaErrorCount++;
                    log.error("fail to add ipa access to test group", ex);
                }
            }
            boolean isSuccess = bindIpaErrorCount == 0;
            return new ProcessResult(isSuccess, "bindIpaCount: " + bindIpaCount + ", bindIpaErrorCount:" + bindIpaErrorCount);
        } catch (Exception ex) {
            log.error("fail to execute BindTesterGroupJob", ex);
            return new ProcessResult(false, ex.getMessage());
        }
    }
}