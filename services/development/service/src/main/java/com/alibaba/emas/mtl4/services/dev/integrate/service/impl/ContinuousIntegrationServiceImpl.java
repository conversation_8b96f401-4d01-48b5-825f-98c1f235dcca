package com.alibaba.emas.mtl4.services.dev.integrate.service.impl;

import com.alibaba.emas.mtl4.commons.pipes.RunStatus;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.services.dev.alter.model.MainFrameworkChangeRecord;
import com.alibaba.emas.mtl4.services.dev.alter.service.MainFrameworkChangeRecordService;
import com.alibaba.emas.mtl4.services.dev.api.enums.*;
import com.alibaba.emas.mtl4.services.dev.api.model.*;
import com.alibaba.emas.mtl4.services.dev.api.model.git.GitBranchInfo;
import com.alibaba.emas.mtl4.services.dev.api.model.git.GitTag;
import com.alibaba.emas.mtl4.services.dev.api.model.git.MergeRequestAcceptResult;
import com.alibaba.emas.mtl4.services.dev.api.release.model.PublishType;
import com.alibaba.emas.mtl4.services.dev.api.release.model.ReleaseBO;
import com.alibaba.emas.mtl4.services.dev.api.release.model.ReleaseType;
import com.alibaba.emas.mtl4.services.dev.api.service.DependencySnapshotService;
import com.alibaba.emas.mtl4.services.dev.api.service.UserRoleService;
import com.alibaba.emas.mtl4.services.dev.app.enums.ModuleDeployStatus;
import com.alibaba.emas.mtl4.services.dev.app.model.ModuleDeployRecordBO;
import com.alibaba.emas.mtl4.services.dev.app.service.AppInnerService;
import com.alibaba.emas.mtl4.services.dev.app.service.ModuleDeployRecordService;
import com.alibaba.emas.mtl4.services.dev.app.service.VersionService;
import com.alibaba.emas.mtl4.services.dev.code.AoneCodeService;
import com.alibaba.emas.mtl4.services.dev.code.CodeService;
import com.alibaba.emas.mtl4.services.dev.code.ScmRepoService;
import com.alibaba.emas.mtl4.services.dev.code.constant.GitOperationConstant;
import com.alibaba.emas.mtl4.services.dev.code.model.MergeRequestDetailDTO;
import com.alibaba.emas.mtl4.services.dev.code.model.MergeRequestQuery;
import com.alibaba.emas.mtl4.services.dev.code.model.MergeState;
import com.alibaba.emas.mtl4.services.dev.common.LockService;
import com.alibaba.emas.mtl4.services.dev.common.lock.LockTicket;
import com.alibaba.emas.mtl4.services.dev.configuration.ConfigurationInnerService;
import com.alibaba.emas.mtl4.services.dev.dependency.service.BaseDependencyInnerService;
import com.alibaba.emas.mtl4.services.dev.git.model.*;
import com.alibaba.emas.mtl4.services.dev.gitops.service.ModuleGitOperationService;
import com.alibaba.emas.mtl4.services.dev.integrate.IntegrationSheetAreaRelationService;
import com.alibaba.emas.mtl4.services.dev.integrate.enums.VersionComparationResult;
import com.alibaba.emas.mtl4.services.dev.integrate.model.*;
import com.alibaba.emas.mtl4.services.dev.integrate.query.IntegrateSheetQuery;
import com.alibaba.emas.mtl4.services.dev.integrate.service.*;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineExecuteService;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineRelationService;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineRelation;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.pipeline.PipelineInstance;
import com.alibaba.emas.mtl4.services.dev.release.service.ReleaseService;
import com.alibaba.emas.mtl4.services.dev.versionPlan.VersionPlanService;
import com.alibaba.emas.mtl4.services.dev.versionPlan.model.VersionPlanBO;
import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.alibaba.emas.mtl4.services.dev.code.constant.GitOperationConstant.SYSTEM_MODIFIER;
import static com.alibaba.emas.mtl4.services.dev.integrate.constants.CiAreaConstants.VERSION_PLAN_OUT_OF_DATE;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2021-08-27
 */
@Service
@Slf4j
public class ContinuousIntegrationServiceImpl implements ContinuousIntegrationService {
    @Autowired
    private IntegrateAreaModuleInnerService areaModuleInnerService;
    @Autowired
    private IntegrateSheetInnerService integrateSheetInnerService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private IntegrationSheetAreaRelationService areaRelationService;
    @Autowired
    private CiModuleRelationService ciModuleRelationService;
    @Autowired
    private DependencySnapshotService dependencySnapshotService;
    @Autowired
    private BaseDependencyInnerService baseDependencyInnerService;
    @Autowired
    private VersionService versionService;
    @Autowired
    private IntegrateSheetModuleInnerService integrationModuleInnerService;
    @Autowired
    private IntegrateAreaInnerService integrateAreaInnerService;
    @Autowired
    private VersionPlanService versionPlanService;
    @Autowired
    private ScmRepoService scmRepoService;
    @Autowired
    private CodeService codeService;
    @Autowired
    private AppInnerService appInnerService;
    @Autowired
    private ModuleDeployRecordService deployRecordService;
    @Autowired
    private ModuleGitOperationService moduleGitOperationService;
    @Autowired
    private PipelineExecuteService pipelineExecuteService;
    @Autowired
    private PipelineRelationService pipelineRelationService;
    @Autowired
    private ContinuousIntegrateAreaService ciAreaService;
    @Autowired
    private ReleaseService releaseService;
    @Autowired
    private ConfigurationInnerService configurationInnerService;
    @Autowired
    private CiReleaseBranchService ciReleaseBranchService;
    @Autowired
    private CiDingMessageService messageService;
    @Autowired
    private IntegrateSheetDingMessageService integrateSheetDingMessageService;
    @Autowired
    private IntegrateAreaStatusManager integrateAreaStatusManager;
    @Autowired
    private DependencyFileSyncService dependencyFileSyncService;
    @Autowired
    private MainFrameworkChangeRecordService changeRecordService;
    @Autowired
    private AoneCodeService aoneCodeService;
    @Autowired
    private LockService lockService;
    @Autowired
    protected RedissonClient redissonClient;

    /**
     * 线程池发送
     */
    private final ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
        .setNameFormat("continuous-integrate-area-module-refresh-thread-%d")
        .setDaemon(true)
        .build();

    private final ExecutorService threadPool = new ThreadPoolExecutor(4, 8,
        0L, TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(10), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

    private final static String CI_INTEGRATION_MAPPING_CACHE_NAME = "mtl4:ci-integration-mapping-cache";

    protected RMapCache<Long, IntegrateSheetBO> ciIntegrationMappingCache;

    @PostConstruct
    public void initCiIntegrationMappingCache() {
        ciIntegrationMappingCache = redissonClient.getMapCache(CI_INTEGRATION_MAPPING_CACHE_NAME, new JsonJacksonCodec());
    }

    @Override
    public void checkWhetherAllowSync(Long ciAreaId, IntegrateAreaBO integrateAreaBO) {
        List<IntegrateAreaModuleBO> ciModules = areaModuleInnerService.getAllIntegrateAreaModules(ciAreaId);
        checkWhetherAllowSync(ciAreaId, integrateAreaBO, ciModules);
    }

    @Override
    public void syncCiModules(Long appId, Long ciAreaId, Long integrateAreaId, String user) {
        List<IntegrateAreaModuleBO> ciModules = areaModuleInnerService.getAllIntegrateAreaModules(ciAreaId);
        syncCiAreaModules(appId, ciAreaId, integrateAreaId, ciModules, user);
    }

    @Override
    public void syncCiModules(Long appId, Long ciAreaId, IntegrateAreaBO area, String user) {
        List<IntegrateAreaModuleBO> ciModules = areaModuleInnerService.getAllIntegrateAreaModules(ciAreaId);
        syncCiAreaModules(appId, ciAreaId, area, ciModules, user);
    }

    @Override
    public void partlySyncCiModules(Long appId, Long ciAreaId, Long integrateAreaId, String user) {
        List<IntegrateAreaModuleBO> notSyncedCiModules = getNotSyncedCiModules(ciAreaId, integrateAreaId);
        syncCiAreaModules(appId, ciAreaId, integrateAreaId, notSyncedCiModules, user);
    }

    @Override
    public void partlySyncCiModules(Long appId, Long ciAreaId, IntegrateAreaBO area, String user) {
        List<IntegrateAreaModuleBO> notSyncedCiModules = getNotSyncedCiModules(ciAreaId, area.getId());
        syncCiAreaModules(appId, ciAreaId, area, notSyncedCiModules, user);
    }

    @Override
    public List<IntegrateAreaModuleBO> getNotSyncedCiModules(Long ciAreaId, Long integrateAreaId) {
        List<IntegrateAreaModuleBO> ciModules = areaModuleInnerService.getAllIntegrateAreaModules(ciAreaId);
        List<CiModuleRelationBO> moduleRelations = ciModuleRelationService.getCiModuleRelationsByAreaId(integrateAreaId,
                ciAreaId);
        Map<Long, List<CiModuleRelationBO>> moduleRelationMap = moduleRelations.stream()
                .collect(Collectors.groupingBy(CiModuleRelationBO::getModuleId));

        List<IntegrateAreaModuleBO> notAutoSyncedCiModules = ciModules.stream().filter(ciModule -> {
            List<CiModuleRelationBO> moduleRelationList = moduleRelationMap.get(ciModule.getModuleId());
            if (CollectionUtils.isEmpty(moduleRelationList)) {
                return true;
            }
            List<String> integrationVersions = moduleRelationList.stream().map(CiModuleRelationBO::getVersion)
                    .collect(Collectors.toList());
            return !integrationVersions.contains(ciModule.getIntegrateVersion());
        }).collect(Collectors.toList());
        //未自动同步的模块排除通过其他方式集成的
        return notAutoSyncedCiModules.stream().filter(ciModule -> {
            boolean hasIntegrated = integrationModuleInnerService.hasBeenIntegrated(ciModule.getModuleId(),
                    integrateAreaId, ciModule.getIntegrateVersion());
            return !hasIntegrated;
        }).collect(Collectors.toList());
    }

    @Override
    public void syncCiAreaModules(Long appId, Long ciAreaId, Long integrateAreaId,
                                  List<IntegrateAreaModuleBO> ciModules, String user) {
        IntegrateAreaBO integrateAreaBO = integrateAreaInnerService.getIntegrateArea(integrateAreaId);
        syncCiAreaModules(appId, ciAreaId, integrateAreaBO, ciModules, user);
    }

    @Override
    public void syncCiAreaModules(Long appId, Long ciAreaId, IntegrateAreaBO area,
                                  List<IntegrateAreaModuleBO> ciModules, String user) {
        if (CollectionUtils.isEmpty(ciModules)) {
            log.error("ciAreaId {} integrateAreaModules are empty", ciAreaId);
            return;
        }
        checkWhetherAllowSync(ciAreaId, area, ciModules);

        List<Long> integrateSheetIdList = ciModules.stream().
                map(IntegrateAreaModuleBO::getIntegrateSheetId)
                .distinct()
                .collect(Collectors.toList());
        IntegrateSheetQuery integrateSheetQuery = new IntegrateSheetQuery();
        integrateSheetQuery.setIds(integrateSheetIdList);
        List<IntegrateSheetInnerBO> integrateSheets = integrateSheetInnerService.findIntegrateSheetList(
                integrateSheetQuery, null);

        List<IntegrateSheetInnerBO> unmatchedIntegrateSheets = integrateSheets.stream()
                .filter(integrateSheet -> integrateSheet.getVersionPlanId() != null &&
                        !integrateSheet.getVersionPlanId().equals(area.getVersionPlanId()))
                .collect(Collectors.toList());
        handleUnmatchedIntegrateSheets(area, ciModules, unmatchedIntegrateSheets);

        //根据关联的版本计划过滤集成单
        List<IntegrateSheetInnerBO> filteredIntegrateSheets = integrateSheets.stream()
                .filter(integrateSheet -> integrateSheet.getVersionPlanId() == null ||
                        area.getVersionPlanId().equals(integrateSheet.getVersionPlanId()))
                .collect(Collectors.toList());

        VersionPlanBO areaVersionPlan = null;
        if (area.getVersionPlanId() != null) {
            areaVersionPlan = versionPlanService.getVersionPlanById(area.getVersionPlanId());
        }
        VersionPlanBO currentVersionPlan = areaVersionPlan;
        Map<IntegrateSheetInnerBO, NormalIntegrationCheckResult> integrationCheckResult = new HashMap<>(256);
        if (!IntegrateAreaStatus.OPEN.equals(area.getStatus())) {
            List<String> allowedSyncStatus = getAllowedSyncStatus(appId);
            //冻结且为受限版本才支持同步
            if (!isLimitedVersionPlanFreezed(area, currentVersionPlan) &&
                    !allowedSyncStatus.contains(area.getStatus().name())) {
                if (needSendCISyncCancelledMessage(area)) {
                    //带有viAreaId的集成单表明集成的时候按照版本集成区的状态正常集成/紧急集成
                    List<IntegrateSheetInnerBO> integrateSheetsWithoutViAreaId = filteredIntegrateSheets.stream()
                            .filter(integrateSheet -> integrateSheet.getViAreaId() == null &&
                                    !isLimitedVersionPlanIntegrated(integrateSheet, currentVersionPlan))
                            .collect(Collectors.toList());
                    messageService.sendCISyncCancelledMessage(ciAreaId, area, integrateSheetsWithoutViAreaId);
                }
                log.error("ciAreaId {} not allowed sync to areaId {} with status {}", ciAreaId, area.getId(), area.getStatus());
                return;
            }
            integrationCheckResult = getIntegrationCheckResult(area, currentVersionPlan, filteredIntegrateSheets);
            filteredIntegrateSheets = integrationCheckResult.entrySet().stream()
                    .filter(entry -> entry.getValue() != null && entry.getValue().isCanNormalIntegration())
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(filteredIntegrateSheets)) {
            syncMainFrameworkChangeRecord(area.getVersionPlanId(), ciAreaId, area);
            syncCiAreaModulesWithLock(appId, ciAreaId, area, ciModules, user, filteredIntegrateSheets);
        }
        messageService.sendCISyncNotAllowedMessage(ciAreaId, area, integrationCheckResult);
    }


    private void checkWhetherAllowSync(Long ciAreaId, IntegrateAreaBO area,
                                       List<IntegrateAreaModuleBO> ciModules) {
        //校验持续集成区最新流水线实例的运行状态
        validatePipelineInstanceRunStatus(ciAreaId);
        //校验持续集成区和集成区的基础依赖是否一致
        boolean dependencyValidationResult = validateBaseDependency(ciAreaId, area.getId());
        Assert.isTrue(dependencyValidationResult, String.format("持续集成区和集成区[%s]的基础依赖不一致", area.getName()));
        //校验持续集成区的模块依赖变更方式是否合理
        boolean alterTypeValidation = ciAreaService.isConflictWithBaseDependency(ciModules);
        Assert.isFalse(alterTypeValidation, "持续集成区部分模块采用的变更模式存在冲突");
        //集成区是否已关闭
        Assert.isFalse(IntegrateAreaStatus.CLOSED.equals(area.getStatus()), String.format("集成区[%s]已关闭", area.getName()));
    }

    private void handleUnmatchedIntegrateSheets(IntegrateAreaBO area,
                                                List<IntegrateAreaModuleBO> ciModules,
                                                List<IntegrateSheetInnerBO> unmatchedIntegrateSheets) {
        if (CollectionUtils.isEmpty(unmatchedIntegrateSheets)) {
            return;
        }
        VersionPlanBO currentVersionPlan = versionPlanService.getVersionPlanById(area.getVersionPlanId());
        Map<Long, List<IntegrateSheetInnerBO>> versionPlanMap = unmatchedIntegrateSheets.stream()
                .collect(Collectors.groupingBy(IntegrateSheetInnerBO::getVersionPlanId));
        List<VersionPlanBO> versionPlans = versionPlanService.queryVersionPlans(new ArrayList<>(versionPlanMap.keySet()));

        versionPlans.forEach(versionPlan -> {
            if (versionPlan.getStartTime().before(currentVersionPlan.getStartTime())) {
                //版本计划已经过期 但是还没有同步到版本集成区的模块需要移除
                List<Long> integrateSheetIds = versionPlanMap.get(versionPlan.getId()).stream()
                       .map(IntegrateSheetInnerBO::getId)
                       .collect(Collectors.toList());
//                List<IntegrateAreaModuleBO> toBeRemovedCiModules = ciModules.stream()
//                        .filter(ciModule -> integrateSheetIds.contains(ciModule.getIntegrateSheetId()))
//                        .collect(Collectors.toList());
//                toBeRemovedCiModules.forEach(toBeRemovedCiModule -> {
//                    BizResult<Boolean> removeResult = areaModuleInnerService.removeIntegrateAreaModuleWithReason(toBeRemovedCiModule.getId(),
//                            toBeRemovedCiModule.getIntegrateVersion(), VERSION_PLAN_OUT_OF_DATE, SYSTEM_MODIFIER);
//                    //TODO 针对新增模块处理iOS持续集成区Podfile
//                    if (removeResult.isSuccess()) {
//                        log.info("remove integrateAreaModuleBO {} success", toBeRemovedCiModule.getId());
//                    }
//                });
                messageService.sendVersionPlanOutOfDateMessage(currentVersionPlan, versionPlan, area,
                        versionPlanMap.get(versionPlan.getId()));
            } else {
                if (versionPlan.getStartTime().before(new Date())) {
                    messageService.sendVersionPlanHasStartedMessage(versionPlan, area, versionPlanMap.get(versionPlan.getId()));
                } else {
                    messageService.sendVersionPlanNotStartedMessage(currentVersionPlan, versionPlan, area,
                            versionPlanMap.get(versionPlan.getId()));
                }
            }
        });
    }

    //将壳工程变更同步到版本集成区 只同步一次
    private void syncMainFrameworkChangeRecord(Long versionPlanId, Long ciAreaId, IntegrateAreaBO area) {
        if (versionPlanId == null) {
            return;
        }
        List<MainFrameworkChangeRecord> changeRecords =
                changeRecordService.getVersionPlanRelatedChangeRecords(versionPlanId);
        List<MainFrameworkChangeRecord> mergedChangeRecords = changeRecords.stream()
                .filter(changeRecord -> changeRecord.getCodeReviewRecord() != null &&
                        MergeState.MERGED.getState().equals(changeRecord.getCodeReviewRecord().getState()))
                .filter(changeRecord -> changeRecord.getTargetBranch() != null &&
                        changeRecord.getTargetBranch().startsWith(GitOperationConstant.CI_AREA_BRANCH_NAME_PREFIX))
                .collect(Collectors.toList());
        //持续集成区分支上没有发起过壳工程变更 直接返回
        if (CollectionUtils.isEmpty(mergedChangeRecords)) {
            return;
        }

        IntegrateAreaBO ciArea = integrateAreaInnerService.getIntegrateArea(ciAreaId);
        MergeRequestQuery query = new MergeRequestQuery();
        query.setPage(0);
        query.setPerPage(1);
        query.setScmAddress(ciArea.getIntegrateApplication().getCodeLibraryAddress());
        query.setSourceBranch(ciArea.getIntegrateBranch());
        query.setTargetBranch(area.getIntegrateBranch());
        List<MergeRequestDetailDTO> mergeRequests = aoneCodeService.getMergeRequests(query);
        //已经同步过了 无须再同步
        if (CollectionUtils.isNotEmpty(mergeRequests)) {
            return;
        }

        syncMainFrameworkChangeRecordToAreaBranch(ciArea, area, mergedChangeRecords.get(0));
    }

    private void syncMainFrameworkChangeRecordToAreaBranch(IntegrateAreaBO ciArea, IntegrateAreaBO area,
                                                           MainFrameworkChangeRecord changeRecord) {
        ApplicationBO applicationBO = ciArea.getIntegrateApplication();
        BizResult<MergeRequestAcceptResult> mergeResult = scmRepoService.createMergeRequestAndAccept(
                applicationBO.getCodeLibraryAddress(), ciArea.getIntegrateBranch(),
                area.getIntegrateBranch(), null,
                "[EMAS-MTL壳工程变更] Merge branch:" + ciArea.getIntegrateBranch() + " to " + area.getIntegrateBranch(),
                false);
        log.error("syncMainFrameworkChangeRecordToAreaBranch mergeResult {}", mergeResult);
        if (!mergeResult.isSuccess()) {
            MergeRequestAcceptResult acceptResult = mergeResult.getData();
            Integer mergeRequestId = null;
            if (acceptResult != null && acceptResult.getId() != null) {
                mergeRequestId = acceptResult.getId();
            }
            messageService.sendChangeRecordSyncFailedMessage(area,
                    changeRecord.getCodeReviewId(), changeRecord.getAlterSheetId(),
                    applicationBO.getCodeLibraryAddress(), changeRecord.getCreator(),
                    mergeResult.getErrorMsg());
        }
    }

    @Override
    public void syncCiAreaModulesWithLock(Long appId, Long ciAreaId, IntegrateAreaBO area,
                                          List<IntegrateAreaModuleBO> ciModules, String user,
                                          List<IntegrateSheetInnerBO> filteredIntegrateSheets) {
        String lockName = appId + "_" + ciAreaId + "_" + area.getId();
        LockTicket lockTicket = lockService.tryLock(lockName, 1, 3 * 60 * 1000, TimeUnit.MILLISECONDS);
        if (!lockTicket.isSuccess()) {
            log.error("getLock {} failed", lockName);
            return;
        }
        try {
            this.syncCiAreaModulesWithoutLimit(appId, ciAreaId, area, ciModules, user, filteredIntegrateSheets);
        } finally {
            lockTicket.release();
        }
    }

    @Override
    public void syncCiAreaModulesWithLockWait(Long appId, Long ciAreaId, IntegrateAreaBO area,
                                              List<IntegrateAreaModuleBO> ciModules, String user,
                                              List<IntegrateSheetInnerBO> filteredIntegrateSheets) {
        String lockName = appId + "_" + ciAreaId + "_" + area.getId();
        LockTicket lockTicket = lockService.tryLock(lockName, 60 * 1000, 3 * 60 * 1000, TimeUnit.MILLISECONDS);
        if (!lockTicket.isSuccess()) {
            log.error("syncCiAreaModulesWithLockWait getLock {} failed", lockName);
            return;
        }
        try {
            this.syncCiAreaModulesWithoutLimit(appId, ciAreaId, area, ciModules, user, filteredIntegrateSheets);
        } finally {
            lockTicket.release();
        }

    }

    @Override
    public boolean syncCiAreaModulesWithoutLimit(Long appId, Long ciAreaId, IntegrateAreaBO area,
                                                 List<IntegrateAreaModuleBO> ciModules, String user,
                                                 List<IntegrateSheetInnerBO> ciIntegrations) {
        log.info("syncCiAreaModulesWithoutLimit appId: {}, ciAreaId: {}, areaId: {}, " +
                        "ciModules: {}, user: {}, ciIntegrations: {}", appId, ciAreaId, area.getId(),
                JSON.toJSONString(ciModules), user, JSON.toJSONString(ciIntegrations));
        IntegrateAreaBO ciArea = integrateAreaInnerService.getIntegrateArea(ciAreaId);
        Long integrateAreaId = area.getId();
        Map<Long, IntegrateSheetInnerBO> ciIntegrationMap = ciIntegrations.stream()
                .collect(Collectors.toMap(IntegrateSheetInnerBO::getId, Function.identity()));
        //依次创建集成单
        Map<Long, Long> integrateSheetIdMap = new HashMap<>(256);
        List<IntegrateSheetBO> autoSavedIntegrateSheets = new ArrayList<>();
        List<IntegrateSheetModuleBO> integrationModules = new ArrayList<>();
        for (IntegrateSheetInnerBO ciIntegration : ciIntegrations) {
            IntegrateSheetBO integrateSheetBO = ciIntegrationMappingCache.get(ciIntegration.getId());
            if (integrateSheetBO == null) {
                integrateSheetBO = generateAndSaveIntegrateSheet(ciIntegration, integrateAreaId, user);
                ciIntegrationMappingCache.put(ciIntegration.getId(), integrateSheetBO, 6, TimeUnit.HOURS);
            } else {
                log.error("processedCiIntegrationId {}", ciIntegration.getId());
            }
            autoSavedIntegrateSheets.add(integrateSheetBO);
            integrateSheetIdMap.put(ciIntegration.getId(), integrateSheetBO.getId());
            integrationModules.addAll(integrateSheetBO.getIntegrateSheetModuleBOList());
        }

        ciModules = ciModules.stream()
                .filter(ciModule -> ciModule.getIntegrateSheetId() == null || ciIntegrationMap.containsKey(ciModule.getIntegrateSheetId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ciModules)) {
            log.error("empty ciModules");
            return true;
        }

        //集成版本高低检查结果
        Map<Long, IntegrateVersionCheckResult> versionCheckMap = checkIntegrateVersion(integrateAreaId, ciModules);
        fillUpVersionCheckResult(integrateAreaId, versionCheckMap);

        //提交集成单
        List<CiModuleRelationBO> ciModuleRelations = new ArrayList<>();
        List<IntegrateSheetModuleBO> integratedModules = new ArrayList<>();
        List<IntegrateSheetModuleInnerBO> innerIntegratedModules = new ArrayList<>();
        List<IntegrateSheetModuleInnerBO> innerIntegrationModules = new ArrayList<>();
        if (!integrateSheetIdMap.isEmpty()) {
            innerIntegrationModules.addAll(integrationModuleInnerService.batchFindIntegrateSheetModules(
                    new ArrayList<>(integrateSheetIdMap.values())));
        }
        List<IntegrateAreaModuleBO> integrateAreaModules = areaModuleInnerService.getAllIntegrateAreaModules(integrateAreaId);
        Map<Long, IntegrateAreaModuleBO> integrateAreaModuleMap = integrateAreaModules.stream()
                .collect(Collectors.toMap(IntegrateAreaModuleBO::getModuleId, Function.identity()));
        ciModules.forEach(ciModule -> {
            IntegrateAreaModuleBO previousAreaModule = integrateAreaModuleMap.get(ciModule.getModuleId());
            IntegrateAreaModuleBO areaModuleBO = generateIntegrateAreaModule(previousAreaModule,
                    integrateSheetIdMap.get(ciModule.getIntegrateSheetId()), ciModule, integrateAreaId, user);
            Long areaModuleId = areaModuleInnerService.saveOrUpdateIntegrateAreaModule(areaModuleBO);
            Long alterModuleId = areaModuleBO.getAlterModuleId();
            String integrateVersion = areaModuleBO.getIntegrateVersion();
            //回滚可能没有集成单
            List<IntegrateSheetModuleInnerBO> matchedInnerIntegrationModules = innerIntegrationModules.stream()
                    .filter(integrateSheetModule -> integrateSheetModule.getIntegrateSheetId().equals(areaModuleBO.getIntegrateSheetId()))
                    .collect(Collectors.toList());
            for (IntegrateSheetModuleInnerBO integrationModule : matchedInnerIntegrationModules) {
                if (alterModuleId.equals(integrationModule.getAlterModuleId()) &&
                        integrateVersion.equals(integrationModule.getIntegrateVersion())) {
                    integrationModuleInnerService.updateIntegrateAreaModuleId(integrationModule.getId(),
                            areaModuleId, user);
                    integrationModule.setIntegrateAreaModuleId(areaModuleId);
                    innerIntegratedModules.add(integrationModule);
                    break;
                }
            }

            List<IntegrateSheetModuleBO> matchedIntegrationModules = integrationModules.stream()
                    .filter(integrateSheetModuleBO -> alterModuleId.equals(integrateSheetModuleBO.getAlterModuleId()) &&
                            integrateVersion.equals(integrateSheetModuleBO.getIntegrateVersion()))
                    .collect(Collectors.toList());
            if (!matchedIntegrationModules.isEmpty()) {
                integratedModules.add(matchedIntegrationModules.get(0));
            }

            CiModuleRelationBO moduleRelation = generateCiModuleRelation(integrateAreaId, ciAreaId,
                    ciModule.getId(), areaModuleId, ciModule.getModuleId(), ciModule.getIntegrateVersion(), user);
            ciModuleRelations.add(moduleRelation);
        });

        //处理monorepo子模块
        handleIntegratedSubmodules(integrateAreaModules, innerIntegratedModules, ciModules, integrateAreaId, user);

        //提交集成单
        autoSubmitIntegrateSheets(autoSavedIntegrateSheets);

        //保存集成单、集成区和持续集成区的关联关系
        batchSaveIntegrationSheetAreaRelation(integrateSheetIdMap, integrateAreaId, ciAreaId, user);

        //保存持续集成区模块和集成区模块的关联关系
        ciModuleRelationService.batchSaveCiModuleRelation(ciModuleRelations);

        integrateSheetDingMessageService.sendLowerVersionIntegratedMessage(integrateAreaId, versionCheckMap, user);
        //持续集成新模式会实时同步 不需要保存依赖快照
        if (!appInnerService.useNewCiMode(appId)) {
            archiveDependencySnapshot(appId, ciAreaId);
        }

        //持续集成区和版本集成区不是同一个分支  则批量更新版本集成区分支的依赖文件
        if (!ciArea.getIntegrateBranch().equals(area.getIntegrateBranch())) {
            IntegrateSheetInnerBO integrateSheetInnerBO = new IntegrateSheetInnerBO();
            integrateSheetInnerBO.setApplicationId(appId);
            integrateSheetInnerBO.setIntegrateAreaId(integrateAreaId);
            integrateSheetInnerBO.setDescription("submit continuousIntegrateArea modules to integrateArea:" + integrateAreaId);
            integrateSheetInnerBO.setIntegrateSheetModuleBOList(integratedModules);
            dependencyFileSyncService.asyncSyncDependencyFileInGitlab(integrateAreaId, integrateSheetInnerBO);
        }

        //自动切换采用集成分支模式的模块在变更单中的开发分支
        performReleaseBranchOperation(ciModules, innerIntegratedModules, integrateAreaId, appId);

        //切换变更单的基础依赖
        List<Long> alterSheetIds = ciIntegrationMap.values()
                .stream()
                .map(IntegrateSheetInnerBO::getAlterSheetId)
                .collect(Collectors.toList());
        if (!appInnerService.useNewCiMode(appId)) {
            // 开启新模式后，持续集成迭代基础依赖固定为持续集成区，不切换
            switchBaseDependency(integrateAreaId, alterSheetIds);
        }

        //发送钉钉消息
        messageService.sendCiSyncSuccessMessage(ciAreaId, area, integrateSheetIdMap, ciIntegrationMap);
        messageService.sendDependencyAddedMessage(ciAreaId, ciModules);
        return true;
    }



    private void handleIntegratedSubmodules(List<IntegrateAreaModuleBO> integrateAreaModules,
                                            List<IntegrateSheetModuleInnerBO> innerIntegratedModules,
                                            List<IntegrateAreaModuleBO> ciModules,
                                            Long integrateAreaId,
                                            String user) {
        Map<Long, List<IntegrateAreaModuleBO>> originalSubAreaModuleMap = integrateAreaModules.stream()
                .filter(areaModuleBO -> areaModuleBO.getMainModuleId() != null)
                .collect(groupingBy(IntegrateAreaModuleBO::getMainModuleId, toList()));

        Map<Long, List<IntegrateSheetModuleInnerBO>> subIntegrationModuleMap = innerIntegratedModules
                .stream()
                .filter(integrationModule -> integrationModule.getMainModuleId() != null)
                .collect(groupingBy(IntegrateSheetModuleInnerBO::getMainModuleId, toList()));

        ciModules.forEach(ciModule -> {
            Optional<List<IntegrateSheetModuleInnerBO>> subIntegrationModuleOptional =
                    Optional.ofNullable(subIntegrationModuleMap.get(ciModule.getModuleId()));
            List<Long> currentSubmoduleIds = subIntegrationModuleOptional
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(IntegrateSheetModuleInnerBO::getModuleId)
                    .collect(toList());

            Optional<List<IntegrateAreaModuleBO>> subAreaModuleOptional =
                    Optional.ofNullable(originalSubAreaModuleMap.get(ciModule.getModuleId()));
            List<Long> previousSubmoduleIds = subAreaModuleOptional
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(IntegrateAreaModuleBO::getModuleId)
                    .collect(toList());

            List<Long> toBeDeletedModuleIds = previousSubmoduleIds.stream()
                    .filter(submoduleId -> !currentSubmoduleIds.contains(submoduleId))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(toBeDeletedModuleIds)) {
                //删除本次未集成的子模块
                areaModuleInnerService.deleteSubAreaModules(integrateAreaId, ciModule.getModuleId(),
                        toBeDeletedModuleIds, user);
            }
        });
    }


    private Map<IntegrateSheetInnerBO, NormalIntegrationCheckResult> getIntegrationCheckResult(
            IntegrateAreaBO area,
            VersionPlanBO currentVersionPlan,
            List<IntegrateSheetInnerBO> integrateSheets) {
        Map<IntegrateSheetInnerBO, NormalIntegrationCheckResult> resultMap = new HashMap<>(256);
        integrateSheets.forEach(integrateSheet -> {
            NormalIntegrationCheckResult checkResult = new NormalIntegrationCheckResult();
            if (integrateSheet.getViAreaId() != null) {
                checkResult.setCanNormalIntegration(true);
            } else {
                //受限版本已经紧急集成过了
                if (isLimitedVersionPlanIntegrated(integrateSheet, currentVersionPlan)) {
                    checkResult.setCanNormalIntegration(true);
                } else {
                    checkResult = integrateSheetInnerService.checkIntegrateAreaStatusWhenIntegration(integrateSheet,
                            area, area.getCreator());
                }
            }
            resultMap.put(integrateSheet, checkResult);
        });
        return resultMap;
    }

    //是否受限版本的冻结
    private boolean isLimitedVersionPlanFreezed(IntegrateAreaBO area, VersionPlanBO versionPlanBO) {
        return IntegrateAreaStatus.FREEZED.equals(area.getStatus()) &&
                versionPlanBO != null &&
                versionPlanBO.getIsLimited() != null &&
                versionPlanBO.getIsLimited();
    }

    //是否受限版本紧急集成
    private boolean isLimitedVersionPlanIntegrated(IntegrateSheetInnerBO integrateSheet, VersionPlanBO versionPlanBO) {
        return integrateSheet.getVersionPlanId() != null &&
                versionPlanBO != null &&
                versionPlanBO.getIsLimited() != null &&
                versionPlanBO.getIsLimited() &&
                IntegrateSheetType.EMERGENCY.equals(integrateSheet.getIntegrateSheetType());
    }

    private void performReleaseBranchOperation(List<IntegrateAreaModuleBO> ciModules,
                                               List<IntegrateSheetModuleInnerBO> innerIntegratedModules,
                                               Long integrateAreaId, Long appId) {
        List<IntegrateSheetModuleGitParam> moduleGitParams = ciModules.stream()
                .map(continuousIntegrateAreaModule -> {
                    IntegrateSheetModuleGitParam moduleGitParam = new IntegrateSheetModuleGitParam();
                    moduleGitParam.setModuleId(continuousIntegrateAreaModule.getModuleId());
                    moduleGitParam.setAutoSwitchToRelease(true);
                    return moduleGitParam;
                }).collect(Collectors.toList());
        ciReleaseBranchService.asyncPerformReleaseBranchOperation(innerIntegratedModules, moduleGitParams,
                integrateAreaId, appId);
    }

    public void performReleaseBranchOperation(Long applicationId, Long integrateSheetId, Long integrateAreaId) {
        List<IntegrateSheetModuleInnerBO> innerIntegrationModules =
                integrationModuleInnerService.findAllIntegrateSheetModuleList(integrateSheetId);
        List<IntegrateSheetModuleGitParam> moduleGitParams = innerIntegrationModules.stream()
                .map(continuousIntegrateAreaModule -> {
                    IntegrateSheetModuleGitParam moduleGitParam = new IntegrateSheetModuleGitParam();
                    moduleGitParam.setModuleId(continuousIntegrateAreaModule.getModuleId());
                    moduleGitParam.setAutoSwitchToRelease(true);
                    return moduleGitParam;
                }).collect(Collectors.toList());
        ciReleaseBranchService.performGitOperations(innerIntegrationModules, moduleGitParams,
                integrateAreaId, applicationId);
    }

    private void switchBaseDependency(Long integrateAreaId, List<Long> alterSheetIds) {
        alterSheetIds.forEach(alterSheetId -> baseDependencyInnerService.switchBaseDependency(EntityType.ALTER_SHEET,
                alterSheetId, BaseDependencyType.INTEGRATE_AREA, null, integrateAreaId, SYSTEM_MODIFIER));
    }

    @Override
    public void validatePipelineInstanceRunStatus(Long ciAreaId) {
        List<PipelineRelation> pipelineRelations =
                pipelineRelationService.queryPipelineRelations(ciAreaId, EntityType.INTEGRATE_AREA);
        Assert.isFalse(pipelineRelations.isEmpty(),
                "cannot find pipeline related with continuousIntegrateArea:" + ciAreaId);
        PipelineRelation pipelineRelation = pipelineRelations.get(0);

        PipelineInstance latestPipelineInstance =
                pipelineExecuteService.findLatestPipelineInstanceByPipelineId(pipelineRelation.getPipelineId());
        Assert.notNull(latestPipelineInstance,
                "cannot find any pipelineInstance in continuousIntegrateArea:" + ciAreaId);
        if (RunStatus.isFinished(latestPipelineInstance.getPipelineBizStatus()) &&
                !RunStatus.isSuccess(latestPipelineInstance.getPipelineBizStatus())) {
            IntegrateAreaBO ciArea = integrateAreaInnerService.getIntegrateArea(ciAreaId);
            throw new IllegalArgumentException(String.format("%s最新一次流水线实例运行失败", ciArea.getName()));
        }
    }

    private IntegrateSheetBO generateAndSaveIntegrateSheet(IntegrateSheetInnerBO integrateSheet, Long integrateAreaId, String user) {
        IntegrateSheetBO integrateSheetBO = new IntegrateSheetBO();
        BeanUtils.copyProperties(integrateSheet, integrateSheetBO);
        integrateSheetBO.setId(null);
        integrateSheetBO.setCreator(user);
        integrateSheetBO.setIntegrateAreaId(integrateAreaId);
        integrateSheetBO.setContinuousIntegration(false);
        integrateSheetBO.setStatus(IntegrateSheetStatus.INIT);
        integrateSheetBO.setIntegrationTime(null);
        //设置集成单的开发和测试
        setDevelopersAndTester(integrateSheet.getId(), integrateSheetBO);
        if (CollectionUtils.isNotEmpty(integrateSheetBO.getRequirementRelationList())) {
            integrateSheetBO.getRequirementRelationList()
                    .forEach(requirementRelationBO -> requirementRelationBO.setId(null));
        }
        if (CollectionUtils.isNotEmpty(integrateSheetBO.getIntegrateSheetModuleBOList())) {
            integrateSheetBO.getIntegrateSheetModuleBOList()
                    .forEach(integrateSheetModuleBO -> {
                            integrateSheetModuleBO.setId(null);
                            integrateSheetModuleBO.setIntegrateAreaModuleId(null);
                    });
        }

        Long integrateSheetId = integrateSheetInnerService.autoSaveIntegrateSheet(integrateSheetBO);
        integrateSheetBO.setId(integrateSheetId);
        return integrateSheetBO;
    }

    private void autoSubmitIntegrateSheets(List<IntegrateSheetBO> autoSavedIntegrateSheets) {
        autoSavedIntegrateSheets.forEach(integrateSheetBO ->
                integrateSheetInnerService.autoSubmitIntegrateSheet(integrateSheetBO));
    }

    private void batchSaveIntegrationSheetAreaRelation(Map<Long, Long> integrateSheetIdMap,
                                                       Long integrateAreaId, Long ciAreaId,
                                                       String user) {
        List<IntegrationSheetAreaRelationBO> integrationSheetRelations = new ArrayList<>();
        for (Map.Entry<Long, Long> entry : integrateSheetIdMap.entrySet()) {
            IntegrationSheetAreaRelationBO integrationSheetRelation = generateIntegrationSheetAreaRelation(
                    integrateAreaId, ciAreaId, entry.getValue(), entry.getKey(), user);
            integrationSheetRelations.add(integrationSheetRelation);
        }
        areaRelationService.batchSaveIntegrationSheetAreaRelation(integrationSheetRelations);
    }

    private Map<Long, IntegrateVersionCheckResult> checkIntegrateVersion(Long integrateAreaId,
                                                                         List<IntegrateAreaModuleBO> integrateAreaModules) {
        Map<Long, IntegrateVersionCheckResult> checkResultMap = new HashMap<>(256);
        Map<String, DepVersionProp> integrateAreaDepDetail = baseDependencyInnerService.findEntityAllDepsDetail(
                EntityType.INTEGRATE_AREA, integrateAreaId);
        Map<Long, DepVersionProp> moduleDepDetailMap = new HashMap<>(256);
        if (!integrateAreaDepDetail.isEmpty()) {
            moduleDepDetailMap = integrateAreaDepDetail.values()
                    .stream()
                    .collect(Collectors.toMap(DepVersionProp::getApplicationId,
                            DepVersionProp -> DepVersionProp,
                            (firstDepVersionProp, secondDepVersionProp) -> firstDepVersionProp));
        }

        for (IntegrateAreaModuleBO areaModule : integrateAreaModules) {
            IntegrateVersionCheckResult checkResult = new IntegrateVersionCheckResult();
            checkResultMap.put(areaModule.getAlterModuleId(), checkResult);
            DepVersionProp moduleDepDetail = moduleDepDetailMap.get(areaModule.getModuleId());
            if (moduleDepDetail == null) {
                continue;
            }

            BizResult<Integer> versionCompareResult = versionService.compareVersion(areaModule.getIntegrateVersion(),
                    moduleDepDetail.getDepVersion());
            if (versionCompareResult != null) {
                checkResult.setDepVersion(moduleDepDetail.getDepVersion());
                checkResult.setModuleId(areaModule.getModuleId());
                checkResult.setIntegrateAreaId(integrateAreaId);
                checkResult.setIntegrateVersion(areaModule.getIntegrateVersion());
                if (versionCompareResult.isSuccess()) {
                    //集成版本小于集成区版本
                    if (versionCompareResult.getData() < 0) {
                        checkResult.setComparationResult(VersionComparationResult.LOWER);
                        checkResult.setMessage("所选版本低于集成区的版本:" + checkResult.getDepVersion());
                    } else if (versionCompareResult.getData() > 0) {
                        checkResult.setComparationResult(VersionComparationResult.HIGHER);
                        checkResult.setMessage("所选版本高于集成区的版本:" + checkResult.getDepVersion());
                    } else {
                        checkResult.setComparationResult(VersionComparationResult.EQUAL);
                        checkResult.setMessage("所选版本等于集成区的版本:" + checkResult.getDepVersion());
                    }
                } else {
                    checkResult.setComparationResult(VersionComparationResult.UNABLE_TO_COMPARE);
                    checkResult.setMessage("字符串版本，无法和集成区的版本:" + checkResult.getDepVersion() + "比较高低");
                }
            }
        }
        return checkResultMap;
    }

    private void fillUpVersionCheckResult(Long integrateAreaId, Map<Long, IntegrateVersionCheckResult> versionCheckMap) {
        versionCheckMap.forEach((crModuleId, versionCheckResult) -> {
            if (VersionComparationResult.LOWER.equals(versionCheckResult.getComparationResult())) {
                //查找集成区模块
                IntegrateAreaModuleBO integrateAreaModuleBO = areaModuleInnerService.findIntegrateAreaModule(
                        integrateAreaId, versionCheckResult.getModuleId());
                if (integrateAreaModuleBO != null) {
                    versionCheckResult.setIntegrateAreaModuleVersion(integrateAreaModuleBO.getIntegrateVersion());
                    versionCheckResult.setOriginalIntegrateSheetId(integrateAreaModuleBO.getIntegrateSheetId());
                    ApplicationBO module = appInnerService.findApplication(integrateAreaModuleBO.getModuleId());
                    versionCheckResult.setModuleName(module.getName());
                }
            }
        });
    }

    private IntegrateAreaModuleBO generateIntegrateAreaModule(IntegrateAreaModuleBO previousAreaModule,
                                                              Long integrateSheetId,
                                                              IntegrateAreaModuleBO ciModule,
                                                              Long integrateAreaId,
                                                              String user) {
        IntegrateAreaModuleBO areaModuleBO;
        if (previousAreaModule == null) {
            areaModuleBO = IntegrateAreaModuleBO.builder()
                    .integrateAreaId(integrateAreaId)
                    .alterModuleId(ciModule.getAlterModuleId())
                    .integrateCount(1)
                    .integrateMode(ciModule.getIntegrateMode())
                    .integrateSheetId(integrateSheetId)
                    .integrateVersion(ciModule.getIntegrateVersion())
                    .moduleId(ciModule.getModuleId())
                    .mainModuleId(ciModule.getMainModuleId())
                    .alterMode(ciModule.getAlterMode())
                    .creator(user)
                    .modifier(ciModule.getModifier())
                    .isDeleted(false)
                    .gmtCreate(new Date())
                    .gmtModified(new Date())
                    .build();
        } else {
            previousAreaModule.setModifier(ciModule.getModifier());
            previousAreaModule.setGmtModified(new Date());
            previousAreaModule.setIntegrateCount(previousAreaModule.getIntegrateCount() + 1);
            previousAreaModule.setIntegrateSheetId(integrateSheetId);
            previousAreaModule.setAlterModuleId(ciModule.getAlterModuleId());
            previousAreaModule.setMainModuleId(ciModule.getMainModuleId());
            previousAreaModule.setIntegrateVersion(ciModule.getIntegrateVersion());
            previousAreaModule.setAlterMode(ciModule.getAlterMode());
            areaModuleBO = new IntegrateAreaModuleBO();
            BeanUtils.copyProperties(previousAreaModule, areaModuleBO);
        }
        return areaModuleBO;
    }

    @Override
    public boolean validateBaseDependency(Long ciAreaId, Long areaId) {
        BaseDependencyBO ciAreaBaseDependency = baseDependencyInnerService.getBaseDependency(EntityType.INTEGRATE_AREA,
                ciAreaId);
        if (ciAreaBaseDependency == null) {
            log.error("cannot get baseDependency for continuousIntegrateArea with id:" + ciAreaId);
            return false;
        }

        BaseDependencyBO baseDependency = baseDependencyInnerService.getBaseDependency(EntityType.INTEGRATE_AREA, areaId);
        if (baseDependency == null) {
            log.error("cannot get baseDependency for integrateArea with id:" + areaId);
            return false;
        }

        if (BaseDependencyType.RELEASE_PRODUCT.equals(ciAreaBaseDependency.getType()) &&
                !ciAreaBaseDependency.getDependencyId().equals(baseDependency.getDependencyId())) {
            log.error("continuousIntegrateArea:" + ciAreaId + " and integrateArea:" + areaId +
                    " don't have the same base dependency");
            return false;
        }
        if (BaseDependencyType.INTEGRATE_AREA.equals(ciAreaBaseDependency.getType()) &&
                !ciAreaBaseDependency.getDependencyIntegrateId().equals(baseDependency.getDependencyIntegrateId())) {
            log.error("continuousIntegrateArea:" + ciAreaId + " and integrateArea:" + areaId +
                    " don't have the same base dependency");
            return false;
        }
        return true;
    }

    @Override
    public void validateReleaseBranchCodeIncluded(Long appId, Long ciAreaId) {
        List<IntegrateAreaModuleBO> ciAreaModules = areaModuleInnerService.getAllIntegrateAreaModules(ciAreaId);
        if (ciAreaModules.isEmpty()) {
            return;
        }

        //查询最新的版本集成区
        VersionPlanBO versionPlanBO = versionPlanService.queryLatestVersionPlanWithIntegrateArea(appId);
        if (versionPlanBO == null) {
            return;
        }
        ciAreaModules.forEach(ciAreaModuleBO -> validateReleaseBranchCodeIncluded(versionPlanBO.getIntegrateAreaId(),
                ciAreaId, ciAreaModuleBO, SYSTEM_MODIFIER));
    }

    @Override
    public void validateReleaseBranchCodeIncluded(Long appId, Long ciAreaId, Long areaModuleId, String user) {
        IntegrateAreaModuleBO ciAreaModuleBO = areaModuleInnerService.getIntegrateAreaModuleBO(areaModuleId);
        if (ciAreaModuleBO == null) {
            log.error("cannot find integrateAreaModule with id {}", areaModuleId);
            return;
        }

        //查询最新的版本集成区
        VersionPlanBO versionPlanBO = versionPlanService.queryLatestVersionPlanWithIntegrateArea(appId);
        if (versionPlanBO == null) {
            return;
        }

        log.error("appId {} ciAreaId {} areaModuleId {}", appId, ciAreaId, areaModuleId);
        validateReleaseBranchCodeIncluded(versionPlanBO.getIntegrateAreaId(), ciAreaId, ciAreaModuleBO, user);
    }

    @Override
    public List<ModuleGitOperationBO> getReleaseBranchCodeIncludedResult(Long appId, Long ciAreaId) {
        VersionPlanBO versionPlanBO = versionPlanService.queryLatestVersionPlanWithIntegrateArea(appId);
        if (versionPlanBO == null) {
            return Collections.emptyList();
        }

        ModuleGitOperationQuery moduleGitOperationQuery = new ModuleGitOperationQuery();
        moduleGitOperationQuery.setIntegrateAreaId(versionPlanBO.getIntegrateAreaId());
        moduleGitOperationQuery.setOperationType(GitOperationType.VALIDATE_INTEGRATION_INCLUDE_RELEASE.name());
        return moduleGitOperationService.queryModuleGitOperations(moduleGitOperationQuery);
    }

    @Override
    public ModuleGitOperationBO getReleaseBranchCodeIncludedResult(Long appId, Long ciAreaId, Long moduleId) {
        VersionPlanBO versionPlanBO = versionPlanService.queryLatestVersionPlanWithIntegrateArea(appId);
        if (versionPlanBO == null) {
            return null;
        }

        ModuleGitOperationQuery gitOperationQuery = new ModuleGitOperationQuery();
        gitOperationQuery.setModuleId(moduleId);
        gitOperationQuery.setIntegrateAreaId(versionPlanBO.getIntegrateAreaId());
        gitOperationQuery.setOperationType(GitOperationType.VALIDATE_INTEGRATION_INCLUDE_RELEASE.name());
        List<ModuleGitOperationBO> moduleGitOperations = moduleGitOperationService.queryModuleGitOperations(gitOperationQuery);
        if (moduleGitOperations.isEmpty()) {
            return null;
        }
        return moduleGitOperations.get(0);
    }

    @Override
    public void doAfterApplicationPublishArchive(long applicationId, long releaseId) {
        log.info("do after application publish archive.application id:{}, release id:{}", applicationId, releaseId);
        String configurationValue = configurationInnerService.getConfigurationValue(EntityType.APPLICATION,
            applicationId, null, ConfigAttributeName.CONTINUOUS_INTERAGION_AREA_OPEN);
        boolean isCiAreaOpen = Boolean.parseBoolean(configurationValue);
        if (!isCiAreaOpen) {
            log.info("continuous area not open for application:" + applicationId);
            return;
        }

        ReleaseBO release = releaseService.querySimpleReleaseBOById(releaseId);
        if (release == null) {
            log.info("release not exist, id:{}", releaseId);
            return;
        }
        if (!PublishType.NORMAL.equals(release.getPublishType())) {
            log.info("publish type is not NORMAL:{}", PublishType.NORMAL);
            return;
        }
        if (!ReleaseType.NORMAL.equals(release.getReleaseType())) {
            log.info("release type is not NORMAL:{}", ReleaseType.NORMAL);
            return;
        }

        IntegrateAreaBO ciArea = integrateAreaInnerService.getContinuousIntegrateArea(applicationId);
        if (ciArea == null) {
            log.error("cannot find ciArea with applicationId {}", applicationId);
            return;
        }

        String archivedVersion = configurationInnerService.getConfigurationValue(EntityType.APPLICATION,
                release.getApplicationId(), ConfigType.APPLICATION_PUBLISH_CONFIG,
                ConfigAttributeName.CURRENT_ARCHIVE_VERSION);
        // 如果当前发布单版本号 < 现在保存的归档版本号，则不更新持续集成区的基础依赖
        if (StringUtils.isNotBlank(archivedVersion)
                && versionService.compareVersion(release.getVersion(), archivedVersion).getData() < 0) {
            log.error("release id {} version {} is smaller than current archived version {}", releaseId,
                    release.getVersion(), archivedVersion);
            return;
        }

        threadPool.submit(() -> {
            BizResult<Long> switchBaseDependencyResult =
                    ciAreaService.switchBaseDependency(ciArea, release.getVersion(), false, SYSTEM_MODIFIER);
            log.info("applicationId {} releaseId {} auto switch continuous integrate area base dependency result {}",
                applicationId, releaseId, JSON.toJSONString(switchBaseDependencyResult));
        });
    }


    @Override
    public List<String> getAllowedSyncStatus(Long appId) {
        String syncTimeValue = configurationInnerService.getConfigurationValue(EntityType.APPLICATION, appId,
                ConfigType.APPLICATION_CONTINUOUS_INTEGRATE_CONFIG,
                ConfigAttributeName.CONTINUOUS_INTEGRATION_AREA_SYNC_TIME);
        if (StringUtils.isEmpty(syncTimeValue)) {
            syncTimeValue = IntegrateAreaStatus.OPEN.name();
        }
        return Arrays.asList(syncTimeValue.split(","));
    }

    @Override
    public boolean willSync(Long appId, Long areaId) {
        IntegrateAreaBO area = integrateAreaInnerService.getIntegrateArea(areaId);

        IntegrateAreaBO ciArea = integrateAreaInnerService.getContinuousIntegrateArea(appId);
        List<String> allowedSyncStatus = getAllowedSyncStatus(appId);

        List<IntegrationSheetAreaRelationBO> relations = areaRelationService.findRelations(ciArea.getId(), area.getId());
        //已经同步过且当前不支持同步
        return CollectionUtils.isEmpty(relations) || allowedSyncStatus.contains(area.getStatus().name());
    }

    @Override
    public CiSyncResult getCiSyncResult(Long ciAreaId, Long ciSheetId) {
        IntegrateSheetInnerBO integrateSheetInnerBO = integrateSheetInnerService.findSimpleIntegrateSheetById(ciSheetId);
        Assert.notNull(integrateSheetInnerBO.getVersionPlanId(), "找不到集成单关联的版本计划");

        CiSyncResult ciAreaSyncResult = new CiSyncResult();
        ciAreaSyncResult.setNeedSync(true);

        if (!integrateSheetInnerBO.fetchIsContinuousIntegration()) {
            ciAreaSyncResult.setNeedSync(false);
            ciAreaSyncResult.setDescription("非持续集成单无需同步");
            return ciAreaSyncResult;
        }

        //版本计划可能被删除了
        VersionPlanBO versionPlanBO = versionPlanService.getVersionPlanById(integrateSheetInnerBO.getVersionPlanId());
        if (versionPlanBO == null) {
            ciAreaSyncResult.setNeedSync(false);
            ciAreaSyncResult.setDescription("当前版本计划不存在，请重新选择版本计划创建迭代");
            return ciAreaSyncResult;
        }

        IntegrateAreaBO area = versionPlanBO.getIntegrateArea();
        //版本集成区还不存在
        if (area == null) {
            ciAreaSyncResult.setWaitSync(true);
            ciAreaSyncResult.setDescription("版本计划对应的版本集成区还未创建");
            return ciAreaSyncResult;
        }

        Long areaId = area.getId();
        //已经同步过了
        List<IntegrationSheetAreaRelationBO> relations = areaRelationService.findRelations(ciAreaId, areaId, ciSheetId);
        if (CollectionUtils.isNotEmpty(relations)) {
            ciAreaSyncResult.setSynced(true);
            ciAreaSyncResult.setIntegrateSheetId(relations.get(0).getIntegrateSheetId());
            return ciAreaSyncResult;
        }

        //无需同步
        List<IntegrateAreaModuleBO> ciModules = areaModuleInnerService.getAllIntegrateAreaModules(ciAreaId);
        List<Long> integrateSheetIdList = ciModules.stream()
                .map(IntegrateAreaModuleBO::getIntegrateSheetId)
                .distinct()
                .collect(Collectors.toList());
        if (!integrateSheetIdList.contains(ciSheetId)) {
            ciAreaSyncResult.setNeedSync(false);
            ciAreaSyncResult.setDescription("当前集成单集成的版本已经被其他集成单集成的版本覆盖或者已被移除，无需同步");
            return ciAreaSyncResult;
        }

        //是否同步失败
        if (!IntegrateAreaStatus.OPEN.equals(area.getStatus())) {
            List<String> allowedSyncStatus = getAllowedSyncStatus(area.getIntegrateApplicationId());
            if (!isLimitedVersionPlanFreezed(area, versionPlanBO) && !allowedSyncStatus.contains(area.getStatus().name())) {
                if (needSendCISyncCancelledMessage(area)) {
                    ciAreaSyncResult.setSyncFailed(true);
                    ciAreaSyncResult.setDescription(String.format("版本集成区[%s]的状态为%s，不支持同步。请在本页面重新手动集成",
                            area.getName(), area.getStatus().getDescription()));
                    return ciAreaSyncResult;
                }
            }
            NormalIntegrationCheckResult checkResult = integrateSheetInnerService.checkIntegrateAreaStatusWhenIntegration(
                    integrateSheetInnerBO, area, area.getCreator());
            if (checkResult != null && !checkResult.isCanNormalIntegration()) {
                ciAreaSyncResult.setSyncFailed(true);
                ciAreaSyncResult.setDescription(String.format("版本集成区[%s]%s，无法自动同步提交到，请在本页面重新手动集成",
                        area.getName(), checkResult.getDescription().replace("，需要紧急集成", "")));
            }
        }

        //是否等待同步
        ciAreaSyncResult.setWaitSync(true);
        ciAreaSyncResult.setDescription("等待平台自动同步");
        return ciAreaSyncResult;
    }

    private void setDevelopersAndTester(Long integrateSheetId, IntegrateSheetBO integrateSheetBO) {
        List<UserRole> userRoles = userRoleService.findUserRole(EntityType.INTEGRATE_SHEET, integrateSheetId);
        userRoles.forEach(userRole -> {
            if (com.alibaba.emas.mtl4.services.dev.api.enums.Role.DEVELOPER.equals(userRole.getRole())) {
                integrateSheetBO.setDevelopers(userRole.getUserList());
            }
            if (com.alibaba.emas.mtl4.services.dev.api.enums.Role.TESTER.equals(userRole.getRole())) {
                integrateSheetBO.setTesters(userRole.getUserList());
            }
        });
    }


    private CiModuleRelationBO generateCiModuleRelation(Long integrateAreaId, Long continuousIntegrateAreaId,
                                                        Long continuousAreaModuleId, Long areaModuleId,
                                                        Long moduleId, String version, String user) {
        CiModuleRelationBO moduleRelation = new CiModuleRelationBO();
        moduleRelation.setCiModuleId(continuousAreaModuleId);
        moduleRelation.setAreaModuleId(areaModuleId);
        moduleRelation.setModuleId(moduleId);
        moduleRelation.setVersion(version);
        moduleRelation.setIntegrateAreaId(integrateAreaId);
        moduleRelation.setCiAreaId(continuousIntegrateAreaId);
        moduleRelation.setCreator(user);
        moduleRelation.setModifier(user);
        return moduleRelation;
    }


    private IntegrationSheetAreaRelationBO generateIntegrationSheetAreaRelation(Long integrateAreaId,
                                                                                Long continuousIntegrateAreaId,
                                                                                Long integrateSheetId,
                                                                                Long continuousIntegrateSheetId,
                                                                                String user) {
        IntegrationSheetAreaRelationBO relationBO = new IntegrationSheetAreaRelationBO();
        relationBO.setIntegrateAreaId(integrateAreaId);
        relationBO.setContinuousIntegrateAreaId(continuousIntegrateAreaId);
        relationBO.setIntegrateSheetId(integrateSheetId);
        relationBO.setContinuousIntegrateSheetId(continuousIntegrateSheetId);
        relationBO.setCreator(user);
        relationBO.setModifier(user);

        return relationBO;
    }

    private void archiveDependencySnapshot(Long applicationId, Long continuousIntegrateAreaId) {
        DependencySnapshotBO dependencySnapshotBO = new DependencySnapshotBO();
        dependencySnapshotBO.setApplicationId(applicationId);
        dependencySnapshotBO.setCreator(SYSTEM_MODIFIER);
        dependencySnapshotBO.setModifier(SYSTEM_MODIFIER);
        dependencySnapshotBO.setEntityId(continuousIntegrateAreaId);
        dependencySnapshotBO.setEntityType(EntityType.INTEGRATE_AREA);
        dependencySnapshotBO.setType(DependencySnapshotType.AUTO_SUBMIT_INTEGRATE_SHEET);
        dependencySnapshotService.archiveDependencySnapshot(dependencySnapshotBO);
    }

    private boolean needSendCISyncCancelledMessage(IntegrateAreaBO area) {
        if (IntegrateAreaStatus.FREEZED.equals(area.getStatus())) {
            //受限版本
            VersionPlanBO versionPlanBO = versionPlanService.getSimpleVersionPlanById(area.getVersionPlanId());
            if (versionPlanBO.getIsLimited() != null && versionPlanBO.getIsLimited()) {
                return true;
            }
            if (integrateAreaStatusManager.notAlwaysFrozen(area.getId())) {
                return true;
            }
        }
        if (IntegrateAreaStatus.NOT_ALLOW_NEW_MODULE_ENTER.equals(area.getStatus())) {
            return true;
        }
        if (IntegrateAreaStatus.WHITELIST.equals(area.getStatus())) {
            return true;
        }
        return IntegrateAreaStatus.REQUIREMENT_CONTROL.equals(area.getStatus());
    }


    private void validateReleaseBranchCodeIncluded(Long integrateAreaId, Long ciAreaId,
                                                   IntegrateAreaModuleBO ciAreaModule, String user) {
        List<IntegrateAreaModuleBO> existedAreaModules =
                areaModuleInnerService.findIntegrateAreaModules(integrateAreaId, ciAreaModule.getModuleId());
        if (existedAreaModules.isEmpty()) {
            return;
        }

        ApplicationBO module = appInnerService.findApplication(ciAreaModule.getModuleId());
        //校验release分支是否存在
        String releaseBranch = String.format(GitOperationConstant.RELEASE_BRANCH, integrateAreaId);
        GitBranchInfo gitBranchInfo = codeService.getGitBranch(module.getCodeLibraryAddress(), releaseBranch);
        if (gitBranchInfo == null) {
            log.error("module {} has no release branch in integrateArea {}", ciAreaModule.getModuleId(), integrateAreaId);
            return;
        }
        //校验release分支的代码是否参与过集成
        List<Long> areaModuleIds = existedAreaModules.stream()
                .map(IntegrateAreaModuleBO::getId)
                .collect(Collectors.toList());
        List<IntegrateSheetModuleInnerBO> integrateSheetModules =
                integrationModuleInnerService.findIntegrateSheetModules(areaModuleIds);
        List<String> integrateVersions = integrateSheetModules.stream()
                .map(IntegrateSheetModuleInnerBO::getIntegrateVersion)
                .collect(Collectors.toList());
        if (integrateVersions.isEmpty()) {
            log.error("cannot find integrateSheetModules with integrateAreaModuleIds {}",
                    JSON.toJSONString(areaModuleIds));
            return;
        }

        List<ModuleDeployRecordBO> deployRecords = deployRecordService.findAll(ciAreaModule.getModuleId(),
                integrateVersions, ModuleDeployStatus.SUCCESS);
        List<ModuleDeployRecordBO> releaseBranchDeployRecords = deployRecords.stream()
                .filter(moduleDeployRecordBO -> releaseBranch.equals(moduleDeployRecordBO.getScmBranch()))
                .collect(Collectors.toList());
        if (releaseBranchDeployRecords.isEmpty()) {
            return;
        }

        //查找release分支对应的最新参与集成的版本
        ModuleDeployRecordBO releaseBranchDeployRecord = null;
        for (String integrateVersion : integrateVersions) {
            for (ModuleDeployRecordBO moduleDeployRecordBO : releaseBranchDeployRecords) {
                if (integrateVersion.equals(moduleDeployRecordBO.getVersion())) {
                    releaseBranchDeployRecord = moduleDeployRecordBO;
                    break;
                }
            }
            if (releaseBranchDeployRecord != null) {
                break;
            }
        }
        if (releaseBranchDeployRecord == null) {
            log.error("cannot find latest release branch deploy record with integrateAreaModuleIds {}",
                    JSON.toJSONString(areaModuleIds));
            return;
        }

        List<ModuleDeployRecordBO> ciBranchDeployRecords = deployRecordService.findAll(ciAreaModule.getModuleId(),
                ciAreaModule.getIntegrateVersion(), ModuleDeployStatus.SUCCESS);
        if (ciBranchDeployRecords.isEmpty()) {
            return;
        }
        ModuleDeployRecordBO ciBranchDeployRecord = ciBranchDeployRecords.get(0);

        ModuleGitOperationBO moduleGitOperationBO = getModuleGitOperation(ciAreaModule.getId(), integrateAreaId,
                ciAreaModule.getModuleId());

        //判断release分支是否包含集成分支的代码
        GitTag releaseBranchTag = codeService.getGitTag(module.getCodeLibraryAddress(),
                releaseBranchDeployRecord.getTag(),
                releaseBranchDeployRecord.getCreator());
        //code平台tag与平台记录的release branch commit匹配时 才校验release分支是否包含集成分支的代码
        if (releaseBranchTag == null || releaseBranchTag.getCommit() == null ||
                !releaseBranchTag.getCommit().getId().equals(releaseBranchDeployRecord.getCommitNumber())) {
            return;
        }

        boolean releaseBranchContainsCiBranchCode = scmRepoService.isCommitInBranch(module.getCodeLibraryAddress(),
                    releaseBranchDeployRecord.getTag(), ciBranchDeployRecord.getCommitNumber());
        if (releaseBranchContainsCiBranchCode) {
            moduleGitOperationBO.setOperationDetail("releaseBranch " + releaseBranch + " contains commit " +
                    ciBranchDeployRecord.getCommitNumber() + " from integrationBranch " +
                    ciBranchDeployRecord.getScmBranch());
        } else {
            String operationDetail = "releaseBranch " + releaseBranch + " not contains commit " +
                    ciBranchDeployRecord.getCommitNumber() + " from integrationBranch " +
                    ciBranchDeployRecord.getScmBranch() + "\n";
            //code平台tag与平台记录的ci branch commit匹配时 才校验集成分支是否包含release分支的代码
            GitTag ciBranchTag = codeService.getGitTag(module.getCodeLibraryAddress(),
                    ciBranchDeployRecord.getTag(),
                    ciBranchDeployRecord.getCreator());
            if (ciBranchTag != null && ciBranchTag.getCommit() != null &&
                    ciBranchTag.getCommit().getId().equals(ciBranchDeployRecord.getCommitNumber())) {
                boolean ciBranchContainsReleaseBranchCode = scmRepoService.isCommitInBranch(module.getCodeLibraryAddress(),
                        ciBranchDeployRecord.getTag(), releaseBranchDeployRecord.getCommitNumber());
                if (ciBranchContainsReleaseBranchCode) {
                    operationDetail += "integrationBranch " + ciBranchDeployRecord.getScmBranch() +
                            " contains commit " + releaseBranchDeployRecord.getCommitNumber() +
                            " from releaseBranch " + releaseBranch;
                } else {
                    //不包含 需要记录结果
                    operationDetail += "integrationBranch tag " + ciBranchDeployRecord.getTag() +
                            " not contains commit " +  releaseBranchDeployRecord.getCommitNumber() +
                            " from releaseBranch " + releaseBranch;
                    moduleGitOperationBO.setOperationResult(GitOperationResult.FAILED);
//                        if (moduleGitOperationBO.getId() == null) {
//                            messageService.sendReleaseBranchCodeNotIncludedMessage(ciAreaId, module, ciAreaModule, releaseBranch);
//                        }
                }
            }
            moduleGitOperationBO.setOperationDetail(operationDetail);
        }
        moduleGitOperationService.saveOrUpdateModuleGitOperation(moduleGitOperationBO, user);
    }

    private ModuleGitOperationBO getModuleGitOperation(Long areaModuleId, Long integrateAreaId, Long moduleId) {
        ModuleGitOperationBO moduleGitOperationBO;
        ModuleGitOperationQuery gitOperationQuery = new ModuleGitOperationQuery();
        gitOperationQuery.setEntityType(EntityType.INTEGRATE_AREA_MODULE);
        gitOperationQuery.setEntityId(areaModuleId);
        gitOperationQuery.setIntegrateAreaId(integrateAreaId);
        gitOperationQuery.setOperationType(GitOperationType.VALIDATE_INTEGRATION_INCLUDE_RELEASE.name());
        List<ModuleGitOperationBO> moduleGitOperations = moduleGitOperationService.queryModuleGitOperations(gitOperationQuery);
        if (!moduleGitOperations.isEmpty()) {
            moduleGitOperationBO = moduleGitOperations.get(0);
            moduleGitOperationBO.setOperationResult(GitOperationResult.SUCCESS);
        } else {
            moduleGitOperationBO = new ModuleGitOperationBO();
            moduleGitOperationBO.setEntityType(EntityType.INTEGRATE_AREA_MODULE);
            moduleGitOperationBO.setEntityId(areaModuleId);
            moduleGitOperationBO.setIntegrateAreaId(integrateAreaId);
            moduleGitOperationBO.setModuleId(moduleId);
            moduleGitOperationBO.setOperationType(GitOperationType.VALIDATE_INTEGRATION_INCLUDE_RELEASE);
            moduleGitOperationBO.setOperationResult(GitOperationResult.SUCCESS);
        }
        return moduleGitOperationBO;
    }


}
