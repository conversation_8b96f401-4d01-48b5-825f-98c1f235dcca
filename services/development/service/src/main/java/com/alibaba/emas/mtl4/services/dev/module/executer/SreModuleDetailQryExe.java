package com.alibaba.emas.mtl4.services.dev.module.executer;

import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.commons.utils.TimeUtil;
import com.alibaba.emas.mtl4.services.dev.api.model.ApplicationBO;
import com.alibaba.emas.mtl4.services.dev.api.service.AppService;
import com.alibaba.emas.mtl4.services.dev.dashboard.enums.AppMetricViewType;
import com.alibaba.emas.mtl4.services.dev.dashboard.measurement.AppMetricMeasurementQuery;
import com.alibaba.emas.mtl4.services.dev.dashboard.service.AppMetricMeasurementService;
import com.alibaba.emas.mtl4.services.dev.module.command.SreModuleDetailQry;
import com.alibaba.emas.mtl4.services.dev.module.converter.SreModuleConverter;
import com.alibaba.emas.mtl4.services.dev.module.dto.SreModuleDetailDTO;
import com.alibaba.emas.mtl4.services.dev.module.model.ModuleMetric;
import com.alibaba.emas.mtl4.services.dev.module.validator.SreModuleDetailQryValidator;
import com.alibaba.fastjson.JSON;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;

import static com.alibaba.emas.mtl4.services.dev.dashboard.constant.DashboardConstant.MODULE_BASIC_METRIC;

@Slf4j
@Component
public class SreModuleDetailQryExe {
    @Setter(onMethod_ = {@Autowired})
    private SreModuleDetailQryValidator validator;
    @Setter(onMethod_ = {@Autowired})
    private AppService appService;
    @Setter(onMethod_ = {@Autowired})
    private AppMetricMeasurementService appMetricMeasurementService;
    @Setter(onMethod_ = {@Autowired})
    private SreModuleConverter converter;


    public BizResult<SreModuleDetailDTO> execute(SreModuleDetailQry query) {
        validator.validate(query);

        ApplicationBO application = appService.findApplicationById(query.getModuleId()).getData();
        AppMetricMeasurementQuery metricQuery = AppMetricMeasurementQuery.builder()
                .applicationIds(Collections.singletonList(query.getModuleId()))
                .viewType(AppMetricViewType.MODULE_BASIC.getName())
                .mainDimValue(TimeUtil.getDayTimeStr(new Date()))
                .metricName(MODULE_BASIC_METRIC)
                .build();
        ModuleMetric moduleMetric = appMetricMeasurementService.queryAppMetricMeasurements(metricQuery)
                .stream()
                .findFirst()
                .map(measurementBO -> JSON.parseObject(measurementBO.getExtras(), ModuleMetric.class))
                .orElse(new ModuleMetric(query.getModuleId()));

        return BizResult.success(converter.toDetail(application, moduleMetric));
    }
}
