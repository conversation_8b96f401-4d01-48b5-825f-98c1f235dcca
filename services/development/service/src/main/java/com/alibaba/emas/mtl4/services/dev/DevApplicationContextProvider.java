package com.alibaba.emas.mtl4.services.dev;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/28
 */
@Component
public class DevApplicationContextProvider implements ApplicationContextAware, DisposableBean {

    protected static ApplicationContext applicationContext;


    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        applicationContext = context;
    }

    @Override
    public void destroy() throws Exception {
        applicationContext = null;
    }

    /**
     * 根据class获取spring容器中的bean
     */
    public static <T> T getBean(Class<T> c){
        return applicationContext.getBean(c);
    }

    public static <T> T getBean(String beanName){
        return (T)applicationContext.getBean(beanName);
    }

    public static Map<String, Object> getBeansWithAnnotation(Class<? extends Annotation> annotationType){
        return applicationContext.getBeansWithAnnotation(annotationType);
    }




}
