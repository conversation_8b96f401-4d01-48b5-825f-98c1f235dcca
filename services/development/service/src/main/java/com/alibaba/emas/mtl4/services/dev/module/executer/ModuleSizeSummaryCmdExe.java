package com.alibaba.emas.mtl4.services.dev.module.executer;

import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.services.dev.collaboration.model.CollaborationSpaceModuleSize;
import com.alibaba.emas.mtl4.services.dev.module.ModuleSizeService;
import com.alibaba.emas.mtl4.services.dev.module.command.ModuleSizeSummaryCmd;
import com.alibaba.emas.mtl4.services.dev.module.converter.ModuleSizeBalanceConverter;
import com.alibaba.emas.mtl4.services.dev.module.dto.ModuleSizeSummaryResultDTO;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ModuleSizeSummaryCmdExe {
    @Setter(onMethod_ = {@Autowired})
    private ModuleSizeService moduleSizeService;
    @Setter(onMethod_ = {@Autowired})
    private ModuleSizeBalanceConverter converter;
    @Setter(onMethod_ = {@Autowired})
    private RedissonClient redissonClient;

    public BizResult<ModuleSizeSummaryResultDTO> execute(ModuleSizeSummaryCmd cmd) {
        validate(cmd);

        Optional<CollaborationSpaceModuleSize> optional = moduleSizeService.getSpaceReleaseModuleSize(cmd.getSpaceId(), cmd.getAppId(), cmd.getAppVersion());

        return optional.map(collaborationSpaceModuleSize -> BizResult.success(convertWithCache(collaborationSpaceModuleSize)))
                .orElseGet(() -> BizResult.success(null));

    }

    private ModuleSizeSummaryResultDTO convertWithCache(CollaborationSpaceModuleSize collaborationSpaceModuleSize) {
        RBucket<String> bucket = redissonClient.getBucket(buildModuleKey(collaborationSpaceModuleSize));
        if (bucket.isExists()) {
            return new Gson().fromJson(bucket.get(), new TypeToken<ModuleSizeSummaryResultDTO>() {
            }.getType());
        }

        ModuleSizeSummaryResultDTO result = converter.toSummaryResultDTO(collaborationSpaceModuleSize);

        bucket.set(new Gson().toJson(result), 1, TimeUnit.DAYS);
        return result;
    }

    private String buildModuleKey(CollaborationSpaceModuleSize collaborationSpaceModuleSize) {
        return "module_size_summary:" + collaborationSpaceModuleSize.getSpaceId() + ":" + collaborationSpaceModuleSize.getAppId() + ":" + collaborationSpaceModuleSize.getAppVersion();
    }

    private void validate(ModuleSizeSummaryCmd cmd) {
        Assert.notNull(cmd, "command 不能为空");
        Assert.notNull(cmd.getOperator(), "operator 不能为空");
        Assert.notNull(cmd.getSpaceId(), "spaceId 不能为空");
        Assert.notNull(cmd.getAppId(), "appId 不能为空");
        Assert.notNull(cmd.getAppVersion(), "appVersion 不能为空");
    }
}
