package com.alibaba.emas.mtl4.services.dev.metric;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import com.alibaba.emas.mtl4.commons.utils.ConversionUtils;
import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.api.enums.IntegrateAreaType;
import com.alibaba.emas.mtl4.services.dev.api.enums.gate.ApprovalStatus;
import com.alibaba.emas.mtl4.services.dev.api.enums.gate.GateCheckResult;
import com.alibaba.emas.mtl4.services.dev.api.model.CollaborationSpaceBO;
import com.alibaba.emas.mtl4.services.dev.api.model.IntegrateAreaBO;
import com.alibaba.emas.mtl4.services.dev.api.model.IntegrateSheetBO;
import com.alibaba.emas.mtl4.services.dev.api.model.gate.GateCheckSummary;
import com.alibaba.emas.mtl4.services.dev.api.service.IntegrateAreaService;
import com.alibaba.emas.mtl4.services.dev.api.service.IntegrateSheetService;
import com.alibaba.emas.mtl4.services.dev.collaboration.CollaborationSpaceCodeService;
import com.alibaba.emas.mtl4.services.dev.collaboration.CollaborationSpaceService;
import com.alibaba.emas.mtl4.services.dev.common.EntitySourcePlatformService;
import com.alibaba.emas.mtl4.services.dev.common.platform.EntitySourcePlatform;
import com.alibaba.emas.mtl4.services.dev.common.platform.SourcePlatformEnum;
import com.alibaba.emas.mtl4.services.dev.gate.domain.GateApproval;
import com.alibaba.emas.mtl4.services.dev.gate.repository.GateApprovalRepository;
import com.alibaba.emas.mtl4.services.dev.gate.service.GateCheckService;
import com.alibaba.emas.mtl4.services.dev.integrate.IntegrationSheetAreaRelationService;
import com.alibaba.emas.mtl4.services.dev.integrate.model.IntegrateSheetInnerBO;
import com.alibaba.emas.mtl4.services.dev.integrate.model.IntegrationSheetAreaRelationBO;
import com.alibaba.emas.mtl4.services.dev.integrate.query.IntegrateSheetQuery;
import com.alibaba.emas.mtl4.services.dev.integrate.service.IntegrateAreaInnerService;
import com.alibaba.emas.mtl4.services.dev.integrate.service.IntegrateAreaMetricService;
import com.alibaba.emas.mtl4.services.dev.integrate.service.IntegrateSheetInnerService;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineExecuteService;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineRelationService;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineRelation;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.pipeline.PipelineInstance;
import com.alibaba.fastjson.JSON;
import com.alibaba.motu.utils.DateUtils;

import com.google.common.base.Preconditions;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.alibaba.motu.utils.DateUtils.COMPACT_MONTH_PATTERN;

/**
 * 模块包大小 卡口指标计算
 * 卡口通过率
 *
 * 8月26号开始的包大小余额机制全量
 */
@Slf4j
@Component
public class ModuleSizeGateCheckMetricCalculator {

    @Autowired
    private IntegrateAreaService integrateAreaService;

    @Autowired
    private EntitySourcePlatformService entitySourcePlatformService;

    @Autowired
    private IntegrateAreaMetricService integrateAreaMetricService;

    @Autowired
    private IntegrateSheetInnerService integrateSheetInnerService;

    @Autowired
    private PipelineRelationService pipelineRelationService;

    @Autowired
    private PipelineExecuteService pipelineExecuteService;

    @Autowired
    private GateCheckService gateCheckService;

    @Autowired
    private GateApprovalRepository gateApprovalRepository;

    @Autowired
    private IntegrationSheetAreaRelationService integrationSheetAreaRelationService;

    @Autowired
    private CollaborationSpaceService collaborationSpaceService;

    //public void process(Long appId) {
    //    new Thread(() -> {
    //        integrateAreaInnerService.getIntegrateAreasWithinTimeRange(appId, new Date(1711900800000L), new Date())
    //            .stream()
    //            .filter(integrateAreaBO -> IntegrateAreaType.NORMAL.equals(integrateAreaBO.getType()))
    //            .filter(integrateAreaBO -> null != integrateAreaBO.getVersionPlanId())
    //            .forEach(integrateAreaBO -> {
    //                log.info(">>>process {}, {}", integrateAreaBO.getName(), JSON.toJSONString(processIntegrateArea
    //                (integrateAreaBO.getId())));
    //            });
    //    }).start();
    //}

    public void processMonth(String month, boolean isAi) {
        new Thread(() -> {
            Date date = DateUtils.parseDate(month, new String[] {COMPACT_MONTH_PATTERN});
            Date startTime = DateUtils.getMonthStart(date);
            Date endTime = DateUtils.getOneDateLastTime(DateUtils.getMonthLastDay(date));

            ModuleSizeGateCheckMetric moduleSizeGateCheckMetric_1 = new ModuleSizeGateCheckMetric();
            ModuleSizeGateCheckMetric moduleSizeGateCheckMetric_6 = new ModuleSizeGateCheckMetric();
            ModuleSizeGateCheckMetric moduleSizeGateCheckMetric = new ModuleSizeGateCheckMetric();

            integrateAreaMetricService.getIntegrateAreasWithinTimeRange(1L, startTime, endTime)
                .stream()
                .filter(integrateAreaBO -> IntegrateAreaType.NORMAL.equals(integrateAreaBO.getType()))
                .filter(integrateAreaBO -> null != integrateAreaBO.getVersionPlanId())
                .forEach(integrateAreaBO -> {
                    log.info(">>>{}", integrateAreaBO.getName());
                    ModuleSizeGateCheckMetric processIntegrateArea = processIntegrateArea(integrateAreaBO.getId(),
                        isAi);

                    moduleSizeGateCheckMetric_1.setIntegrateSheetSize(moduleSizeGateCheckMetric_1.getIntegrateSheetSize() + processIntegrateArea.getIntegrateSheetSize());
                    moduleSizeGateCheckMetric_1.setMcIntegrateSheetSize(moduleSizeGateCheckMetric_1.getMcIntegrateSheetSize() + processIntegrateArea.getMcIntegrateSheetSize());
                    moduleSizeGateCheckMetric_1.getGateApprovals().addAll(processIntegrateArea.getGateApprovals());

                    moduleSizeGateCheckMetric.setIntegrateSheetSize(moduleSizeGateCheckMetric.getIntegrateSheetSize() + processIntegrateArea.getIntegrateSheetSize());
                    moduleSizeGateCheckMetric.setMcIntegrateSheetSize(moduleSizeGateCheckMetric.getMcIntegrateSheetSize() + processIntegrateArea.getMcIntegrateSheetSize());
                    moduleSizeGateCheckMetric.getGateApprovals().addAll(processIntegrateArea.getGateApprovals());
                });

            integrateAreaMetricService.getIntegrateAreasWithinTimeRange(6L, startTime, endTime)
                .stream()
                .filter(integrateAreaBO -> IntegrateAreaType.NORMAL.equals(integrateAreaBO.getType()))
                .filter(integrateAreaBO -> null != integrateAreaBO.getVersionPlanId())
                .forEach(integrateAreaBO -> {
                    log.info(">>>{}", integrateAreaBO.getName());
                    ModuleSizeGateCheckMetric processIntegrateArea = processIntegrateArea(integrateAreaBO.getId(),
                        isAi);

                    moduleSizeGateCheckMetric_6.setIntegrateSheetSize(moduleSizeGateCheckMetric_6.getIntegrateSheetSize() + processIntegrateArea.getIntegrateSheetSize());
                    moduleSizeGateCheckMetric_6.setMcIntegrateSheetSize(moduleSizeGateCheckMetric_6.getMcIntegrateSheetSize() + processIntegrateArea.getMcIntegrateSheetSize());
                    moduleSizeGateCheckMetric_6.getGateApprovals().addAll(processIntegrateArea.getGateApprovals());

                    moduleSizeGateCheckMetric.setIntegrateSheetSize(moduleSizeGateCheckMetric.getIntegrateSheetSize() + processIntegrateArea.getIntegrateSheetSize());
                    moduleSizeGateCheckMetric.setMcIntegrateSheetSize(moduleSizeGateCheckMetric.getMcIntegrateSheetSize() + processIntegrateArea.getMcIntegrateSheetSize());
                    moduleSizeGateCheckMetric.getGateApprovals().addAll(processIntegrateArea.getGateApprovals());
                });

            log.info(">>>1: {}", moduleSizeGateCheckMetric_1.toString(collaborationSpaceService));
            log.info(">>>6: {}", moduleSizeGateCheckMetric_6.toString(collaborationSpaceService));
            log.info(">>>: {}", moduleSizeGateCheckMetric.toString(collaborationSpaceService));

            moduleSizeGateCheckMetric.getGateApprovals()
                .stream()
                .sorted((o1, o2) -> Long.compare(o2.getGmtModified().getTime() - o2.getGmtCreate().getTime(),
                    o1.getGmtModified().getTime() - o1.getGmtCreate().getTime()))
                .forEach(gateApproval -> {
                    log.info(">>>{}, {}", gateApproval.getApprovalUrl(),
                        gateApproval.getGmtModified().getTime() - gateApproval.getGmtCreate().getTime());
                });
        }).start();
    }

    public ModuleSizeGateCheckMetric processIntegrateArea(Long integrateAreaId, boolean isAi) {
        ModuleSizeGateCheckMetric result = new ModuleSizeGateCheckMetric();

        IntegrateAreaBO integrateAreaBO = integrateAreaService.getIntegrateAreaBO(integrateAreaId);

        Preconditions.checkNotNull(integrateAreaBO.getVersionPlanId(), "版本计划不能为空");

        Long continuousIntegrateAreaId = integrateAreaService.findByIntegrateApplicationIdAndType(
            integrateAreaBO.getIntegrateApplicationId(),
            IntegrateAreaType.CONTINUOUS_INTEGRATION).get(0).getId();

        //持续集成区 -> 版本集成区 同步的集成单
        List<Long> syncIntegrateSheetIds = integrationSheetAreaRelationService.findRelations(continuousIntegrateAreaId,
                integrateAreaId)
            .stream()
            .map(IntegrationSheetAreaRelationBO::getIntegrateSheetId)
            .collect(Collectors.toList());

        //排除掉同步的集成单
        List<IntegrateSheetInnerBO> integrateSheets = integrateSheetInnerService.findIntegrateSheetList(
                IntegrateSheetQuery.builder().versionPlanId(integrateAreaBO.getVersionPlanId()).build(), null)
            .stream()
            .filter(integrateSheetInnerBO -> !syncIntegrateSheetIds.contains(integrateSheetInnerBO.getId()))
            .collect(Collectors.toList());

        result.setMcIntegrateSheetSize((int) integrateSheets
            .stream()
            .filter(integrateSheetInnerBO -> {
                List<EntitySourcePlatform> entitySourcePlatforms
                    = entitySourcePlatformService.findByEntityTypeAndEntityId(EntityType.INTEGRATE_SHEET,
                    integrateSheetInnerBO.getId());
                if (CollectionUtils.isNotEmpty(entitySourcePlatforms)) {
                    EntitySourcePlatform entitySourcePlatform = entitySourcePlatforms.get(0);
                    if (SourcePlatformEnum.AUTO.equals(entitySourcePlatform.getSource())
                        || SourcePlatformEnum.MTL_CLOUD.equals(entitySourcePlatform.getSource())) {
                        return true;
                    } else {
                        log.info(">>>{}, {}", entitySourcePlatform.getSource(), integrateSheetInnerBO.getId());
                        return false;
                    }
                } else {
                    log.info(">>>empty, {}", integrateSheetInnerBO.getId());
                    return false;
                }
            }).count());

        Set<Long> integrateSheetIds = new HashSet<>();
        Set<Long> alterSheetIds = new HashSet<>();

        integrateSheetIds.addAll(
            integrateSheets.stream().map(IntegrateSheetInnerBO::getId).collect(Collectors.toSet()));

        alterSheetIds.addAll(integrateSheets.stream().map(IntegrateSheetInnerBO::getAlterSheetId).collect(
            Collectors.toSet()));

        Set<Long> pipelineIds = new HashSet<>();

        log.info(">>>111");

        pipelineIds.addAll(pipelineRelationService.queryPipelineRelations(EntityType.INTEGRATE_SHEET,
                new ArrayList<>(integrateSheetIds))
            .stream()
            .map(PipelineRelation::getPipelineId)
            .collect(Collectors.toList()));

        log.info(">>>222");

        pipelineIds.addAll(pipelineRelationService.queryPipelineRelations(EntityType.ALTER_SHEET,
                new ArrayList<>(alterSheetIds))
            .stream()
            .map(PipelineRelation::getPipelineId)
            .collect(Collectors.toList()));

        log.info(">>>333");

        Set<Long> pipelineInstanceIds = pipelineExecuteService.findPipelineInstanceByPipelineIds(
                new ArrayList<>(pipelineIds))
            .stream()
            .map(PipelineInstance::getId)
            .collect(Collectors.toSet());

        log.info(">>>444");

        List<GateCheckSummary> gateCheckSummaries = gateCheckService.batchGetGateCheckSummary(
                new ArrayList<>(pipelineInstanceIds))
            .stream()
            .filter(gateCheckSummary -> {
                if (isAi) {
                    return "com.alibaba.mtl4.plugin.team-space-module-size-plugin".equals(gateCheckSummary.getGateId());
                } else {
                    return Arrays.asList("com.alibaba.mtl4.plugin.team-space-module-size-plugin",
                        "com.alibaba.mtl4.plugin.android-module-size-plugin",
                        "com.alibaba.mtl4.plugin.alter-sheet-module-size-plugin",
                        "com.alibaba.mtl4.plugin.ios-module-size-plugin").contains(gateCheckSummary.getGateId());
                }
            })
            .collect(Collectors.toList());

        log.info(">>>555");

        List<Long> gateCheckIds = gateCheckSummaries.stream()
            .map(GateCheckSummary::getId)
            .collect(Collectors.toList());

        log.info(">>>gateCheckIds size:{}", gateCheckIds.size());

        List<GateApproval> gateApprovals = new ArrayList<>();
        for (int i = 0; i < gateCheckIds.size(); i += 100) {
            int end = Math.min(i + 100, gateCheckIds.size());
            gateApprovals.addAll(gateApprovalRepository.findByGateCheckIdIn(gateCheckIds.subList(i, end))
                .stream()
                .filter(gateApproval -> Objects.equals(ApprovalStatus.APPROVED, gateApproval.getApprovalStatus()))
                .filter(gateApproval -> (gateApproval.getGmtModified().getTime() - gateApproval.getGmtCreate().getTime())
                    < DateUtils.ONE_DAY_TIME)
                .collect(Collectors.toList()));
        }

        result.setIntegrateSheetSize(integrateSheets.size());
        result.setGateApprovals(gateApprovals);

        //gateApprovals.stream()
        //    .collect(Collectors.groupingBy(gateApproval -> gateCheckSummaries.stream()
        //        .filter(gateCheckSummary -> gateCheckSummary.getId().equals(gateApproval.getGateCheckId()))
        //        .findFirst()
        //        .map(GateCheckSummary::getGateId)
        //        .orElse("")))
        //    .forEach((key, value) -> {
        //        log.info(">>>gate id: {}, {}", key, value.size());
        //
        //        value.stream()
        //            .collect(Collectors.groupingBy(GateApproval::getApprovalStatus))
        //            .forEach((key1, value1) -> {
        //                log.info(">>>approval status: {}, {}", key1, value1.size());
        //            });
        //
        //    });

        return result;
    }

    @Data
    public static class ModuleSizeGateCheckMetric {
        private int integrateSheetSize;
        private int mcIntegrateSheetSize;

        private List<GateApproval> gateApprovals = new ArrayList<>();

        public String toString(CollaborationSpaceService collaborationSpaceService) {
            DescriptiveStatistics statistics = new DescriptiveStatistics();
            gateApprovals.stream()
                .mapToLong(
                    gateApproval -> gateApproval.getGmtModified().getTime() - gateApproval.getGmtCreate().getTime())
                .forEach(statistics::addValue);

            List<GateApproval> notLgGateApprovals = gateApprovals.stream()
                .filter(gateApproval -> {
                    CollaborationSpaceBO collaborationSpaceBO = collaborationSpaceService.getPersonalTeamSpace(
                        gateApproval.getCreator());
                    if (null != collaborationSpaceBO) {
                        return !Arrays.asList(1066615L, 1066647L, 1066828L, 1067908L, 1067914L, 1068017L).contains(
                            collaborationSpaceBO.getId());
                    }
                    return true;
                }).collect(Collectors.toList());
            DescriptiveStatistics notLgStatistics = new DescriptiveStatistics();
            notLgGateApprovals.stream()
                .mapToLong(
                    gateApproval -> gateApproval.getGmtModified().getTime() - gateApproval.getGmtCreate().getTime())
                .forEach(notLgStatistics::addValue);

            //double gateApprovalRate = ((double)gateApprovals.size()) / integrateSheetSize;

            return String.format(
                "integrateSheetSize:%s, mcIntegrateSheetSize:%s, gateApprovalSize:%s, gateApprovalSizeAvg:%s, gateApprovalSizeMedian:%s, notLgGateApprovalSize:%s, notLgGateApprovalAvg:%s, notLgGateApprovalSizeMedian:%s",
                integrateSheetSize, mcIntegrateSheetSize,
                gateApprovals.size(), statistics.getMean(), statistics.getPercentile(50),
                notLgGateApprovals.size(), notLgStatistics.getMean(), notLgStatistics.getPercentile(50)
            );
        }
    }

}
