package com.alibaba.emas.mtl4.services.dev.insight.generator;

import com.alibaba.emas.mtl4.services.dev.ServiceFacade;
import com.alibaba.emas.mtl4.services.dev.insight.dto.request.InsightItemUpdateCmd;
import com.alibaba.emas.mtl4.services.dev.insight.executor.InsightItemUpdateCmdExe;
import com.alibaba.emas.mtl4.services.dev.insight.model.InsightItem;
import com.alibaba.emas.publish.meta.type.DeviceDimision;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ApiLevelRateInsightItemGenerator implements InsightItemGenerator {

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private InsightItemUpdateCmdExe insightItemUpdateCmdExe;

    @Override
    public void generate(InsightItem insightItem) {
        String value = serviceFacade.getPublishDataService().getDimisionUpdatePercent(
                insightItem.getGroup().getApplication(), insightItem.getGroup().getRelease(), DeviceDimision.API_LEVEL);
        insightItem.getData().updateValue(Double.parseDouble(value.replace("%", "")) / 100);
        insightItemUpdateCmdExe.execute(InsightItemUpdateCmd.builder().item(insightItem).build());
    }
}
