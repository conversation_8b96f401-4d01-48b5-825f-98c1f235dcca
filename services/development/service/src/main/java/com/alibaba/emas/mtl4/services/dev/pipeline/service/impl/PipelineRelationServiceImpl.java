package com.alibaba.emas.mtl4.services.dev.pipeline.service.impl;


import com.alibaba.emas.mtl4.commons.utils.*;
import com.alibaba.emas.mtl4.core.message.job.PipelineJob;
import com.alibaba.emas.mtl4.core.message.job.PipelineNode;
import com.alibaba.emas.mtl4.services.dev.ServiceFacade;
import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineRelation;
import com.alibaba.emas.mtl4.services.dev.pipeline.PipelineRelationService;
import com.alibaba.emas.mtl4.services.dev.pipeline.domain.pipeline.PipelineRelationDO;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineStage;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.pipeline.PipelineInstance;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.template.PipelineSceneType;
import com.alibaba.emas.mtl4.services.dev.pipeline.repository.PipelineRelationRepository;
import com.alibaba.emas.mtl4.services.dev.release.domain.Release;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineConstants.Context.PipelineTypeKey;
import static com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineConstants.Type.Module;
import static com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineConstants.Type.ModuleUnitTest;

/**
 * created by qingli.hwz on 2019-07-30
 */
@Service
@Slf4j
@DependsOn("pipelineCallbackServiceContext")
public class PipelineRelationServiceImpl implements PipelineRelationService {

    @Autowired
    private PipelineRelationRepository pipelineRelationRepository;

    @Autowired
    private ServiceFacade serviceFacade;

    @Override
    public void removePipelineRelation(Long pipelineRelationId) {
        pipelineRelationRepository.deleteById(pipelineRelationId);
    }

    @Override
    public Long savePipelineRelation(PipelineRelation pipelineRelation) {
        if (pipelineRelation == null) {
            log.error("pipelineRelation cannot be null.");
            return null;
        }

        PipelineRelationDO relationDO = new PipelineRelationDO();
        BeanUtils.copyProperties(pipelineRelation, relationDO);
        relationDO.setGmtCreate(new Date());
        relationDO.setGmtModified(new Date());
        pipelineRelationRepository.save(relationDO);
        return relationDO.getId();
    }

    @Override
    public List<PipelineRelation> queryPipelineRelations(Long entityId, EntityType entityType) {
        Assert.notNull(entityId, "entityId cannot be null");
        Assert.notNull(entityType, "entityType cannot be null");

        List<PipelineRelationDO> pipelineRelationDOs = pipelineRelationRepository.findByEntityIdAndEntityTypeOrderByIdDesc(entityId, entityType);
        return pipelineRelationDOs.stream().map(this::convertFromPipelineRelationDO).collect(Collectors.toList());
    }

    @Override
    public List<PipelineRelation> queryPipelineRelations(EntityType entityType, Long entityId, String scope) {
        Assert.notNull(entityId, "entityId cannot be null");
        Assert.notNull(entityType, "entityType cannot be null");
        Assert.notNull(scope, "scope cannot be null");

        List<PipelineRelationDO> pipelineRelationDOs =
                pipelineRelationRepository.findByEntityIdAndEntityTypeAndScope(entityId, entityType, scope);
        return pipelineRelationDOs.stream().map(this::convertFromPipelineRelationDO).collect(Collectors.toList());
    }

    @Override
    public List<PipelineRelation> queryPipelineRelations(EntityType entityType, Long entityId, List<String> scopes) {
        Assert.notNull(entityType, "entityType cannot be null");
        Assert.notNull(entityId, "entityId cannot be null");
        Assert.isTrue(CollectionUtils.isNotEmpty(scopes), "scopes cannot be null");

        List<PipelineRelationDO> pipelineRelationDOs =
                pipelineRelationRepository.findByEntityIdAndEntityTypeAndScopeIn(entityId, entityType, scopes);
        return pipelineRelationDOs.stream().map(this::convertFromPipelineRelationDO).collect(Collectors.toList());
    }

    @Override
    public Map<Long, Long> getPipelineIdMapByEntityAndScope(EntityType entityType, List<Long> entityIdList, String scope) {
        Assert.isTrue(CollectionUtils.isNotEmpty(entityIdList));
        Assert.notNull(entityType);
        Assert.notNull(scope);

        List<PipelineRelationDO> pipelineRelationDOs = pipelineRelationRepository.findByEntityTypeAndEntityIdInAndScope(
                entityType, entityIdList, scope);
        return Optional.ofNullable(pipelineRelationDOs)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(PipelineRelationDO::getEntityId, PipelineRelationDO::getPipelineId,
                        (oldValue, newValue) -> newValue));
    }

    @Override
    public List<PipelineRelation> queryPipelineRelations(EntityType entityType, List<Long> entityIds) {
        Assert.notNull(entityType);
        Assert.notNull(entityIds);

        List<PipelineRelationDO> pipelineRelations = pipelineRelationRepository.findByEntityTypeAndEntityIdInAndInstanceIdIsNull(entityType, entityIds);
        return pipelineRelations.stream().map(this::convertFromPipelineRelationDO).collect(Collectors.toList());
    }


    @Override
    public List<PipelineRelation> queryPipelineInstanceRelationsByIdDesc(EntityType entityType, Long entityId) {
        Assert.notNull(entityId);
        Assert.notNull(entityType);

        List<PipelineRelation> pipelineRelations = new ArrayList<>();
        List<PipelineRelationDO> pipelineRelationDOs = pipelineRelationRepository.findByEntityTypeAndEntityIdAndInstanceIdIsNotNullOrderByIdDesc(entityType.name(), entityId);

        if (pipelineRelationDOs != null && !pipelineRelationDOs.isEmpty()) {
            pipelineRelationDOs.forEach(pipelineRelationDO -> {
                PipelineRelation pipelineRelation = convertFromPipelineRelationDO(pipelineRelationDO);
                pipelineRelations.add(pipelineRelation);
            });
        }
        return pipelineRelations;
    }

    @Override
    public PipelineRelation queryPipelineRelationByInstance(Long pipelineInstanceId) {
        if (pipelineInstanceId == null) {
            return null;
        }

        // 先按照pipeline instance查找
        PipelineRelationDO pipelineRelationDO = pipelineRelationRepository.findByInstanceId(pipelineInstanceId);
        if (pipelineRelationDO != null) {
            return convertFromPipelineRelationDO(pipelineRelationDO);
        }

        // 如果找不到，再按照pipeline查找
        PipelineInstance pipelineInstance = serviceFacade.getPipelineExecuteService().findPipelineInstanceById(pipelineInstanceId);
        if (pipelineInstance == null) {
            return null;
        }
        Long pipelineId = pipelineInstance.getPipelineId();
        List<PipelineRelationDO> pipelineRelationDOList = pipelineRelationRepository.findByPipelineId(pipelineId);
        if (CollectionUtils.isEmpty(pipelineRelationDOList)) {
            return null;
        }

        return convertFromPipelineRelationDO(pipelineRelationDOList.get(0));
    }

    @Override
    public PipelineRelation queryBasePipelineRelationByInstance(Long pipelineInstanceId) {
        PipelineRelation pipelineRelation = queryPipelineRelationByInstance(pipelineInstanceId);
        if (pipelineRelation == null) {
            return null;
        }
        EntityType entityType = pipelineRelation.getEntityType();
        switch (entityType) {
            case RELEASE:
                Long releaseId = pipelineRelation.getEntityId();
                BizResult<Release> findRelease = serviceFacade.getReleaseService().queryRelease(releaseId);
                if (!findRelease.isSuccess()) {
                    return null;
                }
                Release release = findRelease.getData();
                Long integrationAreaId = release.getIntegrationAreaId();
                List<PipelineRelation> pipelineRelations = queryPipelineRelations(
                        integrationAreaId != null ? integrationAreaId : release.getAlterSheetId(),
                        integrationAreaId != null ? EntityType.INTEGRATE_AREA : EntityType.ALTER_SHEET
                );
                if (CollectionUtils.isEmpty(pipelineRelations)) {
                    return null;
                } else {
                    return pipelineRelations.get(0);
                }
            default:
                return pipelineRelation;
        }
    }

    @Override
    public PipelineRelation queryPipelineRelationByPipelineId(Long pipelineId) {
        List<PipelineRelationDO> relations = pipelineRelationRepository.findByPipelineIdAndInstanceIdIsNull(pipelineId);
        if (CollectionUtils.isEmpty(relations)) {
            return null;
        }
        return convertFromPipelineRelationDO(relations.get(0));
    }

    @Override
    public List<PipelineRelation> queryPipelineRelations(List<Long> pipelineIds) {
        Assert.notNull(pipelineIds, "pipelineIds cannot be null");

        List<PipelineRelationDO> pipelineRelations = pipelineRelationRepository.findByPipelineIdInAndInstanceIdIsNull(pipelineIds);
        return pipelineRelations.stream().map(this::convertFromPipelineRelationDO).collect(Collectors.toList());
    }


    @Override
    public void deleteAllPipelineByEntityRelation(EntityType entityType, Long entityId) {
        pipelineRelationRepository.deleteByEntityTypeAndEntityId(entityType, entityId);
    }

    @Override
    public void deletePipelineRelation(EntityType entityType, Long entityId, String scope) {
        pipelineRelationRepository.deleteByEntityTypeAndEntityIdAndScope(entityType, entityId, scope);
    }


    @Override
    public void deletePipelineRelations(List<Long> relationIds) {
        Assert.notNull(relationIds);
        relationIds.forEach(relationId -> pipelineRelationRepository.deleteById(relationId));
    }

    @Override
    public List<PipelineRelation> queryPipelineRelations(Long entityId,
                                                         EntityType entityType,
                                                         PipelineSceneType pipelineSceneType,
                                                         Map<String, String> context) {
        Assert.notNull(entityType);
        Assert.notNull(entityId);
        Assert.notNull(pipelineSceneType);
        Assert.notNull(context);

        List<PipelineRelationDO> pipelineRelations = pipelineRelationRepository.findByEntityTypeAndEntityIdAndScope(
                entityType, entityId, pipelineSceneType.name());

        return pipelineRelations.stream()
                .filter(relationDO -> context.entrySet().stream()
                        .allMatch(e -> Objects.equals(e.getValue(), relationDO.getContext().get(e.getKey()))))
                .map(this::convertFromPipelineRelationDO).collect(Collectors.toList());
    }

    @Override
    public List<Long> queryPipelineJobIds(EntityType entityType, Long entityId, String jobName) {
        Assert.notNull(entityType);
        Assert.notNull(entityId);
        Assert.notNull(jobName);
        List<PipelineRelation> pipelineRelations = queryPipelineRelations(entityId, entityType);
        //通过 pipeline relations 的 pipeline ID 获取 stages
        List<List<PipelineStage>> pipelineStages = new ArrayList<>();
        Set<Long> uniqueValues = new HashSet<>();
        for (PipelineRelation pipelineRelation : pipelineRelations) {
            Long pipelineId = pipelineRelation.getPipelineId();
            if (uniqueValues.add(pipelineId)) {
                List<PipelineStage> stageByPipelineId = serviceFacade.getStageService().findStageByPipelineId(pipelineId);
                pipelineStages.add(stageByPipelineId);
            }
        }
        //通过 stages 获取 job ID, 过滤 name
        List<Long> jobIds = new ArrayList<>();
        for (List<PipelineStage> pipelineStageList : pipelineStages) {
            for (PipelineStage pipelineStage : pipelineStageList) {
                List<Long> jobIdList = pipelineStage.getJobs().stream()
                        .filter(pipelineJob -> pipelineJob.getName().equals(jobName))
                        .map(PipelineJob::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(jobIdList)) {
                    jobIds.addAll(jobIdList);
                }
            }
        }
        return jobIds;
    }

    private PipelineRelation convertFromPipelineRelationDO(PipelineRelationDO pipelineRelationDO) {
        if (pipelineRelationDO == null) {
            return null;
        }

        PipelineRelation pipelineRelation = new PipelineRelation();
        BeanUtils.copyProperties(pipelineRelationDO, pipelineRelation);
        if (pipelineRelationDO.getGmtCreate() != null) {
            pipelineRelation.setCreateTime(pipelineRelationDO.getGmtCreate().getTime());
        }
        if (pipelineRelationDO.getGmtModified() != null) {
            pipelineRelation.setModifiedTime(pipelineRelationDO.getGmtModified().getTime());
        }

        return pipelineRelation;
    }


}