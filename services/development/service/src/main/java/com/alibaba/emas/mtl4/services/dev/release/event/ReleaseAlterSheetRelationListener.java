package com.alibaba.emas.mtl4.services.dev.release.event;

import com.alibaba.emas.mtl4.services.dev.api.release.model.ReleaseBO;
import com.alibaba.emas.mtl4.services.dev.release.service.ReleaseAlterRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class ReleaseAlterSheetRelationListener {
    @Autowired
    private ReleaseAlterRelationService releaseAlterRelationService;

    @Async
    @EventListener
    public void onCreate(ReleaseCreateEvent event) {
        if (event == null || event.getReleaseBO() == null) {
            log.error("ReleaseCreateEvent is invalid");
            return;
        }

        ReleaseBO releaseBO = event.getReleaseBO();

        List<Long> relationIds = releaseAlterRelationService.addRelation(releaseBO);
        log.info("add release alter sheet relations: {}", relationIds);
    }
}
