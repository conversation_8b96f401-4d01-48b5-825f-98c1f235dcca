package com.alibaba.emas.mtl4.services.dev.common;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.emas.mpop.services.feedback.model.dto.BizSimpleDTO;
import com.alibaba.emas.mpop.services.feedback.model.dto.MpopProductSimpleDTO;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.core.message.job.PipelineJobInstance;
import com.alibaba.emas.mtl4.services.dev.ServiceFacade;
import com.alibaba.emas.mtl4.services.dev.api.enums.AlterSheetType;
import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.api.enums.IntegrateAreaType;
import com.alibaba.emas.mtl4.services.dev.api.model.*;
import com.alibaba.emas.mtl4.services.dev.api.release.model.PublishWay;
import com.alibaba.emas.mtl4.services.dev.api.release.model.ReleaseBO;
import com.alibaba.emas.mtl4.services.dev.app.enums.ModuleDeployVersionType;
import com.alibaba.emas.mtl4.services.dev.casual.build.PipelineExecuteRecordService;
import com.alibaba.emas.mtl4.services.dev.casual.build.model.PipelineExecuteRecord;
import com.alibaba.emas.mtl4.services.dev.integrate.domain.IntegrateSheet;
import com.alibaba.emas.mtl4.services.dev.integrate.repository.IntegrateSheetRepository;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineRelation;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.pipeline.PipelineInstance;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.template.PipelineSceneType;
import com.alibaba.emas.mtl4.services.dev.publish.model.PublishFilesParamBO;
import com.alibaba.emas.mtl4.services.dev.publish.model.PublishMatchProps;
import com.alibaba.emas.mtl4.services.dev.workflow.adapter.DevSpaceAdapterService;
import com.alibaba.emas.mtl4.services.flow.client.work.WorkflowService;
import com.alibaba.emas.mtl4.services.flow.client.work.WorkflowStepService;
import com.alibaba.emas.mtl4.services.flow.model.work.Workflow;
import com.alibaba.emas.mtl4.services.flow.model.work.WorkflowEntityInfo;
import com.alibaba.emas.mtl4.services.flow.model.work.query.WorkflowQuery;
import com.alibaba.emas.mtl4.services.flow.model.work.query.WorkflowTaskInstanceQuery;
import com.alibaba.emas.mtl4.services.flow.model.work.stage.WorkflowStage;
import com.alibaba.emas.mtl4.services.flow.model.work.step.DisplayableStep;
import com.alibaba.emas.mtl4.services.flow.model.work.step.WorkflowStep;
import com.alibaba.emas.mtl4.services.flow.model.work.step.display.SystemDisplay;
import com.alibaba.emas.mtl4.services.flow.model.work.step.display.WorkflowDisplay;
import com.alibaba.emas.mtl4.services.flow.model.work.task.instance.WorkflowTaskInstance;
import com.alibaba.emas.mtl4.services.flow.service.impl.work.strategy.WorkStepStrategyFacade;
import com.alibaba.emas.services.feedback.client.MpopBizOpenApi;
import com.alibaba.emas.services.feedback.client.MpopProductApi;
import com.alibaba.motu.utils.DateUtils;
import com.alibaba.motu.utils.result.Result;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.alibaba.emas.mtl4.commons.utils.StringKit.lowerCamel;
import static com.alibaba.emas.mtl4.services.dev.publish.model.PublishMatchProps.PROP_MATCH_ARGS_VALUE_64;

/**
 * <AUTHOR>
 * @Date 2022-02-28
 */
@Service
@Slf4j
public class DetailUrlServiceImpl implements DetailUrlService {

    private static final String ITERATION_URL_WITH_STAGE = "%s/#/iteration/%s/detail?spaceId=%s&iterationId=%s&stageIndex=%s";
    private static final String ITERATION_URL_WITH_STEP = "%s/#/iteration/%s/detail?spaceId=%s&iterationId=%s&stepId=%s";
    private static final String ITERATION_URL_WITH_STAGE_STEP =
            "%s/#/iteration/%s/detail?spaceId=%s&iterationId=%s&stageIndex=%s&stepId=%s";
    private static final String ITERATION_URL = "%s/#/iteration/%s/detail?spaceId=%s&iterationId=%s";
    private static final String DYNAMIC_RELEASE_URL = "%s/#/dynamic/release/%s";
    private static final String MODULE_SIZE_GATE_DETAIL = "%s/#/efficiency/%d/restrict-task-record?recordId=%d";
    private static final String CASUAL_BUILD_DETAIL_URL = "%s/#/teamspaces/%d/builds?pipelineInstanceId=%d";


    @HSFConsumer(serviceVersion = "1.0.0")
    private MpopProductApi mpopProductApi;

    @HSFConsumer(serviceVersion = "1.0.0")
    private MpopBizOpenApi mpopBizOpenApi;

    @Value("${mtl4.gateway.addr:http://mtl4.alibaba.net}")
    private String gatewayAddress;

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private WorkflowStepService workflowStepService;

    @Autowired
    private DevSpaceAdapterService devSpaceAdapterService;

    @Autowired
    private WorkStepStrategyFacade workStepStrategyFacade;

    @Autowired
    private IntegrateSheetRepository integrateSheetRepository;

    @Autowired
    private PipelineExecuteRecordService executeRecordService;

    @Override
    public String getModuleSizeGateDetail(Long spaceId, Long recordId) {
        return String.format(MODULE_SIZE_GATE_DETAIL, getMcGatewayAddress(), spaceId, recordId);
    }

    private String getMcGatewayAddress() {
        return toMcUrl(gatewayAddress);
    }

    @Override
    public String getCommonFullLink(String link) {
        return gatewayAddress + "/" + link;
    }

    @Override
    public String getQrCodeFullLink(String qrCodeLink) {
        return gatewayAddress + "/" + qrCodeLink;
    }

    @Override
    public String getAlterSheetUrl(Long spaceId, Long alterSheetId) {
        if (null != spaceId) {
            return String.format("%s/#/collaborationSpace/detail/%s/cr/%s", gatewayAddress, spaceId, alterSheetId);
        } else {
            return String.format("%s/#/cr/detail/%s", gatewayAddress, alterSheetId);
        }
    }

    @Override
    public String getReleaseDetailUrl(Long applicationId, Long releaseId) {
        return gatewayAddress + "/#/app/" + applicationId + "/detail/release/" + releaseId + "/build";
    }

    @Override
    public String getReleasePublishDetailUrl(Long applicationId, Long releaseId) {
        return String.format("%s/#/app/%d/detail/release/%d/publish/client",
                gatewayAddress, applicationId, releaseId);
    }

    @Override
    public String getReleasePublishCdnUrl(Long applicationId, Long releaseId) {
        ApplicationBO applicationBO = serviceFacade.getAppInnerService().findApplication(applicationId);
        List<PublishFilesParamBO> fileParams = serviceFacade.getPublishService().getBatchPublishFileOptions(
                applicationBO, releaseId, PublishWay.UPDATE);
        if (CollectionUtils.isNotEmpty(fileParams)) {
            PublishFilesParamBO fileParam = fileParams.get(0);
            if (CollectionUtils.isNotEmpty(fileParam.getPublishFiles())) {
                if (fileParam.getPublishFiles().size() == 1) {
                    return fileParam.getPublishFiles().get(0).getFileCdnUrl();
                } else {
                    Optional<PublishFile> publishFileOptional = fileParam.getPublishFiles().stream()
                            .filter(publishFile -> Boolean.TRUE.toString().equals(
                                    publishFile.getPublishMatchProp(PublishMatchProps.PROP_MATCH_IS_DEFAULT)))
                            .findFirst();
                    return publishFileOptional.map(PublishFile::getFileCdnUrl).orElse(null);
                }
            }
        }
        return null;
    }

   @Override
   public String getRelease64BitPublishCdnUrl(Long applicationId, Long releaseId) {
       ApplicationBO applicationBO = serviceFacade.getAppInnerService().findApplication(applicationId);
       List<PublishFilesParamBO> fileParams = serviceFacade.getPublishService().getBatchPublishFileOptions(
               applicationBO, releaseId, PublishWay.UPDATE);
       if (CollectionUtils.isNotEmpty(fileParams)) {
           PublishFilesParamBO fileParam = fileParams.get(0);
           if (CollectionUtils.isNotEmpty(fileParam.getPublishFiles())) {
               Optional<PublishFile> publishFileOptional = fileParam.getPublishFiles().stream()
                       .filter(publishFile -> PROP_MATCH_ARGS_VALUE_64.equals(
                               publishFile.getPublishMatchProp(PublishMatchProps.PROP_MATCH_ARGS_VALUE)))
                       .findFirst();
               return publishFileOptional.map(PublishFile::getFileCdnUrl).orElse(null);
           }
       }
       return null;
   }

    @Override
    public String getMcCommonUrl(String workflowScope, Long spaceId, Long workflowId, Long stepId) {
        return String.format(ITERATION_URL_WITH_STEP, getMcGatewayAddress(), lowerCamel(workflowScope), spaceId, workflowId, stepId);
    }

    @Override
    public String getMcAlterSheetUrl(Long alterSheetId) {
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService()
            .findSimpleAlterSheetById(alterSheetId);
        if (null != alterSheetBO.getCollaborationSpaceId()) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                .mainEntityType(EntityType.ALTER_SHEET.name())
                .mainEntityId(alterSheetBO.getId())
                .spaceId(alterSheetBO.getCollaborationSpaceId())
                .build(), true);
            if (null != workflow) {
                return getMcIterationUrlFromWorkflow(workflow);
            }
        }

        return getAlterSheetUrl(alterSheetBO.getCollaborationSpaceId(), alterSheetId);
    }

    @Override
    public String getMcAlterSheetUrl(Long alterSheetId, Long collaborationSpaceId) {
        if (null != collaborationSpaceId) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                    .mainEntityType(EntityType.ALTER_SHEET.name())
                    .mainEntityId(alterSheetId)
                    .spaceId(collaborationSpaceId)
                    .build(), true);
            if (null != workflow) {
                return getMcIterationUrlFromWorkflow(workflow);
            } else {
                log.info("workflow is null, alterSheetId: {}, spaceId: {}", alterSheetId, collaborationSpaceId);
            }
        } else {
            log.info("collaborationSpaceId is null");
        }

        return getAlterSheetUrl(collaborationSpaceId, alterSheetId);
    }

    @Override
    public String getAppDetailUrl(Long applicationId, Long alterSheetId) {
        if (isMcAlterSheet(alterSheetId)) {
            return getMcGatewayAddress() + "/#/app/" + applicationId + "/detail/setting";
        }
        return gatewayAddress + "/#/app/" + applicationId + "/detail/setting";
    }

    @Override
    public String getAppPipelineDetailUrl(Long applicationId) {
        return gatewayAddress + "/#/app/" + applicationId + "/detail/setting?tab=build&subTab=pipeline";
    }

    private boolean isMcAlterSheet(Long alterSheetId) {
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService()
                .findSimpleAlterSheetById(alterSheetId);
        if (null != alterSheetBO.getCollaborationSpaceId()) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                    .mainEntityType(EntityType.ALTER_SHEET.name())
                    .mainEntityId(alterSheetBO.getId())
                    .spaceId(alterSheetBO.getCollaborationSpaceId())
                    .build(), true);
            return null != workflow;
        }
        return false;
    }

    @Override
    public String getMcSubmitTestUrl(Long stepId, Long alterSheetId, Long submitTestId) {
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService()
            .findSimpleAlterSheetById(alterSheetId);
        if (null != alterSheetBO.getCollaborationSpaceId() && null != stepId) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                .mainEntityType(EntityType.ALTER_SHEET.name())
                .mainEntityId(alterSheetBO.getId())
                .spaceId(alterSheetBO.getCollaborationSpaceId())
                .build(), true);
            if (null != workflow) {
                String url = String.format(ITERATION_URL_WITH_STEP, getMcGatewayAddress(), lowerCamel(workflow.getScope()),
                    workflow.getSpaceId(), workflow.getId(), stepId);
                return String.format("%s&alterSheetId=%s&submitTestId=%s", url, alterSheetId, submitTestId);
            }
        }

        return getAlterSheetUrl(alterSheetBO.getCollaborationSpaceId(), alterSheetId);
    }

    @Override
    public String getMcCodeReviewUrl(Long stepId, Long alterSheetId, Long moduleId) {
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService()
            .findSimpleAlterSheetById(alterSheetId);
        if (null != alterSheetBO.getCollaborationSpaceId() && null != stepId) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                .mainEntityType(EntityType.ALTER_SHEET.name())
                .mainEntityId(alterSheetBO.getId())
                .spaceId(alterSheetBO.getCollaborationSpaceId())
                .build(), true);
            if (null != workflow) {
                String url = String.format(ITERATION_URL_WITH_STEP, getMcGatewayAddress(), lowerCamel(workflow.getScope()),
                    workflow.getSpaceId(), workflow.getId(), stepId);
                return String.format("%s&alterSheetId=%s&module=%s", url, alterSheetId, moduleId);
            }
        }

        return getAlterSheetUrl(alterSheetBO.getCollaborationSpaceId(), alterSheetId);
    }

    @Override
    public String getNewMcCodeReviewUrl(Long alterSheetId) {
        return String.format("%s/#/iterations/alterSheet/detail?entityId=%s&step=DEV_SPACE_INTEGRATE_SDK_CODE_MERGE",
                getMcGatewayAddress(), alterSheetId);
    }

    @Override
    public String getMcIntegrateSheetUrl(Long stepId, Long alterSheetId) {
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService()
            .findSimpleAlterSheetById(alterSheetId);
        if (null != alterSheetBO.getCollaborationSpaceId() && null != stepId) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                .mainEntityType(EntityType.ALTER_SHEET.name())
                .mainEntityId(alterSheetBO.getId())
                .spaceId(alterSheetBO.getCollaborationSpaceId())
                .build(), true);
            if (null != workflow) {
                String url = String.format(ITERATION_URL_WITH_STEP, getMcGatewayAddress(), lowerCamel(workflow.getScope()),
                    workflow.getSpaceId(), workflow.getId(), stepId);
                return String.format("%s&alterSheetId=%s", url, alterSheetId);
            }
        }

        return getAlterSheetUrl(alterSheetBO.getCollaborationSpaceId(), alterSheetId);
    }

    @Override
    public String getMcIntegrateSheetUrl(Long integrateSheetId) {
        Optional<IntegrateSheet> optional = integrateSheetRepository.findById(integrateSheetId);
        if (optional.isPresent()) {
            try {
                AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService()
                    .findSimpleAlterSheetById(optional.get().getAlterSheetId());
                if (null != alterSheetBO.getCollaborationSpaceId()) {
                    Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                        .mainEntityType(EntityType.ALTER_SHEET.name())
                        .mainEntityId(alterSheetBO.getId())
                        .spaceId(alterSheetBO.getCollaborationSpaceId())
                        .build(), false);
                    if (null != workflow) {
                        WorkflowStep workflowStep = serviceFacade.getWorkflowStepService().querySingleWorkflowStep(workflow, "DEV_SPACE_SDK_CONTINUOUS_INTEGRATION");
                        if (null != workflowStep) {
                            String url = String.format(ITERATION_URL_WITH_STEP, getMcGatewayAddress(), lowerCamel(workflow.getScope()),
                                workflow.getSpaceId(), workflow.getId(), workflowStep.getId());
                            return String.format("%s&alterSheetId=%s&integrationId=%s", url, alterSheetBO.getId(), integrateSheetId);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("getMcIntegrateSheetUrl error", e);
            }
        }

        return getIntegrateSheetDetailUrl(integrateSheetId, "module");
    }

    @Override
    public String getMcReleaseDetailUrl(Long applicationId, Long releaseId) {
        ReleaseBO releaseBO = serviceFacade.getReleaseService().querySimpleReleaseBOById(releaseId);
        //独立灰度的发布单，直接定位到workflow
        if (null != releaseBO.getSpaceId()) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                .mainEntityType(EntityType.RELEASE.name())
                .mainEntityId(releaseId)
                .spaceId(releaseBO.getSpaceId())
                .build(), true);
            if (null != workflow) {
                return getMcIterationUrlFromWorkflow(workflow);
            }
        } else if (null != releaseBO.getIntegrationAreaId()) { //集成区的发布单，需要带上stage
            IntegrateAreaBO integrateAreaBO = serviceFacade.getIntegrateAreaService()
                .findIntegrateArea(releaseBO.getIntegrationAreaId()).getData();
            if (null != integrateAreaBO.getSpaceId()) {
                Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                    .mainEntityType(EntityType.INTEGRATE_AREA.name())
                    .mainEntityId(integrateAreaBO.getId())
                    .spaceId(integrateAreaBO.getSpaceId())
                    .build(), true);
                if (null != workflow) {
                    return String.format(ITERATION_URL_WITH_STAGE, getMcGatewayAddress(), lowerCamel(workflow.getScope()),
                        workflow.getSpaceId(), workflow.getId(), releaseStage(workflow, releaseBO));
                }
            }
        } else if (null != releaseBO.getAlterSheetId()){
            AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService()
                .findSimpleAlterSheetById(releaseBO.getAlterSheetId());
            if (null != alterSheetBO.getReleaseSpaceId()) {
                Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                    .mainEntityType(EntityType.ALTER_SHEET.name())
                    .mainEntityId(alterSheetBO.getId())
                    .spaceId(alterSheetBO.getReleaseSpaceId())
                    .build(), true);
                if (null != workflow) {
                    return String.format(ITERATION_URL_WITH_STAGE, getMcGatewayAddress(), lowerCamel(workflow.getScope()),
                        workflow.getSpaceId(), workflow.getId(), releaseStage(workflow, releaseBO));
                }
            }
        }

        return getReleaseDetailUrl(applicationId, releaseId);
    }

    @Override
    public String getMcDynamicReleaseDetailUrl(Long applicationId, Long releaseId) {
        //动态发布的发布单，需要带上stage
        DynamicReleaseBO dynamicReleaseBO = serviceFacade.getDynamicReleaseService().findById(releaseId);
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService()
            .findSimpleAlterSheetById(dynamicReleaseBO.getAlterSheetId());
        if (null != alterSheetBO.getReleaseSpaceId()) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                .mainEntityType(EntityType.ALTER_SHEET.name())
                .mainEntityId(alterSheetBO.getId())
                .spaceId(alterSheetBO.getReleaseSpaceId())
                .build(), true);
            if (null != workflow) {
                return String.format(ITERATION_URL_WITH_STAGE, getMcGatewayAddress(), lowerCamel(workflow.getScope()),
                    workflow.getSpaceId(), workflow.getId(), releaseStage(workflow, dynamicReleaseBO));
            }
        }

        return String.format(DYNAMIC_RELEASE_URL, gatewayAddress, releaseId);
    }

    @Override
    public String getDynamicBatchDetailUrl(Long dynamicReleaseId, Long batchId) {
        DynamicReleaseBO dynamicReleaseBO = serviceFacade.getDynamicReleaseService().findById(dynamicReleaseId);
        AlterSheetBO alterSheetBO = dynamicReleaseBO.getAlterSheet();
        if (null != alterSheetBO.getReleaseSpaceId()) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                    .mainEntityType(EntityType.ALTER_SHEET.name())
                    .mainEntityId(alterSheetBO.getId())
                    .spaceId(alterSheetBO.getReleaseSpaceId())
                    .build(), true);
            if (null != workflow) {
                String identifier = "REL_SPACE_BIZ_DYNAMIC_PUBLISH_BATCH";
                WorkflowStep workflowStep = getDynamicReleaseStep(dynamicReleaseId, workflow.getStepIds(), identifier);
                if (workflowStep != null) {
                    return String.format(ITERATION_URL_WITH_STAGE_STEP + "&releaseSheetId=%s&batchId=%s", getMcGatewayAddress(),
                            lowerCamel(workflow.getScope()), workflow.getSpaceId(), workflow.getId(),
                            releaseStage(workflow, dynamicReleaseBO), workflowStep.getId(), dynamicReleaseId, batchId);
                }
                return String.format(ITERATION_URL_WITH_STAGE + "&releaseSheetId=%s&batchId=%s", getMcGatewayAddress(),
                        lowerCamel(workflow.getScope()), workflow.getSpaceId(), workflow.getId(),
                        releaseStage(workflow, dynamicReleaseBO), dynamicReleaseId, batchId);
            }
        }
        return String.format(DYNAMIC_RELEASE_URL, gatewayAddress, dynamicReleaseId);
    }

    @Override
    public String getDynamicPublishTargetDetailUrl(Long dynamicReleaseId) {
        DynamicReleaseBO dynamicReleaseBO = serviceFacade.getDynamicReleaseService().findById(dynamicReleaseId);
        AlterSheetBO alterSheetBO = dynamicReleaseBO.getAlterSheet();
        if (null != alterSheetBO.getReleaseSpaceId()) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                    .mainEntityType(EntityType.ALTER_SHEET.name())
                    .mainEntityId(alterSheetBO.getId())
                    .spaceId(alterSheetBO.getReleaseSpaceId())
                    .build(), true);
            if (null != workflow) {
                String identifier = "REL_SPACE_BIZ_DYNAMIC_PUBLISH_GOAL_CONFIRM";
                WorkflowStep workflowStep = getDynamicReleaseStep(dynamicReleaseId, workflow.getStepIds(), identifier);
                if (workflowStep != null) {
                    return String.format(ITERATION_URL_WITH_STAGE_STEP + "&releaseSheetId=%s", getMcGatewayAddress(),
                            lowerCamel(workflow.getScope()), workflow.getSpaceId(), workflow.getId(),
                            releaseStage(workflow, dynamicReleaseBO), workflowStep.getId(), dynamicReleaseId);
                }
                return String.format(ITERATION_URL_WITH_STAGE + "&releaseSheetId=%s", getMcGatewayAddress(),
                        lowerCamel(workflow.getScope()), workflow.getSpaceId(), workflow.getId(),
                        releaseStage(workflow, dynamicReleaseBO), dynamicReleaseId);
            }
        }
        return String.format(DYNAMIC_RELEASE_URL, gatewayAddress, dynamicReleaseId);
    }

    @Override
    public String getPipelineInstanceDetailUrl(Long pipelineInstanceId, PipelineRelation pipelineRelation) {
        if (pipelineRelation == null) {
            return getMcCasualBuildPipelineInstanceDetailUrl(pipelineInstanceId);
        } else {
            String uuid = serviceFacade.getWorkflowTaskInstanceService().genEntityInfoUuid(Collections.singletonList(WorkflowEntityInfo.builder()
                    .entityType(EntityType.PIPELINE_INSTANCE.name()).entityId(pipelineInstanceId).build()));
            WorkflowTaskInstance instance = serviceFacade.getWorkflowTaskInstanceService()
                    .queryFirstWorkflowTaskInstance(WorkflowTaskInstanceQuery.builder().uuid(uuid).build());
            //来自MC的构建任务
            if (null != instance && null != instance.getStepId() && null != instance.getWorkflowId()
                    && null != instance.getSpaceId() && StringUtils.isNotBlank(instance.getWorkflowScope())) {
                return getMcPipelineBuildDetailUrl(pipelineInstanceId, pipelineRelation.getPipelineId(), pipelineRelation.getEntityType(),
                        pipelineRelation.getEntityId(), pipelineRelation.getScope(), instance.getWorkflowScope(),
                        instance.getSpaceId(), instance.getWorkflowId(), instance.getStepId());
            }
            return getPipelineBuildDetailUrl(pipelineInstanceId, pipelineRelation.getPipelineId(), pipelineRelation.getEntityType(),
                    pipelineRelation.getEntityId(), pipelineRelation.getScope());
        }
    }

    @Override
    public String getPipelineInstanceDetailUrl(Long pipelineInstanceId) {
        PipelineInstance pipelineInstance = serviceFacade.getPipelineExecuteService().findPipelineInstanceById(pipelineInstanceId);
        PipelineRelation pipelineRelation = serviceFacade.getPipelineRelationService().queryPipelineRelationByPipelineId(
                pipelineInstance.getPipelineId());
        return getPipelineInstanceDetailUrl(pipelineInstanceId, pipelineRelation);
    }

    /**
     * 获取MC的流水线构建链接
     */
    public String getMcPipelineBuildDetailUrl(Long pipelineInstanceId, Long pipelineId,
                                              EntityType entityType, Long entityId, String scope, String workflowScope, Long spaceId, Long workflowId, Long stepId) {
        if (EntityType.ALTER_SHEET.equals(entityType) || EntityType.INTEGRATE_SHEET.equals(entityType)) {
            return String.format("%s&alterSheetId=%s&pipeline=%s&pipelineInstanceId=%s",
                    this.getMcCommonUrl(workflowScope, spaceId, workflowId, stepId), entityId, pipelineId, pipelineInstanceId);
        }

        if (EntityType.NATIVE_DYNAMIC_RELEASE.equals(entityType)) {
            return String.format("%s&releaseSheetId=%s&pipeline=%s&pipelineInstanceId=%s",
                    this.getMcCommonUrl(workflowScope, spaceId, workflowId, stepId), entityId, pipelineId, pipelineInstanceId);
        }

        return getPipelineBuildDetailUrl(pipelineInstanceId, pipelineId, entityType, entityId, scope);
    }

    /**
     * 获取MTL4的流水线构建链接
     */
    @Override
    public String getPipelineBuildDetailUrl(Long pipelineInstanceId, Long pipelineId,
                                            EntityType entityType, Long entityId, String scope) {
        String pipelineParam = "pipeline=" + pipelineId + "&instanceId=" + pipelineInstanceId;
        if (EntityType.ALTER_SHEET.equals(entityType)) {
            AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService().findSimpleAlterSheetById(entityId);
            String urlPrefix = gatewayAddress + "/#/cr/detail/" + entityId;
            if (alterSheetBO.getCollaborationSpaceId() != null) {
                urlPrefix = gatewayAddress + "/#/collaborationSpace/detail/" + alterSheetBO.getCollaborationSpaceId() +
                        "/cr/" + entityId;
            }
            if (PipelineSceneType.ALTER_SHEET_BUILD.name().equals(scope)) {
                return urlPrefix + "?step=BUILD" + "&" + pipelineParam;
            }
            if (PipelineSceneType.ALTER_SHEET_DEPLOY.name().equals(scope)) {
                String alterPipelineParam = "&" + pipelineParam;
                if (AlterSheetType.SDK.equals(alterSheetBO.getType())) {
                    return urlPrefix + "?step=DEPLOY&tab=DEPLOY" + alterPipelineParam;
                } else if (AlterSheetType.MAIN_FRAMEWORK.equals(alterSheetBO.getType())) {
                    return urlPrefix + "?step=MAIN_FRAMEWORK_DEPLOY" + alterPipelineParam;
                } else {
                    return urlPrefix + "?step=INTEGRATION&tab=DEPLOY" + alterPipelineParam;
                }
            }
            if (PipelineSceneType.ALTER_SHEET_UNIT_TEST.name().equals(scope)) {
                return urlPrefix + "?step=TEST&tab=UNIT_TEST&pipeline=" + pipelineId;
            }
            if (PipelineSceneType.NATIVE_DYNAMIC_ALTER_SHEET_BUILD.name().equals(scope)) {
                return urlPrefix + "?step=BUILD&pipeline=" + pipelineId;
            }
        } else if (EntityType.INTEGRATE_SHEET.equals(entityType)) {
            return gatewayAddress + "/#/integration/detail/" + entityId + "?tab=build" + "&" + pipelineParam;
        } else if (EntityType.INTEGRATE_AREA.equals(entityType)) {
            IntegrateAreaBO integrateAreaBO = serviceFacade.getIntegrateAreaInnerService().getIntegrateArea(entityId);
            if (integrateAreaBO != null) {
                return gatewayAddress + "/#/app/" + integrateAreaBO.getIntegrateApplicationId() + "/detail/integration/"
                        + entityId + "/build?" + pipelineParam;
            }
        } else if (EntityType.RELEASE.equals(entityType)) {
            ReleaseBO releaseBO = serviceFacade.getReleaseService().queryReleaseById(entityId);
            if (releaseBO != null) {
                if (PipelineSceneType.RELEASE_BETA_BUILD.name().equals(scope) ||
                        PipelineSceneType.RELEASE_BUILD.name().equals(scope)) {
                    return gatewayAddress + "/#/app/" + releaseBO.getApplicationId() + "/detail/release/" + entityId
                            + "/build?" + pipelineParam;
                }
                if (PipelineSceneType.RELEASE_BETA_BUILD_CHANNEL.name().equals(scope)
                        || PipelineSceneType.RELEASE_BUILD_CHANNEL.name().equals(scope)) {
                    return gatewayAddress + "/#/app/" + releaseBO.getApplicationId() + "/detail/release/" + entityId
                            + "/buildChannelPackage?showItem=build" + "&" + pipelineParam;
                }
            }
        } else if (EntityType.PLUGIN.equals(entityType)) {
            if (PipelineSceneType.PLUGIN_DEPLOY.name().equals(scope)) {
                return gatewayAddress + "/#/setting/plugin/" + entityId + "/detail/versionDeploy?" + pipelineParam;
            }
            return gatewayAddress + "/#/setting/plugin/" + entityId + "/detail/test?" + pipelineParam;
        } else if (EntityType.PATCH_CR.equals(entityType)) {
            if (scope.contains("BUILD")) {
                return gatewayAddress + "/?#/patch/cr/" + entityId + "/build?" + pipelineParam;
            }
            if (scope.contains("DEPLOY")) {
                return gatewayAddress + "/?#/patch/cr/" + entityId + "/dependencyPublish?" + pipelineParam;
            }
        } else if (EntityType.PATCH_RELEASE.equals(entityType)) {
            return gatewayAddress + "/?#/patch/release/" + entityId + "/build?" + pipelineParam;
        }
        return gatewayAddress + "/#/pipeline/detail/" + pipelineId + "?instance=" + pipelineInstanceId
                + "&instanceId=" + pipelineInstanceId;
    }

    @Override
    public String getMcPipelineJobInstanceDetailUrlV2(Long jobInstanceId) {
        PipelineJobInstance jobInstance = serviceFacade.getPipelineJobExecuteService().findById(jobInstanceId);
        Assert.notNull(jobInstance, "任务不存在");
        return getMcPipelineInstanceDetailUrlV2(jobInstance.getPipelineInstanceId());
    }

    @Override
    public String getMcModuleDeployUrl(Long moduleId, String moduleVersion, ModuleDeployVersionType type) {
        return String.format("%s/#/modules/%d/releaseRecord?deployVersionType=%s&status=SUCCESS&version=%s", getMcGatewayAddress(), moduleId, type.name(), moduleVersion);
    }

    @Override
    public String getMcPipelineInstanceDetailUrl(Long pipelineInstanceId) {
        PipelineInstance pipelineInstance = serviceFacade.getPipelineExecuteService().findPipelineInstanceById(pipelineInstanceId);
        PipelineRelation pipelineRelation = serviceFacade.getPipelineRelationService().queryPipelineRelationByPipelineId(
                pipelineInstance.getPipelineId());
        if (null == pipelineRelation) {
            return null;
        }
        String uuid = serviceFacade.getWorkflowTaskInstanceService().genEntityInfoUuid(Collections.singletonList(WorkflowEntityInfo.builder()
                .entityType(EntityType.PIPELINE_INSTANCE.name()).entityId(pipelineInstanceId).build()));
        WorkflowTaskInstance instance = serviceFacade.getWorkflowTaskInstanceService()
                .queryFirstWorkflowTaskInstance(WorkflowTaskInstanceQuery.builder().uuid(uuid).build());
        Assert.isTrue(null != instance && null != instance.getStepId() && null != instance.getWorkflowId()
                && null != instance.getSpaceId() && StringUtils.isNotBlank(instance.getWorkflowScope()));
        return getMcPipelineBuildDetailUrl(pipelineInstanceId, pipelineRelation.getPipelineId(), pipelineRelation.getEntityType(),
                pipelineRelation.getEntityId(), pipelineRelation.getScope(), instance.getWorkflowScope(),
                instance.getSpaceId(), instance.getWorkflowId(), instance.getStepId());
    }

    @Override
    public String getMcPipelineInstanceDetailUrlV2(Long pipelineInstanceId) {
        PipelineInstance pipelineInstance = serviceFacade.getPipelineExecuteService().findPipelineInstanceById(pipelineInstanceId);
        PipelineRelation pipelineRelation = serviceFacade.getPipelineRelationService().queryPipelineRelationByPipelineId(
                pipelineInstance.getPipelineId());
        if (null == pipelineRelation) {
            return null;
        }
        if (EntityType.ALTER_SHEET.equals(pipelineRelation.getEntityType())) {
            Workflow workflow = serviceFacade.getWorkflowService().querySingleWorkflow(WorkflowQuery.builder()
                    .mainEntityType(EntityType.ALTER_SHEET.name()).mainEntityId(pipelineRelation.getEntityId()).build(), false);
            if (null != workflow) {
                WorkflowStep workflowStep = serviceFacade.getWorkflowStepService().querySingleWorkflowStep(workflow, "DEV_SPACE_BUILD");
                if (null != workflowStep) {
                    return getMcPipelineBuildDetailUrl(pipelineInstanceId, pipelineRelation.getPipelineId(),
                            EntityType.ALTER_SHEET, pipelineRelation.getEntityId(), pipelineRelation.getScope(), workflow.getScope(), workflow.getSpaceId(), workflow.getId(), workflowStep.getId());
                }
            }
        }
        return getMcPipelineInstanceDetailUrl(pipelineInstanceId);
    }

    @Override
    public String getNewMcPipelineInstanceDetailUrlV2(Long pipelineInstanceId) {
        PipelineInstance pipelineInstance = serviceFacade.getPipelineExecuteService().findPipelineInstanceById(pipelineInstanceId);
        PipelineRelation pipelineRelation = serviceFacade.getPipelineRelationService().queryPipelineRelationByPipelineId(
            pipelineInstance.getPipelineId());
        if (null == pipelineRelation) {
            return null;
        }
        if (EntityType.ALTER_SHEET.equals(pipelineRelation.getEntityType())) {
            return String.format("%s/?ng=true#/iterations/alterSheet/detail?entityId=%s&step=DEV_SPACE_BUILD",
                getMcGatewayAddress(), pipelineRelation.getEntityId());
        }
        return getMcPipelineInstanceDetailUrlV2(pipelineInstanceId);
    }

    private WorkflowStep getDynamicReleaseStep(Long dynamicReleaseId, List<Long> stepIds, String identifier) {
        List<WorkflowStep> steps = workflowStepService.getSimpleWorkflowStepByIds(stepIds);
        for (WorkflowStep step : steps) {
            if (!(step instanceof DisplayableStep)) {
                continue;
            }
            Long dynamicReleaseSheetId = devSpaceAdapterService.getDynamicReleaseSheetId(step);
            if (!dynamicReleaseId.equals(dynamicReleaseSheetId)) {
                continue;
            }
            workStepStrategyFacade.toBO(step);
            WorkflowDisplay display = ((DisplayableStep) step).getDisplay();
            if (display instanceof SystemDisplay) {
                log.error("step {} is systemDisplay", step.getId());
                if (identifier.equals(((SystemDisplay)display).getIdentifier())) {
                    return step;
                }
            }
        }
        return null;
    }



    @Override
    public String getMcDynamicReleaseUrl(Long alterSheetId) {
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService()
            .findSimpleAlterSheetById(alterSheetId);
        if (null != alterSheetBO.getReleaseSpaceId()) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                .mainEntityType(EntityType.ALTER_SHEET.name())
                .mainEntityId(alterSheetBO.getId())
                .spaceId(alterSheetBO.getReleaseSpaceId())
                .build(), true);
            if (null != workflow) {
                return getMcIterationUrlFromWorkflow(workflow);
            }
        }
        return getMcAlterSheetUrl(alterSheetId);
    }

    @Override
    public String getMcIntegrateAreaUrl(Long integrateAreaId) {
        IntegrateAreaBO integrateAreaBO = serviceFacade.getIntegrateAreaService()
            .getIntegrateAreaBO(integrateAreaId);
        if (null != integrateAreaBO.getSpaceId()) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                .mainEntityType(EntityType.INTEGRATE_AREA.name())
                .mainEntityId(integrateAreaBO.getId())
                .spaceId(integrateAreaBO.getSpaceId())
                .build(), true);
            if (null != workflow) {
                return String.format(ITERATION_URL_WITH_STAGE, getMcGatewayAddress(), lowerCamel(workflow.getScope()),
                    workflow.getSpaceId(), workflow.getId(), "0");
            }
        }
        return getIntegrateAreaDetailUrl(integrateAreaBO.getIntegrateApplicationId(), integrateAreaId);
    }

    private String releaseStage(Workflow workflow, ReleaseBO releaseBO) {
        String stage = releaseBO.getVersion();
        switch (releaseBO.getPublishType()) {
            case BETA:
                stage = String.format("灰度 %s", releaseBO.getVersion());
                break;
            case NORMAL:
                stage = String.format("正式 %s", releaseBO.getVersion());
                break;
            default:
                break;
        }
        if (CollectionUtils.isNotEmpty(workflow.getStages())) {
            for (int i = 0; i < workflow.getStages().size(); i++) {
                WorkflowStage workflowStage = workflow.getStages().get(i);
                if (workflowStage.getStage().equals(stage)) {
                    return i + "";
                }
            }
        }
        return "";
    }

    private String releaseStage(Workflow workflow, DynamicReleaseBO dynamicReleaseBO) {
        String stage = dynamicReleaseBO.getFeatureUpdateVersion();
        switch (dynamicReleaseBO.getReleaseType()) {
            case BETA:
                stage = String.format("灰度 %s", dynamicReleaseBO.getFeatureUpdateVersion());
                break;
            case RELEASE:
                stage = String.format("正式 %s", dynamicReleaseBO.getFeatureUpdateVersion());
                break;
            case ROLLBACK:
                stage = String.format("回滚 %s", dynamicReleaseBO.getFeatureUpdateVersion());
                break;
            default:
                break;
        }
        if (CollectionUtils.isNotEmpty(workflow.getStages())) {
            for (int i = 0; i < workflow.getStages().size(); i++) {
                WorkflowStage workflowStage = workflow.getStages().get(i);
                if (workflowStage.getStage().equals(stage)) {
                    return i + "";
                }
            }
        }
        return "";
    }

    @Override
    public String getReleaseArchiveDetailUrl(Long applicationId, Long releaseId) {
        return gatewayAddress + "/#/app/" + applicationId + "/detail/release/" + releaseId + "/archive";
    }

    @Override
    public String getReleaseTestDetailUrl(Long applicationId, Long releaseId) {
        return gatewayAddress + "/#/app/" + applicationId + "/detail/release/" + releaseId + "/test";
    }

    @Override
    public String getIntegrateSheetDetailUrl(Long integrateSheetId, String tab) {
        return gatewayAddress + "/#/integration/detail/" + integrateSheetId + "?tab=" + tab;
    }

    @Override
    public String getIntegrateAreaDetailUrl(Long applicationId, Long integrateAreaId) {
        IntegrateAreaBO integrateAreaBO = serviceFacade.getIntegrateAreaService().getIntegrateAreaBO(integrateAreaId);
        String host = gatewayAddress;
        Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                .mainEntityType(EntityType.INTEGRATE_AREA.name())
                .mainEntityId(integrateAreaId)
                .build(), true);
        if (workflow != null) {
            host = getMcGatewayAddress();
        }
        if (IntegrateAreaType.CONTINUOUS_INTEGRATION.equals(integrateAreaBO.getType())) {
            return host + "/#/app/" + applicationId + "/detail/continuousIntegrationArea/" + integrateAreaId;
        }
        return host + "/#/app/" + applicationId + "/detail/integration/" + integrateAreaId;
    }

    @Override
    public String getReleaseRegressionDetailUrl(Long applicationId, Long releaseId) {
        //mc上的应用回归包地址返回新版工作台的地址
        List<Long> mcAppIds = serviceFacade.getAppInnerService().getMcPublishWorkbenchAvailableAppIds();
        if (mcAppIds.contains(applicationId)) {
            ReleaseBO releaseBO = serviceFacade.getReleaseService().querySimpleReleaseBOById(releaseId);
            if (releaseBO.getIntegrationAreaId() != null) {
                return String.format("%s/#/releases/%d?releaseId=%d", getMcGatewayAddress(),
                        releaseBO.getIntegrationAreaId(), releaseId);
            }
        }
        return String.format("%s/#/app/%d/detail/release/%d/test?testItem=regression", gatewayAddress, applicationId, releaseId);
    }

    @Override
    public String getReleaseBuildChannelPackageDetailUrl(Long applicationId, Long releaseId) {
        //mc上的应用渠道包地址返回新版工作台的地址
        List<Long> mcAppIds = serviceFacade.getAppInnerService().getMcPublishWorkbenchAvailableAppIds();
        if (mcAppIds.contains(applicationId)) {
            ReleaseBO releaseBO = serviceFacade.getReleaseService().querySimpleReleaseBOById(releaseId);
            if (releaseBO.getIntegrationAreaId() != null) {
                return String.format("%s/#/releases/%d?releaseId=%d&tab=package&regressionTab=channel",
                        getMcGatewayAddress(), releaseBO.getIntegrationAreaId(), releaseId);
            }
        }
        return String.format("%s/#/app/%d/detail/release/%d/buildChannelPackage", gatewayAddress, applicationId, releaseId);
    }

    @Override
    public String getTBAppDownloadDetailUrl(Long releaseId, String appVersion) {
        return String.format("https://appdownload.alicdn.com/emas-mtl4/release/%s/600000/600000@taobao_android_%s.apk", releaseId, appVersion);
    }

    @Override
    public String getTBRelease32BitRegressionDetailUrl(Long applicationId, Long releaseId, String appVersion) {
        List<Long> mcAppIds = serviceFacade.getAppInnerService().getMcPublishWorkbenchAvailableAppIds();
        if (mcAppIds.contains(applicationId)) {
            return getReleaseRegressionDetailUrl(applicationId, releaseId);
        }
        return String.format("%s/#/app/%s/detail/release/%s/test?regressionFile=600000@taobao_android_%s-armeabi-v7a.apk",
            gatewayAddress, applicationId, releaseId, appVersion);
    }

    @Override
    public String getReleasePatchArchiveDetailUrl(Long applicationId, Long releaseId) {
        return String.format("%s/#/app/%d/detail/release/%d/test?testItem=patchArchive", gatewayAddress, applicationId, releaseId);
    }

    @Override
    public String getReleaseFenceDetailUrl(Long applicationId, Long releaseId) {
        return String.format("%s/#/app/%d/detail/release/%d/test?testItem=fence", gatewayAddress, applicationId, releaseId);
    }

    @Override
    public String getReleaseCheckDetailUrl(Long applicationId, Long releaseId) {
        List<Long> availableAppIds = serviceFacade.getAppInnerService().getMcPublishWorkbenchAvailableAppIds();
        if (availableAppIds.contains(applicationId)) {
            ReleaseBO releaseBO = serviceFacade.getReleaseService().querySimpleReleaseBOById(releaseId);
            if (releaseBO.getIntegrationAreaId() != null) {
                return String.format("%s/#/releases/%d?releaseId=%d&tab=task", getMcGatewayAddress(),
                        releaseBO.getIntegrationAreaId(), releaseId);
            }
        }
        return String.format("%s/#/app/%d/detail/release/%d/test?testItem=check", gatewayAddress, applicationId, releaseId);
    }

    @Override
    public String getReleaseAlterRegression32BitDetailUrl(Long applicationId, Long releaseId) {
        return String.format("%s/#/app/%d/detail/release/%d/test?testItem=alterRegression32Bit", gatewayAddress, applicationId, releaseId);
    }

    @Override
    public String getCrashConvergeDetailUrl(String opsAppId, String clusterId, String crashType, String appVersion) {
        Preconditions.checkNotNull(opsAppId);
        Preconditions.checkNotNull(clusterId);
        Preconditions.checkNotNull(crashType);
        if (null == appVersion) {
            appVersion = "";
        }

        Long todayTime = DateUtils.getTodayZeroTime();
        Long yesterdayTime = DateUtils.getZeroDay(new Date(), -1).getTime();
        return String.format("https://ha.emas.alibaba-inc.com/#/page/crash?r_=/converge/detail/:appId&g_={\"appId\":\"%s\"}&l_={\"id\":\"%s\",\"errorType\":\"%s\",\"begin\":\"%s\",\"end\":\"%s\",\"subject\":\"analysis\",\"version\":\"%s\",\"countVersion\":\"%s\",\"compareVersion\":\"%s\",\"date\":\"%s\",\"compareDate\":\"%s\",\"historyRange\":[],\"precision\":\"1\"}",
            opsAppId, clusterId, crashType, todayTime, todayTime, appVersion, appVersion, appVersion, todayTime, yesterdayTime);
    }

    @Override
    public String getUserResearchUrl(String opsAppId, String appVersion, String biz, String category) {
        Preconditions.checkNotNull(opsAppId);
        if (null == appVersion) {
            appVersion = "";
        }
        if (null == category) {
            category = "";
        }
        Result<MpopProductSimpleDTO> productResult = mpopProductApi.findMpopProduct(opsAppId);
        String bizId = "";
        if (StringUtils.isNotBlank(biz)) {
            Result<List<BizSimpleDTO>>  bizResult = mpopBizOpenApi.findAllBiz(opsAppId);
            if (bizResult.isSuccess()) {
                Long longBizId = bizResult.getData().stream().filter(bizSimpleDTO -> bizSimpleDTO.getBizName().equals(biz))
                    .map(BizSimpleDTO::getId).findFirst().orElse(null);
                if (null != longBizId) {
                    bizId = longBizId.toString();
                }
            }
        }


        if (productResult.isSuccess()) {
            Long todayZeroTime = DateUtils.getTodayZeroTime();
            Long todayLastTime = DateUtils.getZeroDay(new Date(), 1).getTime() - 1;
            return String.format("https://userresearch.alibaba-inc.com/#/feedback/list?appId=%s&category=%s&bizId=%s&begin=%s&end=%s&pageSize=10&pageNum=0&r=0&productId=%s",
                opsAppId, category, bizId, todayZeroTime, todayLastTime, productResult.getData().getId());

        }
        return null;
    }

    @Override
    public String getMcIterationUrlFromWorkflow(Workflow workflow) {
        if (null != workflow) {
            return String.format(ITERATION_URL, getMcGatewayAddress(), lowerCamel(workflow.getScope()),
                workflow.getSpaceId(), workflow.getId());
        }
        return null;
    }

    @Override
    public String toMcUrl(String url) {
        return url.replace("mtl4.alibaba-inc.com", "mc.alibaba-inc.com");
    }

    @Override
    public String getVersionPlanDetailUrl(Long applicationId) {
        return String.format("%s/#/app/%d/detail/versionPlan", gatewayAddress, applicationId);
    }

    @Override
    public String getCodeReflowDetailUrl(Long alterSheetId, Long moduleId) {
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService().findSimpleAlterSheetById(alterSheetId);
        if (null != alterSheetBO.getCollaborationSpaceId()) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                    .mainEntityType(EntityType.ALTER_SHEET.name())
                    .mainEntityId(alterSheetBO.getId())
                    .spaceId(alterSheetBO.getCollaborationSpaceId())
                    .build(), true);
            if (null != workflow) {
                WorkflowStep workflowStep = workflowStepService.querySingleWorkflowStep(workflow.getId(),
                        "DEV_SPACE_SDK_CODE_REFLOW");
                if (workflowStep != null) {
                    return String.format(ITERATION_URL_WITH_STEP, getMcGatewayAddress(), lowerCamel(workflow.getScope()),
                            workflow.getSpaceId(), workflow.getId(), workflowStep.getId()) +
                            "&alterSheetId=" + alterSheetId + "&moduleId=" + moduleId;
                }
                return getMcIterationUrlFromWorkflow(workflow);
            } else {
                return gatewayAddress + "/#/collaborationSpace/detail/" + alterSheetBO.getCollaborationSpaceId() +
                        "/cr/" + alterSheetId + "?step=INTEGRATION&tab=CODE_REFLOW&moduleId=" + moduleId;
            }
        }
        return gatewayAddress + "/#/cr/detail/" + alterSheetId + "?step=INTEGRATION&tab=CODE_REFLOW&moduleId=" + moduleId;
    }

    @Override
    public String getDynamicCodeReflowDetailUrl(Long alterSheetId, Long moduleId) {
        AlterSheetBO alterSheetBO = serviceFacade.getAlterSheetService().findSimpleAlterSheetById(alterSheetId);
        if (null != alterSheetBO.getCollaborationSpaceId()) {
            Workflow workflow = workflowService.querySingleWorkflow(WorkflowQuery.builder()
                    .mainEntityType(EntityType.ALTER_SHEET.name())
                    .mainEntityId(alterSheetBO.getId())
                    .spaceId(alterSheetBO.getCollaborationSpaceId())
                    .build(), true);
            if (null != workflow) {
                WorkflowStep workflowStep = workflowStepService.querySingleWorkflowStep(workflow.getId(),
                        "DEV_SPACE_BIZ_DYNAMIC_PUBLISH_CODE_REFLOW");
                if (workflowStep != null) {
                    String detailUrl = String.format(ITERATION_URL_WITH_STEP + "&alterSheetId=%s", getMcGatewayAddress(),
                            lowerCamel(workflow.getScope()), workflow.getSpaceId(), workflow.getId(), workflowStep.getId(),
                            alterSheetId);
                    if (moduleId != null) {
                        detailUrl += "&moduleId=" + moduleId;
                    }
                    return detailUrl;
                }
                return String.format(ITERATION_URL + "&alterSheetId=%s", getMcGatewayAddress(), lowerCamel(workflow.getScope()),
                        workflow.getSpaceId(), workflow.getId(), alterSheetId);
            }
        }
        return null;
    }

    @Override
    public String getMcCasualBuildPipelineInstanceDetailUrl(Long pipelineInstanceId) {
        PipelineExecuteRecord executeRecord = executeRecordService.getPipelineExecuteRecord(pipelineInstanceId);
        if (executeRecord != null) {
            return String.format(CASUAL_BUILD_DETAIL_URL, getMcGatewayAddress(),
                    executeRecord.getSpaceId(), pipelineInstanceId);
        }
        return null;
    }

    @Override
    public String getNewMCSubmitTestUrl(Long alterSheetId) {
        return String.format("%s/#/iterations/alterSheet/detail?entityId=%s&step=DEV_SPACE_TEST",
                getMcGatewayAddress(), alterSheetId);
    }

    @Override
    public String getNewMCIntegrateSheetUrl(Long alterSheetId) {
        return String.format("%s/#/iterations/alterSheet/detail?entityId=%s&step=DEV_SPACE_SDK_INTEGRATION",
                getMcGatewayAddress(), alterSheetId);
    }

    @Override
    public String getPatchReleaseUrl(Long patchReleaseId) {
        return String.format("%s/#/patch/release/%s/build", gatewayAddress, patchReleaseId);
    }

    @Override
    public String getModuleDetailUrl(Long moduleId) {
        return String.format("%s/#/modules/%s/setting", getMcGatewayAddress(), moduleId);
    }

    @Override
    public String getAiCrDetailUrl(Long spaceId, Long recordId, Long gateCheckId, Long moduleRecordId) {
        return String.format("%s/#/efficiency/%s/restrict-task-record/code-scan/%s?recordId=%s&" +
                "recordType=code-scan&gateCheckId=%s&moduleRecordId=%s&filterKey=onlyShowHitCheckNodes",
                getMcGatewayAddress(), spaceId, recordId, recordId, gateCheckId, moduleRecordId);
    }
}