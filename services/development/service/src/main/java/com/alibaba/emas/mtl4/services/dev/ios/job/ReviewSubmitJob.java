package com.alibaba.emas.mtl4.services.dev.ios.job;

import com.alibaba.emas.apple.agent.api.model.IpaUploadStatus;
import com.alibaba.emas.mtl4.services.dev.api.enums.IpaUploadType;
import com.alibaba.emas.mtl4.services.dev.api.model.IpaUploadRecord;
import com.alibaba.emas.mtl4.services.dev.ios.query.IpaUploadRecordQuery;
import com.alibaba.emas.mtl4.services.dev.ios.service.IpaUploadManageService;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 自动提交审核
 * Created by 箫枫 on 2020/3/9.
 */

@Component
@Slf4j
public class ReviewSubmitJob extends JavaProcessor {
    @Autowired
    private IpaUploadManageService ipaUploadManageService;

    @Override
    public ProcessResult process(JobContext jobContext) {
        log.error("begin to auto submit review.");
        try{
            IpaUploadRecordQuery recordQuery = new IpaUploadRecordQuery();
            recordQuery.setStatuses(Arrays.asList(
                    IpaUploadStatus.WAIT_TO_BETA_REVIEW
            ));
            recordQuery.setAutoSubmitReview(true);
            recordQuery.setDiscard(false);
            // 针对 App_Store 发布的不自动提审
            recordQuery.setType(IpaUploadType.TEST_FLIGHT);

            List<IpaUploadRecord> ipaUploadRecords = ipaUploadManageService.getIpaUploadRecordByQuery(recordQuery);
            if(!Collections.isEmpty(ipaUploadRecords)){
                for (IpaUploadRecord ipaUploadRecord : ipaUploadRecords) {
                    ipaUploadManageService.submitIpaReview(ipaUploadRecord.getCreator(), ipaUploadRecord);
                }
            }
            return new ProcessResult(true, createResultString(ipaUploadRecords.size()));
        }catch(Exception ex){
            log.error("fail to execute TaskIdleCheckJob", ex);
            return new ProcessResult(false, ex.getMessage());
        }
    }

    private String createResultString(int submitReviewCount){
        return String.format("提交审核个数: %d个", submitReviewCount);
    }
}
