package com.alibaba.emas.mtl4.services.dev.operation.impl;

import java.util.List;

import com.alibaba.emas.mtl4.services.dev.ServiceFacade;
import com.alibaba.emas.mtl4.services.dev.api.enums.ConfigType;
import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.api.model.ApplicationBO;
import com.alibaba.emas.mtl4.services.dev.code.CodeService;
import com.alibaba.emas.mtl4.services.dev.code.model.TagCreateRequest;
import com.alibaba.emas.mtl4.services.dev.operation.ArchiveOperation;
import com.alibaba.emas.mtl4.services.dev.operation.enums.OperationResultType;
import com.alibaba.emas.mtl4.services.dev.operation.model.OperationResult;
import com.alibaba.emas.mtl4.services.dev.regression.domain.Regression;
import com.alibaba.emas.mtl4.services.dev.regression.domain.RegressionIntegrationModule;
import com.alibaba.emas.mtl4.services.dev.release.domain.Release;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.alibaba.emas.mtl4.services.dev.api.enums.ConfigAttributeName.CREATE_APP_VERSION_TAG;
import static com.alibaba.emas.mtl4.services.dev.operation.constant.ArchiveOperationConstant.CREATE_APP_VERSION_TAG_TO_MODULE_NAME;
import static com.alibaba.emas.mtl4.services.dev.operation.constant.ArchiveOperationConstant.CREATE_APP_VERSION_TAG_TO_MODULE_OPERATION_DESCRIPTION;

@Slf4j
@Component
public class ArchiveOperationAppVersionTag implements ArchiveOperation {

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private CodeService codeService;

    @Override
    public OperationResult operate(Release release, String userId) {
        int num = 0;
        Regression regression = serviceFacade.getRegressionService()
            .findLatestRegressionByReleaseId(release.getId());
        List<RegressionIntegrationModule> integrationModules = serviceFacade.getRegressionService()
            .getRegressionModulesByPipelineInstanceId(regression.getBuildPipelineInstanceId());

        if (CollectionUtils.isNotEmpty(integrationModules)) {
            ApplicationBO application = serviceFacade.getAppInnerService()
                .findApplication(release.getApplicationId());
            String tagName = String.format("%s%s", application.getIdentifier(), release.getVersion());
            for (RegressionIntegrationModule integrationModule : integrationModules) {
                String value = serviceFacade.getConfigurationService().findConfiguration(EntityType.APPLICATION,
                    integrationModule.getModuleId(), ConfigType.APPLICATION_PUBLISH_CONFIG, CREATE_APP_VERSION_TAG).getData();

                if ("true".equalsIgnoreCase(value)) {
                    try {
                        ApplicationBO module = serviceFacade.getAppInnerService().findApplication(integrationModule.getModuleId());
                        TagCreateRequest tagCreateRequest = new TagCreateRequest();
                        tagCreateRequest.setTagName(tagName);
                        tagCreateRequest.setRef(integrationModule.getIntegrateVersion());
                        codeService.createTag(module.getCodeLibraryAddress(), tagCreateRequest);
                        num++;
                    } catch (Exception e) {
                        log.error("ArchiveOperationAppVersionTag异常", e);
                    }
                }
            }
        }

        return new OperationResult(OperationResultType.SUCCESS, null, String.format("%s个模块成功创建客户端版本号Tag", num));
    }

    @Override
    public String getOperationName() {
        return CREATE_APP_VERSION_TAG_TO_MODULE_NAME;
    }

    @Override
    public String getDescription() {
        return CREATE_APP_VERSION_TAG_TO_MODULE_OPERATION_DESCRIPTION;
    }
}
