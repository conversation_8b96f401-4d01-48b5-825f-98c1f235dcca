package com.alibaba.emas.mtl4.services.dev.common.monitor;

import com.alibaba.emas.mtl4.services.dev.alter.service.SubmitTestService;
import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.alter.model.SubmitTestBO;
import com.alibaba.emas.mtl4.services.flow.client.process.FlowService;
import com.alibaba.emas.mtl4.services.flow.model.annotation.FlowBean;
import com.alibaba.emas.mtl4.services.flow.model.annotation.FlowFunctionOutput;
import com.alibaba.emas.mtl4.services.flow.model.annotation.FlowMessage;
import com.alibaba.emas.mtl4.services.flow.model.annotation.FlowMessages;
import com.alibaba.emas.mtl4.services.flow.model.common.FlowConstants;
import com.alibaba.emas.mtl4.services.flow.model.common.FlowDefinitionCategory;
import com.alibaba.emas.mtl4.services.flow.model.common.FlowVariables;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.alibaba.emas.mtl4.services.dev.common.monitor.MultipleInstanceMonitorConstants.ModuleDeploy;
import static com.alibaba.emas.mtl4.services.flow.model.common.FlowConstants.SUBMIT_TEST_ALL_MODULE_DEPLOY_COMPLETE;
import static com.alibaba.emas.mtl4.services.flow.model.common.FlowDefinitionInfo.ALTER_SHEET_ID;
import static com.alibaba.emas.mtl4.services.flow.model.common.FlowDefinitionInfo.SUBMIT_TEST_ID;

@FlowBean
@Slf4j
@Component
public class ModuleDeployMultipleInstanceMonitor implements IMultipleInstanceMonitor {
    @Autowired
    private SubmitTestService submitTestService;

    @Autowired
    private FlowService flowService;

    @Override
    public String supportType() {
        return ModuleDeploy;
    }

    @Override
    public MultipleInstanceMonitor init(Long monitorId, EntityType relatedEntityType, Long relatedEntityId) {
        //这里需要这么转一下
        if (EntityType.SUBMIT_TEST.equals(relatedEntityType)) {
            SubmitTestBO submitTest = submitTestService.findSubmitTestBO(relatedEntityId);

            MultipleInstanceMonitor monitor = new MultipleInstanceMonitor();
            monitor.setType(ModuleDeploy);
            monitor.setRelatedEntityType(EntityType.ALTER_SHEET);
            monitor.setRelatedEntityId(submitTest.getAlterSheetId());
            monitor.setInstanceEntityType(EntityType.PIPELINE_INSTANCE);
            monitor.updateExtraInfo("submit_test_id", relatedEntityId.toString());

            submitTest.getSubmitTestModuleBOList().forEach(module -> {
                switch (module.getAlterType()) {
                    case BINARY:
                        monitor.updateInstanceStatus(module.getModuleId().toString(),
                            FlowConstants.COMMON_RESULT_SUCCESS);
                        break;
                    case SOURCE:
                        monitor.updateInstanceStatus(module.getModuleId().toString(),
                            FlowConstants.COMMON_RESULT_ERROR);
                        break;
                    default:
                }
            });
            return monitor;
        } else {
            return null;
        }
    }

    @FlowMessages(messages = {@FlowMessage(name = "提测单所有模块正式版发布完成", desc = "提测单所有模块正式版发布完成，这里指整体回调",
        identity = SUBMIT_TEST_ALL_MODULE_DEPLOY_COMPLETE, category = FlowDefinitionCategory.ALTER_SHEET_CATEGORY,
        outputs = {
            @FlowFunctionOutput(info = ALTER_SHEET_ID),
            @FlowFunctionOutput(info = SUBMIT_TEST_ID),
        }, icon = "module")})
    @Override
    public void callback(MultipleInstanceMonitor multipleInstanceMonitor) {
        //如果所有正式版本都发布成功
        if (multipleInstanceMonitor.getInstanceStatuses().values().stream()
            .allMatch(FlowConstants.COMMON_RESULT_SUCCESS::equals)) {
            flowService.sendMessage(SUBMIT_TEST_ALL_MODULE_DEPLOY_COMPLETE, FlowVariables.builder()
                .with(ALTER_SHEET_ID, multipleInstanceMonitor.getRelatedEntityId())
                .with(SUBMIT_TEST_ID, Long.valueOf(multipleInstanceMonitor.getExtraInfoValue("submit_test_id")))
                .build());
        }
    }
}
