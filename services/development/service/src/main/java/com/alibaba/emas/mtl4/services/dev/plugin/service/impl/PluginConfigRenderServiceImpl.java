package com.alibaba.emas.mtl4.services.dev.plugin.service.impl;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.plugin.core.config.ConfigType;
import com.alibaba.emas.mtl4.plugin.core.config.PluginConfig;
import com.alibaba.emas.mtl4.plugin.core.config.PluginOutput;
import com.alibaba.emas.mtl4.services.dev.common.PipelineTaskDiamondConfig;
import com.alibaba.emas.mtl4.services.dev.plugin.model.*;
import com.alibaba.emas.mtl4.services.dev.plugin.service.PluginConfigRenderService;
import com.alibaba.emas.mtl4.services.plugin.api.model.PluginVersion;
import com.alibaba.emas.mtl4.services.plugin.api.service.PluginVersionApi;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020-06-10
 */
@Service
@Slf4j
public class PluginConfigRenderServiceImpl implements PluginConfigRenderService {

    @Autowired
    private PluginVersionApi pluginVersionApi;
    @Autowired
    private PipelineTaskDiamondConfig diamondConfig;

    private static String APP_SERVICE_URL_PREFIX = "dev/api/v1/app";

    private static String CERT_SERVICE_URL_PREFIX = "dev/api/v1/cert";

    @Override
    public PluginConfigVO renderPluginConfig(Long pluginVersionId) {
        Assert.notNull(pluginVersionId, "pluginVersionId cannot be null");

        PluginVersion pluginVersion = pluginVersionApi.querySimplePluginVersion(pluginVersionId);
        return renderPluginConfig(pluginVersion);
    }

    @Override
    public PluginConfigVO renderPluginConfig(PluginVersion pluginVersion) {
        if (pluginVersion == null) {
            return null;
        }

        PluginConfigVO pluginConfigVO = new PluginConfigVO();
        if (pluginVersion.getInputPluginConfigs() != null) {
            List<FormItem> inputConfigFormItems = new ArrayList<>();
            pluginVersion.getInputPluginConfigs().forEach(pluginConfig ->
                    inputConfigFormItems.add(generateFormItem(pluginConfig)));
            pluginConfigVO.setInputConfigFormItems(inputConfigFormItems);
        }

        if (pluginVersion.getOutputPluginConfigs() != null) {
            List<FormItem> outputConfigFormItems = new ArrayList<>();
            pluginVersion.getOutputPluginConfigs().forEach(pluginOutput ->
                    outputConfigFormItems.add(generateFormItem(pluginOutput)));
            pluginConfigVO.setOutputConfigFormItems(outputConfigFormItems);
        }
        return pluginConfigVO;
    }

    private FormItem generateFormItem(PluginConfig pluginConfig) {
        ConfigType configType = pluginConfig.getConfigType();
        FormItem formItem = FormItemGenerator.getFormItem(configType);
        setBasicProperty(formItem, pluginConfig);

        if ("footerselect".equals(formItem.getType())) {
            setExtensionPropertyForSelectItem((SelectFormItem) formItem, pluginConfig);
        }
        return formItem;
    }

    private FormItem generateFormItem(PluginOutput pluginOutput) {
        FormItem formItem = FormItemGenerator.getFormItem(ConfigType.TEXT);
        setBasicProperty(formItem, pluginOutput);
        return formItem;
    }


    private void setBasicProperty(FormItem formItem, PluginConfig pluginConfig) {
        formItem.setName(pluginConfig.getKey());
        formItem.setLabel(pluginConfig.getDisplayName());
        formItem.setExtra(pluginConfig.getDescription());
        formItem.setValue(pluginConfig.getDefaultValue());
        formItem.setDisabled(!pluginConfig.isEditable());
        formItem.setVisible(pluginConfig.isVisible());
        formItem.setLinkTo(pluginConfig.getLinkTo());
        formItem.setConfigType(pluginConfig.getConfigType().name());

        FormItemRule formItemRule = new FormItemRule();
        formItemRule.setRequired(pluginConfig.isRequired());
        if (formItemRule.getRequired()) {
            formItemRule.setMessage("必选字段，不能为空");
        }
        formItem.addFormItemRule(formItemRule);
    }

    private void setBasicProperty(FormItem formItem, PluginOutput pluginOutput) {
        formItem.setName(String.format("$!{%s_%s}", pluginOutput.getNamespace(), pluginOutput.getKey()));
        formItem.setLabel(pluginOutput.getDisplayName());
        formItem.setExtra(pluginOutput.getDescription());
    }

    private void setExtensionPropertyForSelectItem(SelectFormItem formItem, PluginConfig pluginConfig) {
        switch (pluginConfig.getConfigType()) {
            case SELECT:
                if (!StringUtils.isEmpty(pluginConfig.getSelectOptions())) {
                    try {
                        Map selectOptionMap = JSONObject.parseObject(pluginConfig.getSelectOptions());
                        for (Object entry : selectOptionMap.entrySet()) {
                            String label = (String) ((Map.Entry) entry).getKey();
                            String value = (String) ((Map.Entry) entry).getValue();
                            SelectOption option = new SelectOption(label, value);
                            formItem.addSelectOption(option);
                        }
                    } catch (Exception e) {
                        log.error("parse selectionOptions error:", e);
                    }
                } else {
                    formItem.setKeyword(pluginConfig.getSelectKeywords());
                    formItem.setService(pluginConfig.getSelectService());
                    addLinkToSelectQuery(formItem);
                    formItem.setEmptySearch(pluginConfig.isSelectEmptySearch());
                }
                break;
            case APP:
                formItem.setKeyword("name");
                formItem.setService(APP_SERVICE_URL_PREFIX + "/queryApps");
                formItem.setEmptySearch(true);
                break;
            case MTL_IOS_CERT:
                formItem.setPlaceholder("component.dynamic.form.select.placeholder");
                addLinkToSelectQuery(formItem);
                formItem.setService(CERT_SERVICE_URL_PREFIX + "/queryIOSCert");
                formItem.setEmptySearch(true);
                break;
            case MTL_IOS_PROFILE:
            case MTL_IOS_EXTENSION_PROFILE:
                addLinkToSelectQuery(formItem);
                formItem.setService(CERT_SERVICE_URL_PREFIX + "/queryIOSProfile");
                formItem.setEmptySearch(true);
                break;
            case COCOAPODS:
                Map<String, String> cocoapodsConfigMap = diamondConfig.getCocoapodsVersionMap();
                SelectOption defaultOption = new SelectOption("不使用", "0");
                formItem.addSelectOption(defaultOption);
                formItem.getOptions().addAll(generateOptions(cocoapodsConfigMap));
                break;
            case SDK_TYPE:
                Map<String, String> sdkTypeConfigMap = diamondConfig.getSDKTypeMap();
                formItem.setOptions(generateOptions(sdkTypeConfigMap));
                break;
            default:
        }
    }


    private void addLinkToSelectQuery(SelectFormItem formItem) {
        if (StringUtils.isNotEmpty(formItem.getLinkTo())) {
            List<String> linkToFields = new ArrayList<>(Arrays.asList(formItem.getLinkTo().split(",")));
            linkToFields.forEach(linkToField -> {
                SelectQuery selectQuery = new SelectQuery("form", linkToField);
                formItem.addSelectQuery(selectQuery);
            });
        }
    }

    private List<SelectOption> generateOptions(Map<String, String> configMap) {
        if (configMap == null || configMap.isEmpty()) {
            return Collections.emptyList();
        }

        List<SelectOption> options = new ArrayList<>();
        configMap.forEach((key, value) -> {
            SelectOption selectOption = new SelectOption();
            selectOption.setLabel(key);
            selectOption.setValue(value);
            options.add(selectOption);
        });
        return options;
    }
}
