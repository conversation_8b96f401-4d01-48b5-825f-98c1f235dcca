package com.alibaba.emas.mtl4.services.dev.build.service.impl;

import com.alibaba.emas.mtl4.services.dev.build.domain.BuildConfigTemplateSyncDO;
import com.alibaba.emas.mtl4.services.dev.build.model.BuildConfigTemplateSync;
import com.alibaba.emas.mtl4.services.dev.build.repository.BuildConfigTemplateSyncRepository;
import com.alibaba.emas.mtl4.services.dev.build.service.BuildConfigTemplateSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: qiulibin
 * @Date: 2020/6/30 9:10 PM
 * 说明：构建模板同步
 */
@Service
@Slf4j
public class BuildConfigTemplateSyncServiceImpl implements BuildConfigTemplateSyncService {


    @Autowired
    private BuildConfigTemplateSyncRepository buildConfigTemplateSyncRepository;

    /**
     * 是否导入过
     * @param applicationId
     * @return
     */
    @Override
    public Boolean isEnable(Long applicationId){
        List<BuildConfigTemplateSyncDO> results =
                buildConfigTemplateSyncRepository.findByApplicationId(applicationId);
        if (results == null || results.size()==0){
            // 没导入过
            return true;
        }

        if (results.size() == 1){
            //  正常情况只会有一个
            BuildConfigTemplateSyncDO syncDO = results.get(0);
            String enable = syncDO.getEnable();
            return Boolean.valueOf(enable);
        } else {
            // 导入过了
            return false;
        }
    }

    /**
     * 插入数据
     * @param buildConfigTemplateSync
     * @return
     */
    @Override
    public Long insert(BuildConfigTemplateSync buildConfigTemplateSync){
        BuildConfigTemplateSyncDO syncDO = toDO(buildConfigTemplateSync);
        BuildConfigTemplateSyncDO result = buildConfigTemplateSyncRepository.save(syncDO);
        return result.getId();
    }

    /**
     * 更新状态 通过数据库ID
     * @param id
     * @param enable
     * @return
     */
    @Override
    public Long updateEnableStatusById(Long id, Boolean enable){
        BuildConfigTemplateSyncDO result = buildConfigTemplateSyncRepository.getOne(id);
        if (result != null && result.getId() != null){
            result.setEnable(String.valueOf(enable));
            BuildConfigTemplateSyncDO updateResult = buildConfigTemplateSyncRepository.save(result);
            return updateResult.getId();
        }
        return null;
    }

    /**
     * 更新状态 通过应用ID
     * @param applicationId
     * @param enable
     * @return
     */
    @Override
    public Long updateEnableStatusByApplicationId(Long applicationId, Boolean enable){
        List<BuildConfigTemplateSyncDO> results = buildConfigTemplateSyncRepository.findByApplicationId(applicationId);
        if (results != null && results.size() == 1) {
            BuildConfigTemplateSyncDO result = results.get(0);

            result.setEnable(String.valueOf(enable));
            BuildConfigTemplateSyncDO updateResult = buildConfigTemplateSyncRepository.save(result);
            return updateResult.getId();
        } else {
            return null;
        }
    }

    private BuildConfigTemplateSyncDO toDO(BuildConfigTemplateSync templateSync){
        BuildConfigTemplateSyncDO syncDO = new BuildConfigTemplateSyncDO();
        BeanUtils.copyProperties(templateSync, syncDO);
        return syncDO;
    }
}
