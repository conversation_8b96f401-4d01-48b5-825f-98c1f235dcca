package com.alibaba.emas.mtl4.services.dev.utils;

import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.fastjson.JSONObject;
import jodd.http.HttpRequest;
import jodd.http.HttpResponse;
import org.apache.commons.lang.StringUtils;

/**
 * 需求管控工具
 *
 * <AUTHOR>
 * @date 2025/04/15
 */
public class DemandControlUtils {
    public static Object getDemandControlInfo(Long appId, Long versionPlanId) {
        Assert.notNull(appId, "appId cannot be null");
        Assert.notNull(versionPlanId, "versionPlanId cannot be null");

        HttpRequest request = HttpRequest.get("https://pre-mtl4.alibaba-inc.com/dev/api/v1/promotion/req")
                .query("appId", appId)
                .query("versionPlanId", versionPlanId)
                .query("pageNo", 0)
                .query("pageSize", 1000);
        request.contentType("application/json", "utf-8");

        HttpResponse response = request.send();
        String content = response.charset("utf-8").bodyText();
        if (StringUtils.isNotBlank(content)) {
            return JSONObject.parse(content);
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(getDemandControlInfo(1L, 1235L));
    }
}
