package com.alibaba.emas.mtl4.services.dev.common.monitor;

import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;

import org.springframework.stereotype.Component;

import static com.alibaba.emas.mtl4.services.dev.common.monitor.MultipleInstanceMonitorConstants.RegressionItem;

@Component
public class RegressionItemMultipleInstanceMonitor implements IMultipleInstanceMonitor {
    @Override
    public String supportType() {
        return RegressionItem;
    }

    @Override
    public MultipleInstanceMonitor init(Long monitorId, EntityType relatedEntityType, Long relatedEntityId) {
        return null;
    }

    @Override
    public void callback(MultipleInstanceMonitor multipleInstanceMonitor) {

    }
}
