package com.alibaba.emas.mtl4.services.dev.module.executer;

import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.services.dev.api.enums.gate.GateCheckStatus;
import com.alibaba.emas.mtl4.services.dev.module.command.ModuleSizeGateStartCmd;
import com.alibaba.emas.mtl4.services.dev.module.converter.ModuleSizeGateRecordConverter;
import com.alibaba.emas.mtl4.services.dev.module.gateway.ModuleSizeGateRecordGateway;
import com.alibaba.emas.mtl4.services.dev.module.model.ModuleSizeGateRecord;
import com.alibaba.emas.mtl4.services.dev.module.validator.ModuleSizeGateStartCmdValidator;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ModuleSizeGateStartCmdExe {
    @Setter(onMethod_ = {@Autowired})
    private ModuleSizeGateStartCmdValidator moduleSizeGateStartCmdValidator;
    @Setter(onMethod_ = {@Autowired})
    private ModuleSizeGateRecordConverter gateRecordConverter;
    @Setter(onMethod_ = {@Autowired})
    private ModuleSizeGateRecordGateway moduleSizeGateRecordGateway;

    public BizResult<Long> execute(ModuleSizeGateStartCmd command) {
        moduleSizeGateStartCmdValidator.validate(command);
        ModuleSizeGateRecord record = gateRecordConverter.toEntity(command);
        record.setStatus(GateCheckStatus.RUNNING);
        ModuleSizeGateRecord savedRecord = moduleSizeGateRecordGateway.save(record, command.getOperator());
        return BizResult.success(savedRecord.getId());
    }
}
