package com.alibaba.emas.mtl4.services.dev.publish.service;

import org.apache.commons.collections4.CollectionUtils;
import com.alibaba.emas.mtl4.boot.starter.web.utils.ErrorCode;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.commons.utils.BeanUtils;
import com.alibaba.emas.mtl4.commons.utils.BizResult;
import com.alibaba.emas.mtl4.services.dev.ServiceApiAssert;
import com.alibaba.emas.mtl4.services.dev.ServiceFacade;
import com.alibaba.emas.mtl4.services.dev.common.BaseServiceApi;
import com.alibaba.emas.mtl4.services.dev.common.ServiceApi;
import com.alibaba.emas.mtl4.services.dev.common.PageRequestUtils;
import com.alibaba.emas.mtl4.services.dev.publish.model.*;
import com.alibaba.emas.mtl4.services.dev.publish.query.CrowdQuery;
import com.alibaba.emas.mtl4.services.dev.publish.repository.CrowdRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: qiulibin
 * @Date: 2020/2/25 3:26 PM
 * 说明：人群服务
 */
@Service
@Slf4j
public class CrowdManageService implements BaseServiceApi {

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private CrowdRepository crowdRepository;

    @Autowired
    private PublishTargetService publishTargetService;

    /**
     * 保存人群条件
     * @param crowd
     * @return
     * @throws Exception
     */
    @ServiceApi
    public BizResult<Long> createCrowd(Crowd crowd){
        Long applicationId = crowd.getApplicationId();

        ServiceApiAssert.notNull(applicationId, ErrorCode.ILLEGAL_ARGUMENT.getErrorNo(), PublishError.c100005, PublishError.v100005);
        ServiceApiAssert.notNull(crowd.getCreator(), "请指定创建人");
        ServiceApiAssert.notNull(crowd.getCrowdType(), "请指定人群类型");
        ServiceApiAssert.notNull(crowd.getModifier(), "请指定修改人");
        ServiceApiAssert.notNull(crowd.getName(), "请指定人群名称");
        ServiceApiAssert.notNull(crowd.getConfig(), ErrorCode.ILLEGAL_ARGUMENT.getErrorNo(), PublishError.c100006, PublishError.v100006);

        //校验应用是否存在
        getApplicationBO(serviceFacade, applicationId);

        Optional<CrowdDO> optional = crowdRepository.findByApplicationIdAndName(applicationId, crowd.getName());
        Assert.isFalse(optional.isPresent(), "相同名称的人群已经存在, 请重新命名");

        CrowdDO crowdDO = convertToDO(crowd);
        crowdRepository.save(crowdDO);

        return BizResult.success(crowdDO.getId());
    }

    /**
     * 根据人群ID删除人群
     * @param crowdId
     * @return
     */
    @ServiceApi
    public BizResult<Void> deleteCrowd(Long crowdId) {
        List<PublishTargetBO> targetBOList = publishTargetService.findByCrowdId(crowdId);
        Assert.isTrue(CollectionUtils.isEmpty(targetBOList), "该人群已在发布目标配置中使用，不支持删除");

        crowdRepository.deleteById(crowdId);
        return BizResult.success(null);
    }

    @ServiceApi
    public BizResult<Boolean> setCrowdDefault(Long id) {
        Assert.notNull(id);
        Optional<CrowdDO> crowdOptional = crowdRepository.findById(id);
        if(crowdOptional.isPresent()){
            CrowdDO crowdDO = crowdOptional.get();
            crowdDO.setIsDefaultCrowd(true);
            crowdRepository.save(crowdDO);
            return BizResult.success(true);
        }
        return BizResult.success(false);
    }

    /**
     * 更新人群
     * @param crowd
     * @return
     */
    @ServiceApi
    public BizResult<Void> updateCrowd(Crowd crowd) {
        Long applicationId = crowd.getApplicationId();

        ServiceApiAssert.notNull(applicationId, ErrorCode.ILLEGAL_ARGUMENT.getErrorNo(), PublishError.c100005, PublishError.v100005);
        ServiceApiAssert.notNull(crowd.getId(), "修改人群需要指定人群id");
        ServiceApiAssert.notNull(crowd.getCreator(), "请指定创建人");
        ServiceApiAssert.notNull(crowd.getCrowdType(), "请指定人群类型");
        ServiceApiAssert.notNull(crowd.getModifier(), "请指定修改人");
        ServiceApiAssert.notNull(crowd.getName(), "请指定人群名称");
        ServiceApiAssert.notNull(crowd.getConfig(), ErrorCode.ILLEGAL_ARGUMENT.getErrorNo(), PublishError.c100006, PublishError.v100006);

        Optional<CrowdDO> optional = crowdRepository.findById(crowd.getId());
        ServiceApiAssert.isTrue(optional.isPresent(), () -> String.format("id为%d的人群配置不存在", crowd.getId()));
        Crowd preDefCrowd = convertToBO(optional.get());

        Assert.isTrue(preDefCrowd.getApplicationId().equals(crowd.getApplicationId()), "人群所属的应用不允许更改");
        Assert.isTrue(preDefCrowd.getCrowdType().equals(crowd.getCrowdType()), "人群的类型不允许更改");

        crowdRepository.save(convertToDO(crowd));
        return BizResult.success(null);
    }

    public Crowd getCrowdById(Long id){
        Optional<CrowdDO> optional = crowdRepository.findById(id);
        return convertToBO(optional.orElse(null));
    }

    @ServiceApi
    public BizResult<List<Crowd>> queryCrowdByPage(CrowdQuery query, int pageNum, int pageSize){
        CrowdDO project = new CrowdDO();
        BeanUtils.copyProperties(query, project);


        Example<CrowdDO> example = Example.of(project);

        PageRequest pageRequest = PageRequestUtils.getPageRequest(pageNum, pageSize, new Sort(Sort.Direction.DESC, "gmtCreated"));
        Page<CrowdDO> page = crowdRepository.findAll(example, pageRequest);

        List<Crowd> list = batchConvertToBO(page.getContent());
        return BizResult.okPaged(list, pageNum, pageSize, page.getTotalElements());
    }


    private List<Crowd> batchConvertToBO(List<CrowdDO> crowdDOList){
        if(CollectionUtils.isEmpty(crowdDOList)){
            return Collections.emptyList();
        }
        return crowdDOList.stream().map(this::convertToBO).collect(Collectors.toList());
    }

    private Crowd convertToBO(CrowdDO crowdDO){
        if(crowdDO == null){
            return null;
        }
        Crowd crowd = new Crowd();
        BeanUtils.copyProperties(crowdDO, crowd);
        return crowd;
    }

    private CrowdDO convertToDO(Crowd crowd){
        if(crowd == null){
            return null;
        }
        CrowdDO crowdDO = new CrowdDO();
        BeanUtils.copyProperties(crowd, crowdDO);
        return crowdDO;
    }
}
