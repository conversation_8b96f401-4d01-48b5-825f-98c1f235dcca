package com.alibaba.emas.mtl4.services.dev.module.validator;

import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.services.dev.module.command.SreModuleDetailListQry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SreModuleDetailListQryValidator {
    public void validate(SreModuleDetailListQry cmd) {
        Assert.notNull(cmd, "command 不能为空");
        Assert.notNull(cmd.getOperator(), "operator 不能为空");
        Assert.notNull(cmd.getModuleIds(), "moduleIds 不能为空");
    }
}
