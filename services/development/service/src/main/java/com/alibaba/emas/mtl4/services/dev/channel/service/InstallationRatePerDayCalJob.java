package com.alibaba.emas.mtl4.services.dev.channel.service;


import com.alibaba.emas.appgallery.api.model.reports.AppDownloadExportItem;
import com.alibaba.emas.appgallery.api.model.reports.AppDownloadExportRequest;
import com.alibaba.emas.mtl4.commons.utils.TimeUtil;
import com.alibaba.emas.mtl4.services.dev.dashboard.measurement.AppMetricMeasurementBO;
import com.alibaba.emas.mtl4.services.dev.metric.MetricUnit;
import com.alibaba.emas.mtl4.services.dev.dashboard.enums.AppMetricViewType;
import com.alibaba.emas.mtl4.services.dev.dashboard.service.AppMetricMeasurementService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.*;


/**
 * <AUTHOR>
 * @Date 2021-11-30
 */
@Component
@Slf4j
public class InstallationRatePerDayCalJob extends JavaProcessor {

    @Autowired
    private AndroidAppStoreDataService androidAppStoreDataService;

    private static final List<Long> APP_IDS = Arrays.asList(1L);

    @Autowired
    private AppMetricMeasurementService appMetricMeasurementService;


    @Override
    public ProcessResult process(JobContext jobContext) throws Exception {
        log.info("start InstallationRatePerDayCalJob");
        String jobParameters = jobContext.getJobParameters();

        Date date = new Date();
        if (jobParameters != null) {
            JSONObject parameterObj = JSON.parseObject(jobParameters);
            if (parameterObj != null) {
                String month = parameterObj.getString("month");
                try {
                    date = TimeUtil.MONTH_FORMAT.parse(month);
                } catch (ParseException e) {
                    log.error("parse date error in InstallationRatePerDayCalJob.error:", e);
                }
            }
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date startDate = calendar.getTime();
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date endDate = calendar.getTime();

        long oneDay = 1000 * 60 * 60 * 24L;
        AppDownloadExportRequest request = new AppDownloadExportRequest();
        request.setGroupBy(AppDownloadExportRequest.GroupByField.APP_VERSION);
        for(Long appId : APP_IDS) {
            long start = startDate.getTime();
            long end = endDate.getTime();
            float totalInstallationRate = 0;
            int dateCount = 0;
            while (start <= end) {
                Date time = new Date(start);
                start += oneDay;
                request.setTimeRange(time, time);
                List<AppDownloadExportItem> downloadExportItemList =
                        androidAppStoreDataService.queryHuaweiAppDownloadExportItems(appId, request);
                long maxInstallationCount = 0;
                String installationRate = null;
                for (AppDownloadExportItem item : downloadExportItemList) {
                    if (item.getInstallationCount() != null && !"其他".equals(item.getAppVersion())) {
                        if (item.getInstallationCount() > maxInstallationCount) {
                            maxInstallationCount = item.getInstallationCount();
                            installationRate = item.getInstallationRate();
                        }
                    }
                }
                if (installationRate != null) {
                    dateCount++;
                    totalInstallationRate += Float.parseFloat(installationRate.replace("%",""));
                }
            }
            if (dateCount > 0) {
                String installationRatePerDay = String.format("%.2f", totalInstallationRate / dateCount);
                AppMetricMeasurementBO measurementBO = new AppMetricMeasurementBO();
                measurementBO.setApplicationId(appId);
                measurementBO.setMetricName("华为日均安装成功率");
                measurementBO.setMetricValue(installationRatePerDay);
                measurementBO.setMainDimValue(TimeUtil.MONTH_FORMAT.format(startDate));
                measurementBO.setViewType(AppMetricViewType.APP.getName());
                measurementBO.setMetricUnit(MetricUnit.PERCENT.name());
                appMetricMeasurementService.saveAppMetricMeasurement(measurementBO);
            }
        }
        return new ProcessResult(true);
    }
}
