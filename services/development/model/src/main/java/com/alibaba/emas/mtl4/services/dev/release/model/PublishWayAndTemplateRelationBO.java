package com.alibaba.emas.mtl4.services.dev.release.model;

import com.alibaba.emas.mtl4.services.dev.api.enums.TemplateCategory;
import com.alibaba.emas.mtl4.services.dev.api.release.model.PublishType;
import com.alibaba.emas.mtl4.services.dev.api.release.model.PublishWay;
import lombok.Data;

/**
 * created by qingli.hwz on 2020-02-07
 */
@Data
public class PublishWayAndTemplateRelationBO {

    private Long id;

    private Long appId;

    private PublishType publishType;

    private PublishWay publishWay;

    private TemplateCategory templateCategory;

    private String creator;

    private String modifier;
}