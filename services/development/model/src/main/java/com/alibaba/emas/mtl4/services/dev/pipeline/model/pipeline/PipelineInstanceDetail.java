package com.alibaba.emas.mtl4.services.dev.pipeline.model.pipeline;

import java.util.Date;
import java.util.List;

import com.alibaba.emas.mtl4.commons.pipes.RunStatus;
import com.alibaba.emas.mtl4.services.dev.api.enums.TriggerType;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.stage.PipelineStageInstanceDetail;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class PipelineInstanceDetail {
    Long id;

    Long pipelineId;

    Date startTime;

    Date endTime;

    String executor;

    TriggerType triggerType;

    RunStatus pipelineExecuteStatus;

    List<PipelineStageInstanceDetail> pipelineStageInstanceDetailList;
}
