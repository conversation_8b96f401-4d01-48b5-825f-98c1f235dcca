package com.alibaba.emas.mtl4.services.dev.relation.model;

import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022-06-07
 */
@Data
public class RelationBO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 实体类型
     */
    private EntityType entityType;

    /**
     * 实体ID
     */
    private Long entityId;

    /**
     * 关联的实体类型
     */
    private EntityType relatedEntityType;

    /**
     * 关联的实体ID
     */
    private Long relatedEntityId;

    /**
     * 关系类型
     */
    private RelationType type;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String modifier;
}
