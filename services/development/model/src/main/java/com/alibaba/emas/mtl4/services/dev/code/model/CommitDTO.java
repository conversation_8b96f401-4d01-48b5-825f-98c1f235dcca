package com.alibaba.emas.mtl4.services.dev.code.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-07-01
 */
@Data
public class CommitDTO {

    @J<PERSON><PERSON>ield(name = "author_email")
    private String authorEmail;

    @J<PERSON>NField(name = "author_name")
    private String authorName;

    @JSONField(name = "authored_date")
    private Date authoredDate;

    @JSONField(name = "committed_date")
    private Date committedDate;

    @J<PERSON><PERSON>ield(name = "committer_email")
    private String committerEmail;

    @<PERSON><PERSON><PERSON><PERSON>(name = "committer_name")
    private String committerName;

    @J<PERSON><PERSON>ield(name = "created_at")
    private Date createdAt;

    private String id;

    private String message;

    @JSONField(name = "parent_ids")
    private List<String> parentIds;

    @J<PERSON>NField(name = "short_id")
    private String shortId;

    private String status;

    private String title;
}
