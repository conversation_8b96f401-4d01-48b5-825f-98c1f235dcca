package com.alibaba.emas.mtl4.services.dev.channelBuild.model;


import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class UpdatePhasedRequest {

    /**
     * 分批发布开始时间.
     */
    private Long phasedStartTime;

    /**
     * 分批发布结束时间.
     */
    private Long phasedEndTime;

    /**
     * 分批发布比例.
     * 分阶段发布百分比，取值范围为0.00到100.00，必须精确到两位小数不含百分号。
     */
    @NotNull(message = "分批发布比例不能为空")
    private String phasedRatio;

    /**
     * 发布单id
     */
    @NotNull(message = "发布单id不能为空")
    private Long releaseId;

}
