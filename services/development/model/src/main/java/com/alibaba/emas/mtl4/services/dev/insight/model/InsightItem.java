package com.alibaba.emas.mtl4.services.dev.insight.model;

import com.alibaba.emas.mtl4.services.dev.api.release.model.ReleaseBO;
import com.alibaba.emas.mtl4.services.dev.check.model.CheckItemDataVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InsightItem extends BaseBO {

    /**
     * 基础字段
     */
    private Long groupId;

    private Long applicationId;

    private Long releaseId;

    /**
     * 回归单id
     */
    private Long regressionId;

    /**
     * 回归itemid
     */
    private Long regressionItemId;

    private String appVersion;

    /**
     * 检查项名称
     */
    private String label;

    /**
     * 识别符
     */
    private String name;

    /**
     * 级别
     */
    private String level;

    /**
     * 是否阻塞
     */
    private Boolean block;

    /**
     * 检查项类型
     * NORMAL
     * CRASH
     * FEEDBACK(舆情)
     * OTHER
     * CUSTOM(自定义问题反馈)
     */
    private String type;

    /**
     * insight item原始创建人
     */
    private String origin;

    /**
     * insight 处理状态
     */
    private String status;

    /**
     * 处理人
     */
    private String processor;

    /**
     * 处理方案描述
     */
    private String planDetail;

    /**
     * 处理方案类型
     */
    private InsightItemPlanType plan;

    /**
     * 处理方案extra info
     */
    private InsightItemPlan planInfo;

    /**
     * 检查项归属
     */
    private InsightItemBelong belong;

    private CheckItemDataVO checkItemDataVO;

    /**
     * 数据
     */
    private InsightItemData data;

    /**
     * 反馈
     */
    private InsightItemFeedback feedback;

    /**
     * 自定义信息
     */
    private InsightItemCustomInfo customInfo;

    /**
     * 内容
     */
    private InsightItemContent content;

    /**
     * insight group
     */
    private InsightGroup group;

    private AoneBindAlterInfo alterInfo;

    private String validator;

    private Date gmtProcess;

    private Date gmtValidate;

    private ReleaseBO release;

    private String creatorNickName;

    private String processorNickName;

    private List<FeedbackProgress> feedbackList;

    private String aoneTitle;

    public InsightItemBelong getBelong() {
        if (null == belong) {
            belong = new InsightItemBelong();
        }
        return belong;
    }

    public InsightItemData getData() {
        if (null == data) {
            data = new InsightItemData();
        }
        return data;
    }

    public InsightItemFeedback getFeedback() {
        if (null == feedback) {
            feedback = new InsightItemFeedback();
        }
        return feedback;
    }

    public InsightItemCustomInfo getCustomInfo() {
        if (null == customInfo) {
            customInfo = new InsightItemCustomInfo();
        }
        return customInfo;
    }
}
