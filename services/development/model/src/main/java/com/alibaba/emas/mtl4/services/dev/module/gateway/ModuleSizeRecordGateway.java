package com.alibaba.emas.mtl4.services.dev.module.gateway;

import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.module.model.ModuleSizeRecord;
import com.alibaba.emas.mtl4.services.dev.module.model.PackageSizeHistoryRecord;

import java.util.List;
import java.util.Optional;

public interface ModuleSizeRecordGateway {
    void saveAll(List<ModuleSizeRecord> records, String operator);

    List<ModuleSizeRecord> findAllByGateRecordId(Long gateRecordId);

    List<ModuleSizeRecord> findLatestByVersionPlanIdAndModuleSpaceId(Long versionPlanId, Long moduleSpaceId);

    Optional<ModuleSizeRecord> findByPipelineInstanceIdAndModuleId(Long pipelineInstanceId, Long moduleId);

    void removeByGateRecordId(Long gateRecordId);

    void removeById(Long recordId);

    Optional<ModuleSizeRecord> findLatestApprovalPassed(EntityType entityType, Long entityId, Long appId, Long moduleId, String moduleVersion);

    Optional<PackageSizeHistoryRecord> findLatestApprovalPassedFromOld(EntityType entityType, Long entityId, Long appId, Long moduleId, String moduleVersion);

    void removeByAlterSheetIdAndModuleIds(Long alterSheetId, List<Long> moduleIds);
}
