package com.alibaba.emas.mtl4.services.dev.git.model;



/**
 * <AUTHOR>
 * @Date 2021-06-30
 */
public enum GitOperationType {

    /**
     * 拉取release分支
     */
    CREATE_RELEASE("创建release分支"),

    /**
     * 变更单模块分支自动切换成release分支
     */
    AUTO_SWITCH_TO_RELEASE("变更单模块分支自动切换成release分支"),

    /**
     * 合并release分支到集成分支
     */
    MERGE_RELEASE_INTO_INTEGRATION("合并release分支到集成分支"),

    /**
     * 校验集成分支是否包含了release分支的代码
     */
    VALIDATE_INTEGRATION_INCLUDE_RELEASE("校验集成分支是否包含了release分支的代码"),

    /**
     * 合并集成分支到release分支
     */
    MERGE_INTEGRATION_INTO_RELEASE("合并集成分支到release分支"),

    /**
     * 合并动态代码到集成分支
     */
    MERGE_DYNAMIC_INTO_INTEGRATION("合并动态发布代码到集成分支"),


    /**
     * 从持续发布分支创建feature
     */
    CREATE_DYNAMIC_FROM_PUBLISH("从持续发布分支创建feature"),


    /**
     * 合并feature代码到动态化发布分支
     */
    MERGE_DYNAMIC_INTO_PUBLISH("合并feature代码到动态化发布分支"),

    /**
     * 合并feature代码到动态化发布分支(跟版合并)
     */
    MERGE_FEATURE_INTO_INTEGRATION("合并feature代码到集成分支");

    private String description;

    GitOperationType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
