package com.alibaba.emas.mtl4.services.dev.versionPlan.model;

import com.google.common.base.Preconditions;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Calendar;
import java.util.Date;

import com.alibaba.motu.utils.DateUtils;

import static com.alibaba.motu.utils.DateUtils.COMPACT_DAY_PATTERN;
import static com.alibaba.motu.utils.DateUtils.DEFAULT_DAY_PATTERN;
import static com.alibaba.motu.utils.DateUtils.HH_MM;

/**
 * <AUTHOR>
 * @Date 2021-06-19
 */
@Data
public class VersionPlanTimeNodeBO {

    private Long id;

    private String type;

    private Date startTime;

    private Date startHour;

    private Date endTime;

    private Date endHour;

    private Boolean disabled;

    private Boolean showInMonth;

    private Boolean showInYear;

    /**
     * 版本计划时间节点阶段
     * 研发阶段：DEVELOP
     * 集成阶段：INTEGRATE
     * 发布阶段：RELEASE
     * 未定义阶段：UNDEFINED
     */
    private String stage;

    /**
     * 备注
     */
    private String remark;

    /**
     * 颜色
     */
    private String color;

    /**
     * 阶段序列，非表字段
     */
    private Integer stageIndex;

    private Long versionPlanId;

    private Date gmtCreated;

    private Date gmtModified;

    private String creator;

    private String modifier;

    public String fetchStartTimeDay() {
        return DateUtils.format(startTime, DEFAULT_DAY_PATTERN);
    }

    public String fetchStartTimeHour() {
        return DateUtils.format(startHour, HH_MM);
    }

    public String fetchContent() {
        try {
            TimeNodeType timeNodeType = TimeNodeType.valueOf(type);
            return timeNodeType.getDescription();
        } catch (Exception e) {
            return type;
        }
    }

    public Date fetchRealStartTime() {
        return fetchRealTime(startTime, startHour);
    }

    public Date fetchRealEndTime() {
        return fetchRealTime(endTime, endHour);
    }

    private Date fetchRealTime(Date time, Date hour) {
        Preconditions.checkNotNull(time);
        Calendar startTimeCalendar = Calendar.getInstance();
        startTimeCalendar.setTime(time);
        int year = startTimeCalendar.get(Calendar.YEAR);
        int dayOfYear = startTimeCalendar.get(Calendar.DAY_OF_YEAR);
        int hourOfDay = 0;
        int minute = 0;
        if (null != hour) {
            Calendar startHourCalendar = Calendar.getInstance();
            startHourCalendar.setTime(hour);
            hourOfDay = startHourCalendar.get(Calendar.HOUR_OF_DAY);
            minute = startHourCalendar.get(Calendar.MINUTE);
        }

        Calendar realStartTimeCalendar = Calendar.getInstance();
        realStartTimeCalendar.set(Calendar.YEAR, year);
        realStartTimeCalendar.set(Calendar.DAY_OF_YEAR, dayOfYear);
        realStartTimeCalendar.set(Calendar.HOUR_OF_DAY, hourOfDay);
        realStartTimeCalendar.set(Calendar.MINUTE, minute);
        realStartTimeCalendar.set(Calendar.SECOND, 0);

        return realStartTimeCalendar.getTime();
    }

    public String getStage() {
        if (StringUtils.isBlank(stage)) {
            return VersionPlanTimeNodeStage.UNCATEGORIZED.name();
        }
        return stage;
    }
}
