package com.alibaba.emas.mtl4.services.dev.notify.domain;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.alibaba.emas.mtl4.commons.utils.JsonConverter;
import com.alibaba.emas.mtl4.services.dev.notify.MessageNotifyTempPlaceholder;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Entity
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "emas_mtl4_message_notify_template")
public class MessageNotifyTemplateDO {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "gmt_create")
    private Date gmtCreate;

    @Column(name = "gmt_modified")
    private Date gmtModified;

    @Column(name = "creator")
    private String creator;

    @Column(name = "modifier")
    private String modifier;

    @Column(name = "name")
    private String name;

    @Column(name = "notify_title")
    private String notifyTitle;

    @Column(name = "html_content")
    private String htmlContent;

    @Column(name = "markdown_content")
    private String markdownContent;

    @Column(name = "mail_tos")
    @Convert(converter = JsonConverter.ListConverter.class)
    private List<String> mailTos;

    @Column(name = "mail_ccs")
    @Convert(converter = JsonConverter.ListConverter.class)
    private List<String> mailCcs;

    @Column(name = "dd_users")
    @Convert(converter = JsonConverter.ListConverter.class)
    private List<String> ddUsers;

    @Column(name = "dd_groups")
    @Convert(converter = JsonConverter.ListConverter.class)
    private List<String> ddGroups;

    @Column(name = "jump_url")
    private String jumpUrl;

    @Column(name = "placeholders")
    @Convert(converter = JsonConverter.ListConverter.class)
    private List<MessageNotifyTempPlaceholder> placeholders;
}
