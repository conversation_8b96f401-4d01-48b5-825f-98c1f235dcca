package com.alibaba.emas.mtl4.services.dev.dashboard.version;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: qiulibin
 * @Date: 2020/7/3 6:15 PM
 * 说明：包信息
 */
@Data
public class DashboardVersionDataDetail {

    /**
     * 文件信息
     */
    private List<DashboardVersionDataDetailFile> fileInfos;

    /**
     * 跳转到变更单页面（如果是模块的话）
     */
    private Long alterSheetId;
    /**
     * 模块的版本号
     */
    private String moduleVersion;

    /**
     * 跳转到渠道打包页面
     */
    private Long releaseId;

    /**
     * 渠道号
     */
    private String channelNum;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 应用ID
     */
    private Long applicationId;

    /**
     * 添加文件信息
     * @param fileName
     * @param downloadUrl
     * @param md5
     */
    public void addFileInfo(String fileName, String downloadUrl, String md5, String qrCodeUrl){
        if (fileInfos == null){
            fileInfos = new ArrayList<>();
        }
        DashboardVersionDataDetailFile file = new DashboardVersionDataDetailFile();
        file.setDownloadUrl(downloadUrl);
        file.setFileName(fileName);
        file.setMd5(md5);
        file.setQrCodeUrl(qrCodeUrl);
        addFileInfo(file);
    }

    public void addFileInfo(DashboardVersionDataDetailFile file) {
        if (fileInfos == null){
            fileInfos = new ArrayList<>();
        }
        fileInfos.add(file);
    }
}
