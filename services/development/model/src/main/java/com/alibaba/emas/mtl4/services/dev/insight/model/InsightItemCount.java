package com.alibaba.emas.mtl4.services.dev.insight.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InsightItemCount {

    private Long allCount;

    private Long undoCount;

    private Long doneCount;

    public void setExtraCount(InsightItemCount extraItemCount) {
        synchronized (this) {
            if(Objects.isNull(extraItemCount)){
                return;
            }

            if(Objects.nonNull(extraItemCount.getUndoCount())){
                undoCount = undoCount + extraItemCount.getUndoCount();
            }

            if(Objects.nonNull(extraItemCount.getDoneCount())){
                doneCount = doneCount + extraItemCount.getDoneCount();
            }

            if(Objects.nonNull(extraItemCount.getAllCount())){
                allCount = allCount + extraItemCount.getAllCount();
            }
        }
    }
}
