package com.alibaba.emas.mtl4.services.dev.git.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 分支活动详情
 * <AUTHOR>
 */
@Data
@Builder
public class ModuleGitActionDetail extends ModuleGitOperationBO {

    private Integer order;

    private Boolean main;

    private String branchName;

    private Long applicationId;

    private String applicationName;

    private String moduleName;

    private String label;

    private String desc;

    private List<ModuleGitAction> moduleGitActionList;

}
