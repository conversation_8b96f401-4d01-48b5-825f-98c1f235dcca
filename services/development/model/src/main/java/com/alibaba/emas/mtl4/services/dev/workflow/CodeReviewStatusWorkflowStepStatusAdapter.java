package com.alibaba.emas.mtl4.services.dev.workflow;

import com.alibaba.emas.mtl4.commons.utils.Pair;
import com.alibaba.emas.mtl4.services.dev.code.model.CodeReviewStatus;
import com.alibaba.emas.mtl4.services.flow.model.work.status.WorkflowStepStatus;
import com.alibaba.emas.mtl4.services.flow.model.work.step.WorkflowStep;
import com.alibaba.emas.mtl4.services.flow.model.work.step.WorkflowStepStatusAdapter;

import org.springframework.stereotype.Component;

@Component
public class CodeReviewStatusWorkflowStepStatusAdapter implements WorkflowStepStatusAdapter {

    @Override
    public Pair<WorkflowStepStatus, String> adapt(WorkflowStep step, String value) {
        CodeReviewStatus status = CodeReviewStatus.valueOf(value);
        switch (status) {
            case APPROVED:
                return new Pair<>(WorkflowStepStatus.EXECUTED_COMPLETED, "评审通过") ;
            case CLOSED:
                return new Pair<>(WorkflowStepStatus.EXECUTED_ERROR, "评审不通过");
            case IN_APPROVAL:
                return new Pair<>(WorkflowStepStatus.EXECUTING, "评审中") ;
            default:
                return null;
        }
    }

    @Override
    public String getListenerName() {
        return CodeReviewStatusWorkflowStepStatusAdapter.class.getSimpleName();
    }
}
