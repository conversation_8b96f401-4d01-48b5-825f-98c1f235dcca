package com.alibaba.emas.mtl4.services.dev.collaboration.domain;

import com.alibaba.emas.mtl4.services.dev.api.enums.SpaceBelongEnum;
import com.alibaba.emas.mtl4.services.dev.api.enums.SpaceType;
import com.alibaba.emas.mtl4.services.dev.api.model.SpaceStatus;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * 研发协作空间
 * <AUTHOR>
 * @Date 2022-05-23
 */
@Data
@Entity
@Table(name = "emas_mtl4_collaboration_space")
public class CollaborationSpaceDO {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "autoIdGenerator")
    @GenericGenerator(name = "autoIdGenerator", strategy = "com.alibaba.emas.mtl4.services.dev.ManualIdInsertGenerator")
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "description")
    private String description;

    @Column(name = "gmt_create")
    private Date gmtCreate;

    @Column(name = "gmt_modified")
    private Date gmtModified;

    @Column(name = "creator")
    private String creator;

    @Column(name = "modifier")
    private String modifier;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private SpaceType type;

    @Column(name = "belong")
    @Enumerated(EnumType.STRING)
    private SpaceBelongEnum belong;

    @Column(name = "biz_code")
    private String bizCode;

    @Column(name = "app_id")
    private Long appId;

    @Column(name = "icon")
    private String icon;

    @Column(name = "visible")
    private Boolean visible;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private SpaceStatus status;
}
