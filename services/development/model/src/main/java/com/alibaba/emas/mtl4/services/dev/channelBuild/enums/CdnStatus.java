package com.alibaba.emas.mtl4.services.dev.channelBuild.enums;

import java.util.Arrays;
import java.util.List;

/**
 * @Author：xinglv
 * @Date：2019/7/10
 */
public enum CdnStatus {

    WAIT_PUBLISH("待上传"),

    NO_VERIFY("待验证"),

    VERIFY_SUCCESS("验证成功"),

    VERIFY_NO_EXIST("文件不存在"),

    VERIFY_FAIL("验证失败"),

    VERIFY_MD5_NOT_EQUAL("MD5码不等");

    private String description;

    CdnStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static List<CdnStatus> NEED_VERIFY_STATUS = Arrays.asList(NO_VERIFY, VERIFY_NO_EXIST, VERIFY_MD5_NOT_EQUAL);




}
