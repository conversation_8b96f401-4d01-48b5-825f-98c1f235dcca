package com.alibaba.emas.mtl4.services.dev.channelBuild.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum AppChannelResourcePublishStrategy {
    /**
     * 全网发布
     */
    GLOBAL_PUBLISH(1, "全网发布"),

    /**
     * 分批发布
     */
    PHASED_PUBLISH(3, "分批发布"),

    /**
     * 邀请测试
     */
    INVITE_TEST(5, "邀请测试"),

    /**
     * 公开测试
     */
    PUBLIC_TEST(6, "公开测试");


    private Integer code;
    private String desc;

    AppChannelResourcePublishStrategy(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AppChannelResourcePublishStrategy getByName(String name) {
        for (AppChannelResourcePublishStrategy strategy : values()) {
            if (strategy.name().equals(name)) {
                return strategy;
            }
        }
        return null;
    }

}
