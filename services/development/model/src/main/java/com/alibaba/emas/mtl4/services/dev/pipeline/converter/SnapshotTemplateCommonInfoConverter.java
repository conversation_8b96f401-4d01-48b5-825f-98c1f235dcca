package com.alibaba.emas.mtl4.services.dev.pipeline.converter;

import com.alibaba.emas.mtl4.core.message.job.JobType;
import com.alibaba.emas.mtl4.core.message.job.PipelineJob;
import com.alibaba.emas.mtl4.core.message.task.PipelineTask;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.JobTemplateType;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineNodeExtendedConstant;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.PipelineStage;
import com.alibaba.emas.mtl4.services.dev.pipeline.model.template.*;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 简单流水线相关的converter
 *
 * <AUTHOR>
 * @date 2022/11/29
 */
@Slf4j
@UtilityClass
public class SnapshotTemplateCommonInfoConverter {

    /**
     * 对于插件模版参数 ${jobTemplate_xx_yyyy} 在生成 pipeline、task 时会被动态替换成 otherJob_xx_yyy，详见 https://yuque.antfin.com/mtl/iy59mp/sg1sk1?
     */
    private static final Pattern JOB_TEMPLATE_PLUGIN_CONFIG_PATTERN = Pattern.compile("\\$\\{jobTemplate_[1-9][0-9]*_.*");

    private static final Pattern JOB_SNAPSHOT_PLUGIN_CONFIG_PATTERN = Pattern.compile("\\{(otherJob_[^}]*)}");

    public static SnapshotTemplateTaskCommonInfo toSimplePipelineTask(PipelineTaskTemplate pipelineTaskTemplate) {
        SnapshotTemplateTaskCommonInfo simplePipelineTask = new SnapshotTemplateTaskCommonInfo();
        BeanUtils.copyProperties(pipelineTaskTemplate, simplePipelineTask);
        // 看 bean 里面的注释吧，为了兼容 pipeline_task 里面的逻辑
        simplePipelineTask.setPluginId(null);
        // 删除 pluginConfigs 里面的动态参数
        simplePipelineTask.getPluginConfigs()
                .entrySet()
                .removeIf(stringObjectEntry -> {
                    String configValue = Objects.toString(stringObjectEntry.getValue());
                    return JOB_TEMPLATE_PLUGIN_CONFIG_PATTERN.matcher(configValue).find();
                });
        return simplePipelineTask;
    }

    public static SnapshotTemplateTaskCommonInfo toSimplePipelineTask(PipelineTask pipelineTask) {
        SnapshotTemplateTaskCommonInfo simplePipelineTask = new SnapshotTemplateTaskCommonInfo();
        BeanUtils.copyProperties(pipelineTask, simplePipelineTask);
        simplePipelineTask.setPluginVersionId(pipelineTask.getPluginId());
        // 看 bean 里面的注释吧，pipeline_task 里面的 pluginId 其实是 pluginVersionId
        simplePipelineTask.setPluginId(null);
        simplePipelineTask.getPluginConfigs()
                .entrySet()
                .removeIf(stringObjectEntry -> {
                    String configValue = Objects.toString(stringObjectEntry.getValue());
                    return JOB_SNAPSHOT_PLUGIN_CONFIG_PATTERN.matcher(configValue).find();
                });
        return simplePipelineTask;
    }

    public static SnapshotTemplateJobCommonInfo toSimplePipelineJob(PipelineJobTemplate pipelineJobTemplate) {
        if (Objects.isNull(pipelineJobTemplate)) {
            return null;
        }
        SnapshotTemplateJobCommonInfo simplePipelineJob = new SnapshotTemplateJobCommonInfo();
        BeanUtils.copyProperties(pipelineJobTemplate, simplePipelineJob);
        simplePipelineJob.setJobType(JobTemplateType.BUILD_CONFIG.equals(pipelineJobTemplate.getJobTemplateType()) ?
                JobType.NORMAL : JobType.GATE);
        List<SnapshotTemplateTaskCommonInfo> taskList = pipelineJobTemplate.getTaskTemplates()
                .stream()
                .map(SnapshotTemplateCommonInfoConverter::toSimplePipelineTask)
                .collect(Collectors.toList());
        simplePipelineJob.setTaskList(taskList);
        simplePipelineJob.setLastModifiedTime(pipelineJobTemplate.getGmtModified() != null ?
                pipelineJobTemplate.getGmtModified().getTime() : pipelineJobTemplate.getGmtCreate().getTime());
        return simplePipelineJob;
    }

    public static SnapshotTemplateJobCommonInfo toSimplePipelineJob(PipelineJob pipelineJob) {
        if (Objects.isNull(pipelineJob)) {
            return null;
        }
        SnapshotTemplateJobCommonInfo simplePipelineJob = new SnapshotTemplateJobCommonInfo();
        BeanUtils.copyProperties(pipelineJob, simplePipelineJob);
        List<SnapshotTemplateTaskCommonInfo> taskList = pipelineJob.getTasks()
                .stream()
                .map(SnapshotTemplateCommonInfoConverter::toSimplePipelineTask)
                .collect(Collectors.toList());
        simplePipelineJob.setTaskList(taskList);
        simplePipelineJob.setLastModifiedTime(pipelineJob.getModifiedTime() != null ?
                pipelineJob.getModifiedTime() : pipelineJob.getCreateTime());
        return simplePipelineJob;
    }

    public static SnapshotTemplateStageCommonInfo toSimplePipelineStage(PipelineStageTemplate pipelineStageTemplate) {
        SnapshotTemplateStageCommonInfo simplePipelineStage = new SnapshotTemplateStageCommonInfo();
        simplePipelineStage.setName(pipelineStageTemplate.getName());
        simplePipelineStage.setBuildJob(toSimplePipelineJob(pipelineStageTemplate.getBuildJobTemplate()));
        simplePipelineStage.setPreGateJob(toSimplePipelineJob(pipelineStageTemplate.getPreGateJobTemplate()));
        simplePipelineStage.setPostGateJob(toSimplePipelineJob(pipelineStageTemplate.getPostGateJobTemplate()));
        return simplePipelineStage;
    }

    public static SnapshotTemplateStageCommonInfo toSimplePipelineStage(PipelineStageTemplate pipelineStageTemplate,
                                                                        String stageName) {
        SnapshotTemplateStageCommonInfo simplePipelineStage = toSimplePipelineStage(pipelineStageTemplate);
        simplePipelineStage.setName(stageName);
        return simplePipelineStage;
    }

    public static SnapshotTemplateStageCommonInfo toSimplePipelineStage(PipelineStage pipelineStage) {
        SnapshotTemplateStageCommonInfo simplePipelineStage = new SnapshotTemplateStageCommonInfo();
        simplePipelineStage.setName(pipelineStage.getName());
        pipelineStage.getJobs()
                .forEach(pipelineJob -> {
                    // 普通的构建配置
                    if (Objects.equals(JobType.NORMAL, pipelineJob.getJobType())) {
                        simplePipelineStage.setBuildJob(toSimplePipelineJob(pipelineJob));
                        return;
                    }
                    // 后置卡口
                    if (StringUtils.equals(pipelineJob.getExtendInfo(PipelineNodeExtendedConstant.GATE_TYPE),
                            PipelineNodeExtendedConstant.GATE_TYPE_POST_VALUE)) {
                        simplePipelineStage.setPostGateJob(toSimplePipelineJob(pipelineJob));
                        return;
                    }
                    // 前置卡口
                    simplePipelineStage.setPreGateJob(toSimplePipelineJob(pipelineJob));
                });
        return simplePipelineStage;
    }

}
