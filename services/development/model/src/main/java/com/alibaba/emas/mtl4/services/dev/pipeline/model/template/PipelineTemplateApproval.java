package com.alibaba.emas.mtl4.services.dev.pipeline.model.template;

import java.util.Date;
import java.util.List;

import com.alibaba.emas.mtl4.core.bpms.model.BpmsApprovalStatus;

import lombok.Getter;
import lombok.Setter;

/**
 * 流水线模板的审批记录
 *
 * <AUTHOR>
 * @date 2020/6/5
 */

@Getter
@Setter
public class PipelineTemplateApproval {
    /**
     * 流水线模板id
     */
    private Long id;

    /**
     * 产品id
     */
    private Long applicationId;

    /**
     * 流水线场景
     */
    private PipelineSceneType sceneType;

    /**
     * 流水线阶段模板
     */
    private List<PipelineStageTemplate> stageTemplates;

    /**
     * 修改记录版本id
     */
    private Long pipelineTemplateId;

    /**
     * 审批类型
     */
    private PipelineApprovalType approvalType;

    /**
     * 审批状态
     */
    private BpmsApprovalStatus approvalStatus;

    /**
     * 审批流实例id
     */
    private String bpmsInstanceId;

    /**
     * 审批流url
     */
    private String bpmsUrl;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 创建人
     */
    private String creator;


}
