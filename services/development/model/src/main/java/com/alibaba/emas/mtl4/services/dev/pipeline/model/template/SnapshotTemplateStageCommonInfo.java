package com.alibaba.emas.mtl4.services.dev.pipeline.model.template;

import com.alibaba.emas.mtl4.commons.utils.BeanUtils;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 简化的流水线stage
 *
 * <AUTHOR>
 * @date 2022/11/29
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Accessors(chain = true)
public class SnapshotTemplateStageCommonInfo {

    /**
     * 阶段名.
     */
    private String name;

    /**
     * 构建任务 job.
     */
    private SnapshotTemplateJobCommonInfo buildJob;

    /**
     * 前置卡口任务.
     */
    private SnapshotTemplateJobCommonInfo preGateJob;

    /**
     * 后置卡口任务.
     */
    private SnapshotTemplateJobCommonInfo postGateJob;

}
