package com.alibaba.emas.mtl4.services.dev.dependency.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-02-05
 */
@Data
public class TargetDefinition {

    @JsonProperty("Name")
    private String name;

    @JsonProperty("Project")
    private String project;

    @JsonProperty("Platform")
    private TargetDefinitionPlatform platform;

    @JsonProperty("Configurations")
    private List<String> configurations;

    @JsonProperty("Subspecs")
    private List<String> subspecs;

    @JsonProperty("Monorepo")
    private List<String> monorepo;
}
