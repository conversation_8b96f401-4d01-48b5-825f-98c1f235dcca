package com.alibaba.emas.mtl4.services.dev.cert.domain;

import com.alibaba.emas.mtl4.services.dev.cert.model.FileUsage;
import com.alibaba.emas.mtl4.services.dev.cert.model.IOSCertType;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

/**
 * created by qingli.hwz on 2019-07-25
 */
@Data
@Entity
@Table(name = "emas_mtl4_ios_cert")
@EntityListeners(AuditingEntityListener.class)
public class IOSCertDO {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "manualId")
    @GenericGenerator(name = "manualId", strategy = "com.alibaba.emas.mtl4.services.dev.ManualIdInsertGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private IOSCertType type;

    /**
     * 旧版本cert路径，新版请勿使用
     */
    @Deprecated
    @Column(name = "path")
    private String path;

    @Column(name = "full_name")
    private String fullName;

    /**
     * 旧版本cert密码，新版请勿使用
     */
    @Deprecated
    @Column(name = "password")
    private String password;

    @Column(name = "expiration_time")
    private Date expirationTime;

    @Column(name = "file_usage")
    @Enumerated(EnumType.STRING)
    private FileUsage fileUsage;

    @Column(name = "team_id")
    private String teamId;

    /**
     * oss路径
     */
    @Column(name = "file_url")
    private String fileUrl;

    /**
     * 证书md5
     */
    @Column(name = "file_md5")
    private String fileMd5;

    /**
     * 证书维护人
     */
    @Column(name = "owner")
    private String owner;

    /**
     * 加密过的p12证书密码
     */
    @Column(name = "p12_password")
    private String p12Password;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    private Boolean isDeleted;

    @CreatedBy
    @Column(name = "creator")
    private String creator;

    @LastModifiedBy
    @Column(name = "modifier")
    private String modifier;

    @CreatedDate
    @Column(name = "gmt_create")
    private Date gmtCreate;

    @LastModifiedDate
    @Column(name = "gmt_modified")
    private Date gmtModified;

}