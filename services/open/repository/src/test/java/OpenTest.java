import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.emas.mtl4.services.open.model.provider.api.HttpOpenApi;

import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

public class OpenTest {
    @Test
    public void keyGenTest() throws Exception {
        SecureRandom random = new SecureRandom();
        Base64.Encoder base64Encoder = Base64.getUrlEncoder();

        byte bytes[] = new byte[20];
        random.nextBytes(bytes);
        System.out.println(base64Encoder.encodeToString(bytes));
    }

    @Test
    public void pathTest() {
        String pathVariablePath = "/dev/api/v1/pipeline/executes/{pipelineInstanceId}/summary";
        String path = "/dev/api/v1/pipeline/executes/123/summary";
        PathMatcher matcher =  new AntPathMatcher();
        System.out.println(matcher.extractUriTemplateVariables(pathVariablePath, path));
    }
}
