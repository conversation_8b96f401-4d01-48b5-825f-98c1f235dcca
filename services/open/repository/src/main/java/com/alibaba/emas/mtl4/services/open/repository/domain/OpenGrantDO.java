package com.alibaba.emas.mtl4.services.open.repository.domain;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "emas_mtl4_open_grant")
@EntityListeners(AuditingEntityListener.class)
public class OpenGrantDO {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "gmt_create")
    @CreatedDate
    private Date gmtCreate;

    @Column(name = "creator")
    private String creator;

    @Column(name = "consumer_id")
    private Long consumerId;

    @Column(name = "client_id")
    private Long clientId;
}
