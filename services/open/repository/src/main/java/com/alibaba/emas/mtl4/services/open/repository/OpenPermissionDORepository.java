package com.alibaba.emas.mtl4.services.open.repository;

import java.util.List;
import java.util.Optional;

import com.alibaba.emas.mtl4.services.open.model.provider.api.OpenPermissionResourceType;
import com.alibaba.emas.mtl4.services.open.repository.domain.OpenPermissionDO;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface OpenPermissionDORepository extends JpaRepository<OpenPermissionDO, Long>,
        JpaSpecificationExecutor<OpenPermissionDO> {
    List<OpenPermissionDO> findAllByConsumerId(Long consumerId);

    List<OpenPermissionDO> findAllByConsumerIdAndResourceType(Long consumerId, OpenPermissionResourceType type);

    Optional<OpenPermissionDO> findByConsumerIdAndResourceTypeAndResourceValue(Long consumerId, OpenPermissionResourceType type, String value);

    List<OpenPermissionDO> findAllByResourceTypeAndResourceValue(OpenPermissionResourceType type, String value);
}
