package com.alibaba.emas.mtl4.services.open.gateway.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import com.alibaba.emas.mtl4.acl.commons.annotation.ReturnType;
import com.alibaba.emas.mtl4.acl.commons.annotation.resource.MTLACLCreateOrUpdateResource;
import com.alibaba.emas.mtl4.acl.commons.annotation.resource.MTLACLMetaChangeListener;
import com.alibaba.emas.mtl4.acl.commons.model.Resource;
import com.alibaba.emas.mtl4.acl.commons.model.role.Action;
import com.alibaba.emas.mtl4.acl.commons.model.role.Role;
import com.alibaba.emas.mtl4.boot.starter.acl.meta.MetaChangeListener;
import com.alibaba.emas.mtl4.core.bpms.model.BpmsApprovalStatus;
import com.alibaba.emas.mtl4.core.bpms.service.BpmsInstanceService;
import com.alibaba.emas.mtl4.services.dev.api.annotation.SaveUserRole;
import com.alibaba.emas.mtl4.services.dev.api.enums.EntityType;
import com.alibaba.emas.mtl4.services.dev.api.model.UserRole;
import com.alibaba.emas.mtl4.services.dev.api.service.UserRoleService;
import com.alibaba.emas.mtl4.services.open.exception.BizException;
import com.alibaba.emas.mtl4.services.open.exception.OpenErrorCode;
import com.alibaba.emas.mtl4.services.open.gateway.OpenClientGateway;
import com.alibaba.emas.mtl4.services.open.gateway.OpenConsumerGateway;
import com.alibaba.emas.mtl4.services.open.gateway.mapper.OpenClientMapper;
import com.alibaba.emas.mtl4.services.open.model.client.OpenClient;
import com.alibaba.emas.mtl4.services.open.model.client.OpenClientSingleQuery;
import com.alibaba.emas.mtl4.services.open.model.consumer.OpenConsumer;
import com.alibaba.emas.mtl4.services.open.model.consumer.grant.OpenGrant;
import com.alibaba.emas.mtl4.services.open.model.consumer.permisson.OpenPermission;
import com.alibaba.emas.mtl4.services.open.repository.OpenClientDORepository;
import com.alibaba.emas.mtl4.services.open.repository.domain.OpenClientDO;
import com.alibaba.fastjson.JSON;

import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import static com.alibaba.emas.mtl4.core.bpms.model.BpmsRelatedType.OPEN_CLIENT;

/**
 * <AUTHOR>
 * @date 2022/4/20
 */
@Slf4j
@Service
@MTLACLMetaChangeListener(resource = Resource.OPEN_CLIENT)
public class OpenClientGatewayImpl implements OpenClientGateway, MetaChangeListener<OpenClient> {

    @Value("${mtl4.dev.env.bpms}")
    private String envBpmsUrl;

    @Autowired
    private OpenClientDORepository openClientDORepository;

    @Autowired
    private OpenConsumerGateway openConsumerGateway;

    @Autowired
    private BpmsInstanceService bpmsInstanceService;

    @Autowired
    private UserRoleService userRoleService;

    @Override
    public Optional<OpenClient> findById(Long id) {
        return openClientDORepository
            .findById(id)
            .map(OpenClientMapper.INSTANCE::toBO)
            .map(this::complete);
    }

    @Override
    public Optional<OpenClient> find(OpenClientSingleQuery singleQuery) {
        return openClientDORepository.findOne(toSpecification(singleQuery))
            .map(OpenClientMapper.INSTANCE::toBO)
            .map(this::complete);
    }

    @Override
    @SaveUserRole
    @MTLACLCreateOrUpdateResource(returnType = ReturnType.PRIMITIVE)
    public Long add(OpenClient client) {
        Preconditions.checkArgument(client.getId() == null, "Client已注册");

        if (null == client.getIsEnable()) {
            client.setIsEnable(true);
        }
        client.setAdmin(client.getCreator());

        OpenClientDO clientDO = OpenClientMapper.INSTANCE.toDO(client);
        openClientDORepository.save(clientDO);
        client.setId(clientDO.getId());

        return clientDO.getId();
    }

    @Override
    @SaveUserRole
    @MTLACLCreateOrUpdateResource(returnType = ReturnType.PRIMITIVE)
    public Long update(OpenClient client) {
        Preconditions.checkNotNull(client.getId());

        OpenClientDO clientDO = OpenClientMapper.INSTANCE.toDO(client);
        openClientDORepository.save(clientDO);

        return client.getId();
    }

    @Override
    public void update(Long id, Boolean enable) {
        Preconditions.checkNotNull(id);
        openClientDORepository.findById(id).ifPresent(openClientDO -> {
            openClientDO.setIsEnable(enable);
            openClientDORepository.save(openClientDO);
        });
    }

    @Override
    public void delete(Long clientId) {
        Preconditions.checkNotNull(clientId);
        List<OpenGrant> openGrants = openConsumerGateway.getGrantConsumers(clientId);
        if (CollectionUtils.isNotEmpty(openGrants)) {
            OpenConsumer openConsumer = openConsumerGateway.findById(openGrants.get(0).getConsumerId())
                .orElseThrow(() -> new BizException(OpenErrorCode.CONSUMER_NOT_EXIST));
            throw new RuntimeException(String.format("%s仍在关联该Client，请删除相应关联关系后重试删除该Client",
                openConsumer.getIdentifier()));
        }
        openClientDORepository.deleteById(clientId);
    }

    @Override
    public List<OpenClient> findAll() {
        return openClientDORepository.findAll()
            .parallelStream()
            .map(openClientDO -> {
                OpenClient openClient = OpenClientMapper.INSTANCE.toBO(openClientDO);

                Long consumerId = openConsumerGateway.findIdByIdentifier(openClient.getIdentifier()).get();
                List<OpenPermission> openPermissions = openConsumerGateway.getPermissions(consumerId);
                openPermissions.parallelStream().forEach(openConsumerGateway::completePermissionLabel);
                openClient.setConsumerId(consumerId);
                openClient.setOpenPermissions(openPermissions);
                //未打开的Client，查出相应的申请链接
                if (!openClient.getIsEnable()) {
                    Optional.ofNullable(bpmsInstanceService.findBpmsInstance(OPEN_CLIENT, openClientDO.getId()))
                        .ifPresent(bpmsInstance -> {
                            if (BpmsApprovalStatus.IN_APPROVAL.equals(bpmsInstance.getApprovalStatus())) {
                                openClient.setProcessInstanceUrl(envBpmsUrl + bpmsInstance.getProcessInstanceId());
                            }
                        });
                }

                return openClient;
            })
            .map(this::complete)
            .collect(Collectors.toList());
    }

    @Override
    public OpenClient getMetaById(Long id) {
        return this.findById(id).orElse(null);
    }

    @Override
    public List<OpenClient> getAllMeta() {
        return this.findAll();
    }

    @Override
    public void changeRole(Object object, Long resourceId, Action action, String empId, Role role) throws Exception {
        try {
            log.info(">>>changeRole:{}", JSON.toJSONString(object));
        }catch (Exception e) {
            log.error("changeRole异常", e);
        }
        userRoleService.saveUserRole(object);
    }

    private Specification<OpenClientDO> toSpecification(OpenClientSingleQuery query) {
        return (Root<OpenClientDO> root, CriteriaQuery<?> criteriaQuery,
                CriteriaBuilder criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (null != query.getIdentifier()) {
                predicates.add(criteriaBuilder.equal(root.get("identifier"), query.getIdentifier()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    private OpenClient complete(OpenClient openClient) {
        UserRole userRole = userRoleService.findUserRole(EntityType.OPEN_CLIENT, openClient.getId(),
            com.alibaba.emas.mtl4.services.dev.api.enums.Role.ADMIN);
        if (userRole != null) {
            openClient.setAdmin(userRole.getUserList());
        }
        return openClient;
    }
}
