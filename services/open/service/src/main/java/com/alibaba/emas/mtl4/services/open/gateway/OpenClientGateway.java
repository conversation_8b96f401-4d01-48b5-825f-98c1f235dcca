package com.alibaba.emas.mtl4.services.open.gateway;

import java.util.List;
import java.util.Optional;

import com.alibaba.emas.mtl4.services.open.model.client.OpenClient;
import com.alibaba.emas.mtl4.services.open.model.client.OpenClientSingleQuery;

import org.aspectj.apache.bcel.classfile.Module.Open;

/**
 * <AUTHOR>
 * @date 2022/4/20
 */
public interface OpenClientGateway {
    Optional<OpenClient> findById(Long id);

    /**
     * 搜索Client
     *
     * @param singleQuery 单个实体查询
     * @return 单个实体
     */
    Optional<OpenClient> find(OpenClientSingleQuery singleQuery);

    /**
     * 添加Client
     *
     * @param client 实体
     * @return ID
     */
    Long add(OpenClient client);

    Long update(OpenClient client);

    void update(Long id, Boolean enable);

    void delete(Long clientId);

    List<OpenClient> findAll();
}
