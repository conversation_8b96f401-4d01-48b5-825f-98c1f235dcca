package com.alibaba.emas.mtl4.services.open.service.callback;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.emas.mtl4.core.bpms.model.BpmsApprovalStatus;
import com.alibaba.emas.mtl4.core.bpms.model.BpmsRelatedType;
import com.alibaba.emas.mtl4.core.bpms.service.BpmsInstanceService;
import com.alibaba.emas.mtl4.services.open.gateway.OpenClientGateway;
import com.alibaba.emas.mtl4.services.open.service.OpenClientApplicationCallback;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@HSFProvider(serviceInterface = OpenClientApplicationCallback.class)
public class OpenClientApplicationCallbackImpl implements OpenClientApplicationCallback {

    @Autowired
    private OpenClientGateway openClientGateway;

    @Autowired
    private BpmsInstanceService bpmsInstanceService;

    @Override
    public void agree(Long clientId, String operator) {
        log.info(">>>agree:{}", clientId);

        openClientGateway.update(clientId, true);

        bpmsInstanceService.modifyApprovalStatus(clientId, BpmsRelatedType.OPEN_CLIENT, BpmsApprovalStatus.APPROVED, operator);
    }

    @Override
    public void disagree(Long clientId, String operator) {
        log.info(">>>disagree:{}", clientId);

        openClientGateway.delete(clientId);

        bpmsInstanceService.modifyApprovalStatus(clientId, BpmsRelatedType.OPEN_CLIENT, BpmsApprovalStatus.REJECT, operator);
    }
}
