package com.alibaba.emas.mtl4.services.open.model.provider;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/4/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OpenProviderSingleQuery {
    private Long id;
    private String appName;

    public boolean isValid() {
        return id != null || StringUtils.isNotBlank(appName);
    }
}
