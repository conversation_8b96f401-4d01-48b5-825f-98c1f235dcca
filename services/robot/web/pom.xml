<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mtl4-services-robot</artifactId>
        <groupId>com.alibaba.emas</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mtl4-services-robot-web</artifactId>
    <version>${revision}</version>
    <name>MTL4 Services :: Robot :: Web</name>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>velocity-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-boot-starter-buc</artifactId>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-services-robot-service</artifactId>
        </dependency>
    </dependencies>

</project>