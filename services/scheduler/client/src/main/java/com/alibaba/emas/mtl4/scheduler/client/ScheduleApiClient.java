package com.alibaba.emas.mtl4.scheduler.client;

import com.alibaba.emas.mtl4.commons.storage.*;
import com.alibaba.emas.mtl4.scheduler.client.model.*;

import java.util.Map;

public interface ScheduleApiClient {

    Version getVersion();

    /**
     * register
     *
     * @param registerRequest
     * @return
     */
    RegisterResult register(RegisterRequest registerRequest);

    /**
     * updateMachineInfo
     *
     * @param updateMachineInfoRequest
     */
    UpdateMachineInfoResult updateMachineInfo(UpdateMachineInfoRequest updateMachineInfoRequest);

    /**
     * reportMetrics
     *
     * @param reportMetricsRequest
     */
    ReportMetricsResult reportMetrics(ReportMetricsRequest reportMetricsRequest);


    /**
     * pushEvents
     *
     * @param pushEventsRequest
     */
    PushEventsResult pushEvents(PushEventsRequest pushEventsRequest);

    /**
     * pullJob
     *
     * @param pullJobRequest
     * @return
     */
    PullJobResult pullJob(PullJobRequest pullJobRequest);

    /**
     * requestForward
     *
     * @param requestForwardRequest
     * @return
     */
    RequestForwardResult requestForward(RequestForwardRequest requestForwardRequest);

    /**
     * applyLogUpload
     *
     * @param applyLogUploadRequest
     * @return
     */
    ApplyLogUploadResult applyLogUpload(ApplyLogUploadRequest applyLogUploadRequest);

    /**
     * applyArtifactUpload
     *
     * @param applyArtifactUploadRequest
     * @return
     */
    ApplyArtifactUploadResult applyArtifactUpload(ApplyArtifactUploadRequest applyArtifactUploadRequest);


    /**
     * artifactUploaded
     *
     * @param artifactUploadedRequest
     * @return
     */
    ArtifactUploadedResult artifactUploaded(ArtifactUploadedRequest artifactUploadedRequest);

    /**
     * jobStarted
     *
     * @param jobStartedRequest
     * @return
     */
    JobStartedResult jobStarted(JobStartedRequest jobStartedRequest);

    /**
     * jobStarted
     *
     * @param jobPreparedRequest
     * @return
     */
    JobPreparedResult jobPrepared(JobPreparedRequest jobPreparedRequest);

    /**
     * jobStarted
     *
     * @param jobRunnerStartedRequest
     * @return
     */
    JobRunnerStartedResult jobRunnerStarted(JobRunnerStartedRequest jobRunnerStartedRequest);

    /**
     * jobStarted
     *
     * @param jobRunnerFinishedRequest
     * @return
     */
    JobRunnerFinishedResult jobRunnerFinished(JobRunnerFinishedRequest jobRunnerFinishedRequest);

    /**
     * jobFinished
     *
     * @param jobFinishedRequest
     * @return
     */
    JobFinishedResult jobFinished(JobFinishedRequest jobFinishedRequest);

    /**
     * afterFinishJobStarted
     *
     * @param afterFinishJobStartedRequest
     * @return
     */
    AfterFinishJobStartedResult afterFinishJobStarted(AfterFinishJobStartedRequest afterFinishJobStartedRequest);

    /**
     * afterFinishJobFinished
     *
     * @param afterFinishJobFinishedRequest
     * @return
     */
    AfterFinishJobFinishedResult afterFinishJobFinished(AfterFinishJobFinishedRequest afterFinishJobFinishedRequest);

    /**
     * taskStarted
     *
     * @param taskStartedRequest
     * @return
     */
    TaskStartedResult taskStarted(TaskStartedRequest taskStartedRequest);

    /**
     * taskPluginDownloadStart
     *
     * @param taskPluginDownloadStartRequest
     * @return
     */
    TaskPluginDownloadStartResult taskPluginDownloadStart(
            TaskPluginDownloadStartRequest taskPluginDownloadStartRequest);

    /**
     * taskPluginDownloadFinish
     *
     * @param taskPluginDownloadFinishRequest
     * @return
     */
    TaskPluginDownloadFinishResult taskPluginDownloadFinish(
            TaskPluginDownloadFinishRequest taskPluginDownloadFinishRequest);

    /**
     * taskPluginLoadStart
     *
     * @param taskPluginLoadStartRequest
     * @return
     */
    TaskPluginLoadStartResult taskPluginLoadStart(
            TaskPluginLoadStartRequest taskPluginLoadStartRequest);

    /**
     * taskPluginLoadFinish
     *
     * @param taskPluginLoadFinishRequest
     * @return
     */
    TaskPluginLoadFinishResult taskPluginLoadFinish(
            TaskPluginLoadFinishRequest taskPluginLoadFinishRequest);

    /**
     * taskPluginInstallStart
     *
     * @param taskPluginInstallStartRequest
     * @return
     */
    TaskPluginInstallStartResult taskPluginInstallStart(
            TaskPluginInstallStartRequest taskPluginInstallStartRequest);

    /**
     * taskPluginInstallFinish
     *
     * @param taskPluginInstallFinishRequest
     * @return
     */
    TaskPluginInstallFinishResult taskPluginInstallFinish(
            TaskPluginInstallFinishRequest taskPluginInstallFinishRequest);

    /**
     * taskFinished
     *
     * @param taskFinishedRequest
     * @return
     */
    TaskFinishedResult taskFinished(TaskFinishedRequest taskFinishedRequest);


    /**
     * createUploader
     *
     * @param filename
     * @param fileMetadata
     * @param storageType
     * @param storageToken
     * @param storageObjectKey
     * @param storageObjectMetadata
     * @return
     */
    SimpleUploader createUploader(String filename, Map<String, String> fileMetadata,
                                  StorageType storageType, StorageToken storageToken,
                                  StorageObjectKey storageObjectKey, Map<String, String> storageObjectMetadata);


    /**
     * createAppendUploader
     *
     * @param filename
     * @param fileMetadata
     * @param storageType
     * @param storageToken
     * @param storageObjectKey
     * @param storageObjectMetadata
     * @return
     */
    AppendUploader createAppendUploader(String filename,
                                        Map<String, String> fileMetadata,
                                        StorageType storageType,
                                        StorageToken storageToken,
                                        StorageObjectKey storageObjectKey,
                                        Map<String, String> storageObjectMetadata,
                                        boolean deleteOnExist);

}
