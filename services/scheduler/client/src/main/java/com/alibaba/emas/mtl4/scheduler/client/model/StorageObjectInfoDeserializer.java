package com.alibaba.emas.mtl4.scheduler.client.model;


import com.alibaba.emas.mtl4.commons.storage.StorageObjectKey;
import com.alibaba.emas.mtl4.commons.storage.StorageType;
import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.commons.utils.JsonUtils;
import com.alibaba.emas.mtl4.scheduler.client.utils.StorageJsonHelper;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;
import java.util.Map;

public class StorageObjectInfoDeserializer extends JsonDeserializer<StorageObjectInfo> {

    @Override
    public StorageObjectInfo deserialize(JsonParser p, DeserializationContext ctxt)
            throws IOException, JsonProcessingException {

        JsonNode rootNode = p.getCodec().readTree(p);

        StorageObjectInfo storageObjectInfo = new StorageObjectInfo();

        StorageType storageType = null;
        JsonNode storageTypeNode = rootNode.get("storageType");
        if (null != storageTypeNode) {
            storageType = StorageJsonHelper.toStorageType(storageTypeNode);
        }

        Assert.notNull(storageType, "storageType");

        StorageObjectKey storageObjectKey = null;
        JsonNode storageObjectKeyNode = rootNode.get("storageObjectKey");
        if (null != storageObjectKeyNode) {
            storageObjectKey = StorageJsonHelper.toStorageObjectKey(storageType, storageObjectKeyNode);
        }

        Map<String, String> storageObjectMetadata = null;
        JsonNode storageObjectMetadataNode = rootNode.get("storageObjectMetadata");
        if (null != storageObjectMetadataNode) {
            storageObjectMetadata = JsonUtils.toObject(storageObjectMetadataNode, new TypeReference<Map<String, String>>() {
            });
        }

        storageObjectInfo.setStorageType(storageType);
        storageObjectInfo.setStorageObjectKey(storageObjectKey);
        storageObjectInfo.setStorageObjectMetadata(storageObjectMetadata);

        return storageObjectInfo;
    }
}
