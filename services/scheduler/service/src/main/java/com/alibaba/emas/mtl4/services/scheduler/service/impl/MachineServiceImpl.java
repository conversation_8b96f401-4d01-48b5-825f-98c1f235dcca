package com.alibaba.emas.mtl4.services.scheduler.service.impl;

import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.commons.utils.BeanUtils;
import com.alibaba.emas.mtl4.services.scheduler.domain.Machine;
import com.alibaba.emas.mtl4.services.scheduler.enums.MachineStatus;
import com.alibaba.emas.mtl4.services.scheduler.exception.BizException;
import com.alibaba.emas.mtl4.services.scheduler.exception.ErrorCode;
import com.alibaba.emas.mtl4.services.scheduler.repository.MachineRepository;
import com.alibaba.emas.mtl4.services.scheduler.service.MachineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Service
public class MachineServiceImpl implements MachineService {

    @Autowired
    private MachineRepository machineRepository;

    @Transactional
    @Override
    public Machine createMachine(Machine machine) {
        Assert.notNull(machine);
        Assert.isNull(machine.getId());
        Assert.notNull(machine.getMacAddress());

        Machine newMachine = new Machine();
        BeanUtils.copyProperties(machine, newMachine);

        if (null == newMachine.getStatus()) {
            newMachine.setStatus(MachineStatus.NORMAL);
        }

        Date now = new Date();
        newMachine.setGmtCreate(now);
        newMachine.setGmtModified(now);
        return machineRepository.save(newMachine);
    }

    @Transactional
    @Override
    public void updateMachine(Machine machine) {
        Assert.notNull(machine);
        Assert.notNull(machine.getId());

        Machine updateMachine = new Machine();
        BeanUtils.copyProperties(machine, updateMachine);
        // 不允许修改MAC地址
        if (null != updateMachine.getMacAddress()) {
            updateMachine.setMacAddress(null);
        }

        Date now = new Date();
        updateMachine.setGmtModified(now);
        machineRepository.update(updateMachine);
    }

    @Override
    public Machine getMachine(Long id) {
        Assert.notNull(id);

        Optional<Machine> machineOptional = machineRepository.findById(id);
        return machineOptional.orElseThrow(() -> {
            log.error(">>> machine id cannot be found {}", id);

            return BizException.builder()
                    .errorCode(ErrorCode.MACHINE_NOT_FOUND)
                    .errorMessage("MACHINE_NOT_FOUND")
                    .build();
        });
    }

    @Override
    public Machine getMachine(String macAddress) {
        Assert.notNull(macAddress);

        Optional<Machine> machineOptional = machineRepository.findByMacAddress(macAddress);
        return machineOptional.orElseThrow(() -> {
            log.error(">>> machine MAC address cannot be found {}", macAddress);

            return BizException.builder()
                    .errorCode(ErrorCode.MACHINE_NOT_FOUND)
                    .errorMessage("MACHINE_NOT_FOUND")
                    .build();
        });
    }

    @Override
    public Optional<Machine> findMachine(String macAddress) {
        Assert.notNull(macAddress);

        return machineRepository.findByMacAddress(macAddress);
    }

    @Override
    public List<Machine> findMachines(Set<Long> machineIds) {
        return machineRepository.findAllById(machineIds);
    }
}
