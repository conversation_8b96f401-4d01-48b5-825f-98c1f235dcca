package com.alibaba.emas.mtl4.services.scheduler.service;

import com.alibaba.emas.mtl4.log.model.TaskModelQuery;
import com.alibaba.emas.mtl4.log.model.TrainModelDTO;
import com.alibaba.emas.mtl4.log.model.TrainModelResultVO;
import com.alibaba.emas.mtl4.log.model.TrainModelVO;
import com.alibaba.emas.mtl4.services.scheduler.domain.TrainModel;
import com.alibaba.emas.mtl4.services.scheduler.query.TrainModelQuery;

import java.util.List;

/**
 * 训练模型service
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
public interface TrainModelService {

    /**
     * 上传 train model.
     * @param trainModelDTO 训练模型传输对象
     */
    void uploadTrainModel(TrainModelDTO trainModelDTO);

    /**
     * 更新训练模型数据信息.
     * @param trainModelId 训练模型id
     * @param trainModelDTO 训练模型传输数据对象
     */
    void updateTrainModelInfo(Long trainModelId, TrainModelDTO trainModelDTO);

    /**
     * 根据查询条件获取相关的训练模型列表.
     * @param trainModelQuery 训练模型查询.
     * @return 训练模型数据
     */
    List<TrainModel> getTrainModelListByQuery(TrainModelQuery trainModelQuery);

    /**
     * 根据 task instance 筛选一个最匹配的训练模型结果.
     * @param taskModelQuery 流水线 task instance 相关信息
     * @return 最相关的训练模型值对象
     */
    TrainModelResultVO getTrainModelByTask(TaskModelQuery taskModelQuery);

    /**
     * 根据identifier查找最新的模型.
     * @param identifier 模型唯一标识
     * @return 模型查询结果值对象
     */
    TrainModelVO getLatestModelByIdentifier(String identifier);

    /**
     * 获取日志 embedding 模型.
     * @param taskModelQuery 任务模型查询条件
     * @return 训练模型值对象
     */
    TrainModelVO getLogEmbeddingModel(TaskModelQuery taskModelQuery);

}
