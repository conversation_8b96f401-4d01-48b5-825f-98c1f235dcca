package com.alibaba.emas.mtl4.services.scheduler.service.impl;

import com.alibaba.emas.mtl4.core.message.job.EnvType;
import com.alibaba.emas.mtl4.core.message.job.Environment;
import com.alibaba.emas.mtl4.services.scheduler.model.AgentCondition;
import com.alibaba.emas.mtl4.services.scheduler.model.TargetCondition;
import com.alibaba.emas.mtl4.services.scheduler.service.TargetConditionRender;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

@Service
public class AgentConditionRender extends TargetConditionRender {
    @Override
    public boolean canRender(Environment env) {
        return super.canRender(env);
    }

    @Override
    public boolean isDefaultRender() {
        return true;
    }

    @Override
    public TargetCondition renderCondition(Set<Long> workgroupIds, Environment env) {
        AgentCondition agentCondition = new AgentCondition();
        agentCondition.setWorkgroupIds(workgroupIds);
        agentCondition.setOsExpr(env.getOs());
        agentCondition.setOsVersionExpr(env.getOsVersion());
//        agentCondition.setAndroidVersionExpr(env.getAndroidVersion());
        agentCondition.setXcodeVersionExpr(env.getXcodeVersion());
//        agentCondition.setJavaVersionExpr(env.getJavaVersion());
//        agentCondition.setPythonVersionExpr(env.getPythonVersion());
        agentCondition.setTags(env.getTags() != null ? new HashSet<>(env.getTags()) : null);
        agentCondition.setEnvType(env.getEnvType() != null ? env.getEnvType() : EnvType.NATIVE);
        agentCondition.setEnvYml(env.getEnvYml());
        return agentCondition;
    }
}
