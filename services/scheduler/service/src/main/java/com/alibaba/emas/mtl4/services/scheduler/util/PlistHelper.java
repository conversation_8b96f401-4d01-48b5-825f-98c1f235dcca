package com.alibaba.emas.mtl4.services.scheduler.util;

import com.alibaba.emas.mtl4.services.scheduler.domain.JobArtifact;
import com.alibaba.emas.mtl4.services.scheduler.model.ArtifactMetadata;
import com.alibaba.emas.mtl4.services.scheduler.model.FileMetadata;
import org.w3c.dom.DOMImplementation;
import org.w3c.dom.Document;
import org.w3c.dom.DocumentType;
import org.w3c.dom.Element;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;

public abstract class PlistHelper {

    private static void appendKey(Document doc, Element parentElement, String key, String value) {
        Element keyElement = doc.createElement("key");
        keyElement.setTextContent(key);
        Element valueElement = doc.createElement("string");
        valueElement.setTextContent(value);
        parentElement.appendChild(keyElement);
        parentElement.appendChild(valueElement);
    }

    private static void appendSoftwarePackage(Document doc, Element parentElement, String url) {
        Element dict = doc.createElement("dict");
        appendKey(doc, dict, "kind", "software-package");
        appendKey(doc, dict, "url", url);
        parentElement.appendChild(dict);
    }

    private static void appendSoftwareMetadata(Document doc, Element parentElement, String bundleIdentifier,
                                               String bundleVersion, String title) {
        Element metadataElement = doc.createElement("key");
        metadataElement.setTextContent("metadata");
        Element metadataDictElement = doc.createElement("dict");

        appendKey(doc, metadataDictElement, "bundle-identifier", bundleIdentifier);
        appendKey(doc, metadataDictElement, "bundle-version", bundleVersion);
        appendKey(doc, metadataDictElement, "kind", "software");
        appendKey(doc, metadataDictElement, "title", title);

        parentElement.appendChild(metadataElement);
        parentElement.appendChild(metadataDictElement);
    }

    private static void appendDownloadItem(Document doc, Element parentElement, String url,
                                           String bundleIdentifier, String bundleVersion, String title) {
        Element itemElement = doc.createElement("dict");
        Element assetsElement = doc.createElement("key");
        assetsElement.setTextContent("assets");
        Element arrayElement = doc.createElement("array");
        appendSoftwarePackage(doc, arrayElement, url);
        itemElement.appendChild(assetsElement);
        itemElement.appendChild(arrayElement);
        appendSoftwareMetadata(doc, itemElement, bundleIdentifier, bundleVersion, title);
        parentElement.appendChild(itemElement);
    }

    public static String createDownloadPlist(String url, String bundleIdentifier, String bundleVersion, String title)
            throws TransformerException, ParserConfigurationException, IOException {

        // Create Document
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        DOMImplementation di = builder.getDOMImplementation();
        DocumentType dt = di.createDocumentType("plist",
                "-//Apple//DTD PLIST 1.0//EN",
                "http://www.apple.com/DTDs/PropertyList-1.0.dtd");
        Document doc = di.createDocument("", "plist", dt);
        doc.setXmlStandalone(true);

        // Set plist version
        Element root = doc.getDocumentElement();
        root.setAttribute("version", "1.0");

        // Enter data
        // dict
        Element rootDict = doc.createElement("dict");
        root.appendChild(rootDict);
        // dict -> key("items")
        Element dictKey = doc.createElement("key");
        dictKey.setTextContent("items");
        rootDict.appendChild(dictKey);
        Element arrayElement = doc.createElement("array");
        rootDict.appendChild(arrayElement);

        appendDownloadItem(doc, arrayElement, url, bundleIdentifier, bundleVersion, title);
        // Create a transformer
        DOMSource domSource = new DOMSource(doc);
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer t = tf.newTransformer();
        t.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
        t.setOutputProperty(OutputKeys.DOCTYPE_PUBLIC, dt.getPublicId());
        t.setOutputProperty(OutputKeys.DOCTYPE_SYSTEM, dt.getSystemId());
        t.setOutputProperty(OutputKeys.INDENT, "yes");
        t.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

        try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            StreamResult streamResult = new StreamResult(output);
            t.transform(domSource, streamResult);
            return output.toString();
        }
    }

    public static String createPlistHtmlContainer(String url) {
        String content =
                "<!DOCTYPE html>" +
                        "<html lang=\"zh\">" +
                        "<header>" +
                        "    <meta name=“viewport” content=“initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no,width=device-width”/>" +
                        "    <meta charset=\"utf-8\">" +
                        "</header>" +
                        "<body style=\"background-color: #04152C\">" +
                        "<div style=\"display: flex; flex-direction: column; justify-content: center; align-items: center; height: -webkit-fill-available\">" +
                        "    <div style=\"margin: 48px;\">" +
                        "        <img src=\"https://img.alicdn.com/imgextra/i3/O1CN01pRoKgZ1H93Rie3bmf_!!6000000000714-2-tps-530-92.png\" alt=\"logo\">" +
                        "    </div>" +
                        "    <div style=\"flex: auto; display: flex; align-items: center\">" +
                        "        <img width=\"642\" height=\"548\" src=\"https://img.alicdn.com/imgextra/i3/O1CN010eNgAS25nZGjVKf4L_!!6000000007571-55-tps-321-274.svg\" alt=\"introduction\">" +
                        "    </div>" +
                        "    <div style=\"margin-bottom: 240px; display: flex; width: 100%%\">" +
                        "        <a href=\"itms-services://?action=download-manifest&url=%s\" style=\"flex: auto; margin: 0 48px\">" +
                        "            <button style=\"color: #0070CC; width: 100%%; text-align: center; line-height: 96px; font-size: 48px\">立即下载</button>" +
                        "        </a>" +
                        "    </div>" +
                        "</div>" +
                        "</body>" +
                        "</html>";
        return String.format(content, url);
    }

    public static String createPlistHtmlContainer(String url, JobArtifact jobArtifact) {
        String content =
                "<!DOCTYPE html>" +
                        "<html lang=\"zh\">" +
                        "<header>" +
                        "    <meta name=“viewport” content=“initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no,width=device-width”/>\n" +
                        "    <meta charset=\"utf-8\">" +
                        "</header>" +
                        "<body style=\"background-color: #04152C\">" +
                        "<div style=\"display: flex; flex-direction: column; justify-content: center; align-items: center; height: -webkit-fill-available\">" +
                        "    <div style=\"margin: 48px;\">" +
                        "        <img src=\"https://img.alicdn.com/imgextra/i3/O1CN01pRoKgZ1H93Rie3bmf_!!6000000000714-2-tps-530-92.png\" alt=\"logo\">" +
                        "    </div>" +
                        "    <div style=\"flex: auto; display: flex; align-items: center\">" +
                        "        <img width=\"642\" height=\"548\" src=\"https://img.alicdn.com/imgextra/i3/O1CN010eNgAS25nZGjVKf4L_!!6000000007571-55-tps-321-274.svg\" alt=\"introduction\">" +
                        "    </div>" +
                        "    <div style=\"margin-bottom: 24px; display: flex; width: 100%%\">" +
                        "        <a href=\"itms-services://?action=download-manifest&url=%s\" style=\"flex: auto; margin: 0 48px\">" +
                        "            <button style=\"color: #0070CC; width: 100%%; text-align: center; line-height: 96px; font-size: 48px\">立即下载</button>" +
                        "        </a>" +
                        "    </div>" +
                        "    <div style=\"color: white; margin-bottom: 128px\">" +
                        "        <div style=\"line-height: 32px; font-size: 24px\">文件名：%s</div>" +
                        "        <div style=\"line-height: 32px; font-size: 24px\">版本：%s</div>" +
                        "        <div style=\"line-height: 32px; font-size: 24px\">MD5：%s</div>" +
                        "    </div>" +
                        "</div>" +
                        "</body>" +
                        "</html>";
        ArtifactMetadata artifactMetadata = jobArtifact.getArtifactMetadataObj();
        FileMetadata fileMetadata = jobArtifact.getFileMetadataObj();
        return String.format(content, url, fileMetadata.getFileName(), artifactMetadata.getBundleVersion(), fileMetadata.getFileMD5());
    }

}
