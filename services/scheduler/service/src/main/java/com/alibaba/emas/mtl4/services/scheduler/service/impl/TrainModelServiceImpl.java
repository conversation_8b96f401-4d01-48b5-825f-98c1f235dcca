package com.alibaba.emas.mtl4.services.scheduler.service.impl;

import com.alibaba.emas.mtl4.commons.pipes.RunStatus;
import com.alibaba.emas.mtl4.commons.utils.Pair;
import com.alibaba.emas.mtl4.commons.utils.StringKit;
import com.alibaba.emas.mtl4.core.message.job.PipelineJobInstance;
import com.alibaba.emas.mtl4.core.message.task.PipelineTaskInstance;
import com.alibaba.emas.mtl4.log.enums.TrainModelStatus;
import com.alibaba.emas.mtl4.log.model.TaskModelQuery;
import com.alibaba.emas.mtl4.log.model.TrainModelDTO;
import com.alibaba.emas.mtl4.log.model.TrainModelResultVO;
import com.alibaba.emas.mtl4.log.model.TrainModelVO;
import com.alibaba.emas.mtl4.services.scheduler.domain.*;
import com.alibaba.emas.mtl4.services.scheduler.enums.JobTaskStatus;
import com.alibaba.emas.mtl4.services.scheduler.model.StoragePolicy;
import com.alibaba.emas.mtl4.services.scheduler.query.TrainModelQuery;
import com.alibaba.emas.mtl4.services.scheduler.repository.TrainModelRepository;
import com.alibaba.emas.mtl4.services.scheduler.service.*;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 训练模型service实现
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
@Slf4j
@Service("trainModelService")
public class TrainModelServiceImpl implements TrainModelService {

    private static final String LOG_EMBEDDING_MODEL_IDENTIFIER = "log_embedding_model";

    @Resource
    private TrainModelRepository trainModelRepository;

    @Resource
    private ModelTrainTaskRecordService modelTrainTaskRecordService;

    @Autowired
    private JobScheduleService jobScheduleService;

    @Autowired
    private JobTaskService jobTaskService;

    @Autowired
    private StorageService storageService;

    @Override
    public void uploadTrainModel(TrainModelDTO trainModelDTO) {
        TrainModel trainModel = new TrainModel();
        BeanUtils.copyProperties(trainModelDTO, trainModel);
        trainModel.setGmtCreate(new Date());
        trainModel.setGmtModified(new Date());
        trainModelRepository.save(trainModel);
    }

    @Override
    public void updateTrainModelInfo(Long trainModelId, TrainModelDTO trainModelDTO) {
        TrainModel trainModel = trainModelRepository.findById(trainModelId)
                .orElseThrow(() -> new IllegalArgumentException("训练模型不存在"));
        com.alibaba.emas.mtl4.commons.utils.BeanUtils.mergeObject(trainModel, trainModelDTO);
        trainModel.setGmtModified(new Date());
        trainModelRepository.save(trainModel);
    }

    @Override
    public List<TrainModel> getTrainModelListByQuery(TrainModelQuery trainModelQuery) {
        return null;
    }

    @Override
    public TrainModelResultVO getTrainModelByTask(TaskModelQuery taskModelQuery) {
        JobSchedule jobSchedule = jobScheduleService.getJobSchedule(taskModelQuery.getJobScheduleId());
        List<Pair<PipelineTaskInstance, JobTask>> failedTaskList = getFailedTaskInstance(jobSchedule, taskModelQuery);
        if (CollectionUtils.isEmpty(failedTaskList)) {
            return new TrainModelResultVO().setMessage("没有找到失败的任务，直接跳过运行");
        }

        PipelineTaskInstance failedTaskInstance = null;
        TrainModel failedTaskLogModel = null;
        Pair<PipelineTaskInstance, JobTask> failedTaskPair = null;
        // 找到最近失败的日志模型
        for (Pair<PipelineTaskInstance, JobTask> infoPair : failedTaskList) {
            failedTaskPair = infoPair;
            failedTaskInstance = infoPair.getFirst();
            failedTaskLogModel = getFailedTaskLogModel(jobSchedule.getJobDTO().getPipelineJobInstance(),
                    failedTaskInstance, taskModelQuery.getIdentifierPrefix());
            if (Objects.nonNull(failedTaskLogModel)) {
                break;
            }
        }

        if (Objects.isNull(failedTaskLogModel)) {
            Map<String, Object> extra = Maps.newHashMapWithExpectedSize(1);
            extra.put("task", failedTaskInstance);
            TrainModelResultVO trainModelResultVO = new TrainModelResultVO();
            trainModelResultVO
                    .setMessage(StringKit.format("没有找到任务【{}】对应的模型", failedTaskInstance.getPipelineTask().getName()))
                    .setExtra(extra);
            return trainModelResultVO;
        }
        // 拼装训练模型结果值对象
        ModelTrainTaskRecord trainTaskRecord = modelTrainTaskRecordService.getTrainTaskRecordById(failedTaskLogModel.getModelTrainRecordId());
        TrainModelResultVO modelResultVO = new TrainModelResultVO();
        BeanUtils.copyProperties(failedTaskLogModel, modelResultVO);
        modelResultVO.setErrorTaskInstanceId(failedTaskInstance.getId())
                .setErrorTaskUuid(failedTaskInstance.getPipelineTask().getUuid())
                .setErrorJobTaskId(failedTaskPair.getSecond().getId())
                .setErrorJobTaskLogUploadId(failedTaskPair.getSecond().getTaskLog().getLogUploadId())
                .setMessage("获取模型成功");
        // model 文件下载信息
        Set<String> uploadIdSet = Sets.newHashSetWithExpectedSize(6);
        uploadIdSet.add(failedTaskLogModel.getModelFileUploadId());
        uploadIdSet.add(trainTaskRecord.getOriginLogDataUploadId());
        uploadIdSet.add(trainTaskRecord.getStructuredLogDataUploadId());
        uploadIdSet.add(trainTaskRecord.getTemplatesLogDataUploadId());
        uploadIdSet.add(trainTaskRecord.getFeatureDataUploadId());
        uploadIdSet.add(trainTaskRecord.getParserObjectUploadId());
        List<FileUploaded> fileUploadedList = storageService.getFileUploadedList(uploadIdSet);
        Map<String, FileUploaded> fileUploadedMap = fileUploadedList.stream()
                .collect(Collectors.toMap(FileUploaded::getUploadId, Function.identity(), (v1, v2) -> v2));
        StoragePolicy storagePolicy = storageService.getGlobalStoragePolicy();
        modelResultVO.setModelFileDownloadUrl(storageService.getDownloader(fileUploadedMap.get(failedTaskLogModel.getModelFileUploadId()), storagePolicy).generateDownloadUrl().toString());
        modelResultVO.setOriginLogDownloadUrl(storageService.getDownloader(fileUploadedMap.get(trainTaskRecord.getOriginLogDataUploadId()), storagePolicy).generateDownloadUrl().toString());
        modelResultVO.setStructuredLogDownloadUrl(storageService.getDownloader(fileUploadedMap.get(trainTaskRecord.getStructuredLogDataUploadId()), storagePolicy).generateDownloadUrl().toString());
        modelResultVO.setFeatureDataDownloadUrl(storageService.getDownloader(fileUploadedMap.get(trainTaskRecord.getFeatureDataUploadId()), storagePolicy).generateDownloadUrl().toString());
        modelResultVO.setLogTemplateDownloadUrl(storageService.getDownloader(fileUploadedMap.get(trainTaskRecord.getTemplatesLogDataUploadId()), storagePolicy).generateDownloadUrl().toString());
        Optional.ofNullable(fileUploadedMap.get(trainTaskRecord.getParserObjectUploadId()))
                .ifPresent(fileUploaded -> modelResultVO.setParserObjectDownloadUrl(
                        storageService.getDownloader(fileUploaded, storagePolicy).generateDownloadUrl().toString()));
        return modelResultVO;
    }

    @Override
    public TrainModelVO getLatestModelByIdentifier(String identifier) {
        TrainModel trainModel = trainModelRepository.findFirstByIdentifierAndStatusOrderByIdDesc(identifier,
                TrainModelStatus.VALID)
                .orElseThrow(() -> new IllegalArgumentException("找不到对应的模型"));
        TrainModelVO trainModelVO = new TrainModelVO();
        BeanUtils.copyProperties(trainModel, trainModelVO);
        StoragePolicy storagePolicy = storageService.getGlobalStoragePolicy();
        // 设置 model file 下载链接
        FileUploaded modelFileUploaded = storageService.getFileUploaded(trainModelVO.getModelFileUploadId());
        trainModelVO.setModelFileDownloadUrl(storageService.getDownloader(modelFileUploaded, storagePolicy)
                .generateDownloadUrl().toString());
        if (StringUtils.isNotBlank(trainModel.getParserObjectUploadId())) {
            FileUploaded parserFileUploaded = storageService.getFileUploaded(trainModelVO.getParserObjectUploadId());
            trainModelVO.setParserObjectDownloadUrl(storageService.getDownloader(parserFileUploaded, storagePolicy)
                    .generateDownloadUrl().toString());
        }
        return trainModelVO;
    }

    @Override
    public TrainModelVO getLogEmbeddingModel(TaskModelQuery taskModelQuery) {
        return getLatestModelByIdentifier(LOG_EMBEDDING_MODEL_IDENTIFIER);
    }

    /**
     * 获取失败的任务相关的日志模型.
     * @param pipelineJobInstance job instance
     * @param failedTaskInstance 失败的 task instance
     * @param identifierPrefix 唯一标识前缀
     * @return task 关联的日志模型
     */
    public TrainModel getFailedTaskLogModel(PipelineJobInstance pipelineJobInstance, PipelineTaskInstance
            failedTaskInstance, String identifierPrefix) {
        Map<String, Object> context = Maps.newHashMapWithExpectedSize(4);
        context.put("jobInstance", pipelineJobInstance);
        context.put("taskInstance", failedTaskInstance);
        context.put("prefix", StringUtils.isNotBlank(identifierPrefix) ? identifierPrefix : StringUtils.EMPTY);
        context.put("pluginTask", failedTaskInstance.getPluginTask());

        Function<String, Optional<TrainModel>> getTrainModelByIdentifierFunc = identifier ->
                trainModelRepository.findFirstByIdentifierAndStatusOrderByIdDesc(identifier, TrainModelStatus.VALID);
        // 更改规则就从 strategy 中扩充
        return TrainModelStrategy.getTrainModelByTaskContext(context, getTrainModelByIdentifierFunc);
    }

    /**
     * 找到需要分析的 task 任务.
     * @param jobSchedule 任务调度信息
     * @param taskModelQuery 任务模型查询条件对象
     * @return 获取错误任务实例信息+jobTask
     */
    public List<Pair<PipelineTaskInstance, JobTask>> getFailedTaskInstance(JobSchedule jobSchedule, TaskModelQuery taskModelQuery) {
        List<PipelineTaskInstance> taskInstanceList = jobSchedule.getJobDTO().getPipelineJobInstance().getPipelineTaskInstanceList();
        List<JobTask> jobTaskList = jobTaskService.getJobTaskListByScheduleIdOrderByIdDesc(taskModelQuery.getJobScheduleId());
        // 如果指定了分析的任务的实例id
        if (Objects.nonNull(taskModelQuery.getErrorTaskInstanceId())) {
            return taskInstanceList.stream()
                    .filter(pipelineTaskInstance -> Objects.equals(pipelineTaskInstance.getId(), taskModelQuery.getErrorTaskInstanceId()))
                    .findFirst()
                    .map(pipelineTaskInstance -> {
                        JobTask failedJobTask = jobTaskList.stream()
                                .filter(jobTask -> StringUtils.equals(jobTask.getTaskId(), pipelineTaskInstance.getPipelineTask().getUuid()))
                                .findFirst()
                                .orElse(null);
                        return Lists.newArrayList(new Pair<>(pipelineTaskInstance, failedJobTask));
                    })
                    .orElse(new ArrayList<>());
        }
        Map<String, PipelineTaskInstance> taskInstanceMap = taskInstanceList.stream()
                .collect(Collectors.toMap(pipelineTaskInstance -> pipelineTaskInstance.getPipelineTask().getUuid(),
                        Function.identity(), (v1, v2) -> v2));

        List<Pair<PipelineTaskInstance, JobTask>> failedTaskList = new ArrayList<>();
        // 找到最近的失败的构建任务，这个是倒序的任务列表
        for (JobTask jobTask : jobTaskList) {
            // 如果 jobTask 是完整状态，但是 runStatus 是运行失败
            if (JobTaskStatus.FINISHED.equals(jobTask.getTaskStatus()) && RunStatus.FAILED.equals(
                    jobTask.getTaskResult().getStatus())) {
                failedTaskList.add(new Pair<>(taskInstanceMap.get(jobTask.getTaskId()), jobTask));
            }
        }
        return failedTaskList;
    }

}
