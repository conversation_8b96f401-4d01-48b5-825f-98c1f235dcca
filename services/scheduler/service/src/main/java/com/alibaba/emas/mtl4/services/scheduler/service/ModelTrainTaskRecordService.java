package com.alibaba.emas.mtl4.services.scheduler.service;

import com.alibaba.emas.mtl4.log.model.ModelTrainTaskRecordDTO;
import com.alibaba.emas.mtl4.services.scheduler.domain.ModelTrainTaskRecord;

/**
 * 模型训练任务记录service
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
public interface ModelTrainTaskRecordService {

    /**
     * 保存模型训练记录.
     * @param modelTrainTaskRecordDTO 训练模型传输对象
     */
    void saveModelTrainTaskRecord(ModelTrainTaskRecordDTO modelTrainTaskRecordDTO);

    /**
     * 更新模型训练任务记录信息.
     * @param trainRecordId 训练记录 id
     * @param modelTrainTaskRecordDTO 训练模型任务记录传输数据对象
     */
    void updateTrainModelInfo(Long trainRecordId, ModelTrainTaskRecordDTO modelTrainTaskRecordDTO);

    /**
     * 根据 id 查询相关的训练任务记录.
     * @param trainTaskRecordId 任务训练记录 id
     * @return 训练任务记录
     */
    ModelTrainTaskRecord getTrainTaskRecordById(Long trainTaskRecordId);

}
