<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.alibaba.emas</groupId>
        <artifactId>mtl4-services-scheduler</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>mtl4-services-scheduler-service</artifactId>
    <version>${revision}</version>
    <name>MTL4 :: Services :: Scheduler :: Service</name>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-commons-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-commons-aliyun</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-commons-msgcenter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-core-message</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-services-scheduler-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-services-scheduler-repository</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-services-scheduler-api</artifactId>
        </dependency>
        <!-- ### HSF Dependency Start ### -->
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-services-admin-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-services-plugin-client</artifactId>
        </dependency>
        <!-- ### HSF Dependency End ### -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>mtl4-boot-starter-hsf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
        </dependency>
        <dependency>
            <groupId>org.gitlab4j</groupId>
            <artifactId>gitlab4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.12.20</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-exec</artifactId>
            <version>1.3</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mvel</groupId>
            <artifactId>mvel2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx2-spring-boot-starter</artifactId>
        </dependency>

    </dependencies>
</project>