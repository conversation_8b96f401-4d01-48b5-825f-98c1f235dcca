package com.alibaba.emas.mtl4.scheduler.api;

import com.alibaba.emas.mtl4.commons.pipes.RunStatus;
import com.alibaba.emas.mtl4.commons.storage.StorageObjectKey;
import com.alibaba.emas.mtl4.commons.storage.StorageType;
import com.alibaba.emas.mtl4.core.message.job.Environment;
import com.alibaba.emas.mtl4.core.message.job.JobDTO;
import com.alibaba.emas.mtl4.core.message.job.JobExt;
import com.alibaba.emas.mtl4.log.model.LogAnalyzeResultVO;
import com.alibaba.emas.mtl4.services.scheduler.domain.JobArtifact;
import com.alibaba.emas.mtl4.services.scheduler.domain.JobSchedule;
import com.alibaba.emas.mtl4.services.scheduler.domain.JobTask;
import com.alibaba.emas.mtl4.services.scheduler.model.*;
import com.alibaba.emas.mtl4.services.scheduler.query.JobArtifactQuery;
import com.alibaba.emas.mtl4.services.scheduler.query.JobTaskQuery;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface JobClientService {

    JobSchedule submitJob(Long workerId, JobDTO jobDTO);

    JobSchedule submitJob(Long workerId, JobDTO jobDTO, Integer timeoutSec);

    JobSchedule submitJob(Set<Long> workgroupIds, Environment env, JobDTO jobDTO);

    JobSchedule submitJob(Set<Long> workgroupIds, Environment env, JobDTO jobDTO, Integer timeoutSec);

    JobSchedule submitServerlessJob(Set<Long> workgroupIds, JobDTO jobDTO);

    JobSchedule submitServerlessJob(Set<Long> workgroupIds, JobDTO jobDTO, Integer timeoutSec);

    JobExt fetchJob(Long workerId);

    JobSchedule getScheduleJob(Long jobScheduleId);

    JobTask getJobTask(Long jobScheduleId, String taskId);

    List<JobTask> queryJobTask(Long jobScheduleId, JobTaskQuery jobTaskQuery);

    JobArtifact getJobArtifact(Long jobScheduleId, String artifactId);

    List<JobArtifactBO> queryJobArtifact(Long jobScheduleId, JobArtifactQuery jobArtifactQuery);

    JobArtifactBO getJobArtifact(Long id);

    List<JobArtifactBO> getJobArtifacts(Long[] ids);

    String createDownloadPlist(String url, String bundleIdentifier, String bundleVersion, String title) throws IOException, TransformerException, ParserConfigurationException;

    String createPlistHtmlContainer(String plistDownloadUrl);

    String stop(String definitionId, RunStatus runStatus);

    JobExt fetchStopJob(Long workerId);

    /**
     * 查询任务 condition 是否可以调度.
     *
     * @param workgroupCondition 过滤条件
     * @return 条件检查值对象
     */
    ConditionCheckResultVO checkJobCondition(WorkgroupCondition workgroupCondition);

    /**
     * 查询任务 condition 是否可以调度.
     *
     * @param workgroupCondition    过滤条件
     * @param workerMachineInfoList 机器信息列表
     * @return 条件检查值对象
     */
    ConditionCheckResultVO checkJobCondition(WorkgroupCondition workgroupCondition, Collection<WorkerMachineInfo>
            workerMachineInfoList);

    /**
     * 根据日志分析结果记录 id 查询日志内容.
     *
     * @param logAnalyzeResultId 分析结果id
     * @param start              content 开头
     * @param length             content 结束位置
     * @return 日志内容
     */
    LogContent getLogContentByLogAnalyzeResultId(Long logAnalyzeResultId, Long start, Long length);


    /**
     * 根据日志上传 id 查询日志下载详情.
     *
     * @param length   长度
     * @param uploadId 分析结果id
     * @return 日志下载详情
     */
    LogDetail getLogDetailByUploadId(Long length, String uploadId);

    /**
     * 根据 task instance id 查询日志内容.
     *
     * @param taskInstanceId task 任务实例 id
     * @param start          content 开头
     * @param length         content 结束位置
     * @return 日志内容
     */
    LogContent getLogContentByTaskInstanceId(Long taskInstanceId, Long start, Long length);

    /**
     * 根据日志分析结果记录 id 查询日志内容.
     *
     * @param logAnalyzeResultId 分析结果id
     * @param start              content 开头
     * @param length             content 结束位置
     * @return 日志内容
     */
    LogAnalyzeResultVO getLogAnalyzeResultWithContent(Long logAnalyzeResultId, Long start, Long length);

    /**
     * 根据 task 任务 id 获取日志分析结果记录.
     *
     * @param taskInstanceId task instance id.
     * @return 日志分析记录
     */
    LogAnalyzeResultVO getLogAnalyzeResultByTaskInstanceId(Long taskInstanceId);

    /**
     * 批量根据 task 任务 id 获取日志分析结果记录.
     *
     * @param taskInstanceIdList task instance id 列表.
     * @return 日志分析记录列表
     */
    List<LogAnalyzeResultVO> batchGetLogAnalyzeResultByTaskInstanceId(List<Long> taskInstanceIdList);


    /**
     * logFileUploaded
     * @param
     * @return
     */
    String logFileUploaded(String tokenId, String filename, Map<String, String> fileMetadata, StorageType storageType,
                           StorageObjectKey storageObjectKey, Map<String, String> storageObjectMetadata);

}
