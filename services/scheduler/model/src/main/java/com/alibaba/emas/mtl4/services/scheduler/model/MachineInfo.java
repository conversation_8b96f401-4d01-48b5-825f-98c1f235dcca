package com.alibaba.emas.mtl4.services.scheduler.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class MachineInfo {

    String hostName;
    String hostAddress;
    String macAddress;

    // cpu
    Integer logicalCpuCount;
    Integer physicalCpuCount;
    Boolean isCpu64bit;
    Double cpuFreqGHz;

    // memory
    Double maxMemoryMB;

    // disk
    Double hardDiskGB;

    // system
    String os;
    String osVersion;
    String osArch;
    Long maxFileDescriptor;

    String javaVersion;
    String xcodeVersion;
    String androidVersion;
    String pythonVersion;

    List<String> installedTools;
    Map<String, String> javaProperties;
}
