package com.alibaba.emas.mtl4.services.scheduler.api.v1;

import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.core.message.job.JobExt;
import com.alibaba.emas.mtl4.scheduler.api.JobClientService;
import com.alibaba.emas.mtl4.scheduler.client.model.*;
import com.alibaba.emas.mtl4.services.scheduler.domain.Machine;
import com.alibaba.emas.mtl4.services.scheduler.domain.Worker;
import com.alibaba.emas.mtl4.services.scheduler.exception.BizException;
import com.alibaba.emas.mtl4.services.scheduler.model.WorkerToken;
import com.alibaba.emas.mtl4.services.scheduler.service.MachineService;
import com.alibaba.emas.mtl4.services.scheduler.service.RegisterService;
import com.alibaba.emas.mtl4.services.scheduler.service.WorkerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api
@RestController
@RequestMapping("/api/v1")
public class WorkerApi {

    @Autowired
    private WorkerService workerService;

    @Autowired
    private MachineService machineService;

    @Autowired
    private RegisterService registerService;

    @Autowired
    private JobClientService jobClientService;

    @ApiOperation(value = "register")
    @RequestMapping(path = "/workers", method = RequestMethod.POST)
    public RegisterResult register(@RequestBody RegisterRequest registerRequest) {

        Assert.notNull(registerRequest);
        Assert.notNull(registerRequest.getAuthKey());
        Assert.notNull(registerRequest.getAuthRandom());
        Assert.notNull(registerRequest.getAuthInfo());
        Assert.notNull(registerRequest.getWorkerInfo());
        Assert.notNull(registerRequest.getMachineInfo());

        String authKey = registerRequest.getAuthKey();
        String authRandom = registerRequest.getAuthRandom();
        String authInfo = registerRequest.getAuthInfo();

        com.alibaba.emas.mtl4.services.scheduler.model.WorkerInfo workerInfo =
                new com.alibaba.emas.mtl4.services.scheduler.model.WorkerInfo();
        BeanUtils.copyProperties(registerRequest.getWorkerInfo(), workerInfo);

        com.alibaba.emas.mtl4.services.scheduler.model.MachineInfo machineInfo =
                new com.alibaba.emas.mtl4.services.scheduler.model.MachineInfo();
        BeanUtils.copyProperties(registerRequest.getMachineInfo(), machineInfo);

        WorkerToken workerToken = registerService.register(authKey, authRandom, authInfo,
                workerInfo, machineInfo);

        RegisterResult registerResult = new RegisterResult();
        registerResult.setWorkerId(workerToken.getWorkerId());
        registerResult.setSignToken(workerToken.getSignToken());
        return registerResult;
    }

    @ApiOperation(value = "updateMachineInfo")
    @RequestMapping(path = "/workers/{workerId}/machineInfo", method = RequestMethod.POST)
    public UpdateMachineInfoResult updateMachineInfo(@PathVariable String workerId,
                                                     @RequestBody UpdateMachineInfoRequest updateMachineInfoRequest) {

        Assert.notNull(workerId);
        Assert.notNull(updateMachineInfoRequest);
        Assert.notNull(updateMachineInfoRequest.getMachineInfo());

        MachineInfo machineInfo = updateMachineInfoRequest.getMachineInfo();
        Worker worker = workerService.getWorker(workerId);
        String macAddress = machineInfo.getMacAddress();

        try {
            Machine machine = machineService.getMachine(macAddress);
            log.error(">>> Update MachineInfo workerId: {} machineInfo: {}", workerId, machineInfo);
            BeanUtils.copyProperties(machineInfo, machine);
            machineService.updateMachine(machine);
            if (!worker.getMachineId().equals(machine.getId())) {
                worker.setMachineId(machine.getId());
                workerService.updateWorker(worker);
            }
        } catch (BizException e) {
            // machine 没有的情况
            Machine machine = new Machine();
            com.alibaba.emas.mtl4.commons.utils.BeanUtils.copyProperties(machineInfo, machine);
            Machine persistMachine = machineService.createMachine(machine);
            worker.setMachineId(persistMachine.getId());
            workerService.updateWorker(worker);
        }
        return new UpdateMachineInfoResult();
    }

    @ApiOperation(value = "reportMetrics")
    @RequestMapping(path = "/workers/{workerId}/metrics", method = RequestMethod.POST)
    public ReportMetricsResult reportMetrics(@PathVariable String workerId,
                                             @RequestBody ReportMetricsRequest reportMetricsRequest) {

        Assert.notNull(workerId);
        Assert.notNull(reportMetricsRequest);

        return null;
    }

    @ApiOperation(value = "pushEvents")
    @RequestMapping(path = "/workers/{workerId}/events", method = RequestMethod.POST)
    public PushEventsResult pushEvents(@PathVariable String workerId,
                                       @RequestBody PushEventsRequest pushEventsRequest) {

        Assert.notNull(workerId);
        Assert.notNull(pushEventsRequest);

        return null;
    }

    @ApiOperation(value = "pullJob")
    @RequestMapping(path = "/workers/{workerId}/pullJob", method = RequestMethod.POST)
    public PullJobResult pullJob(@PathVariable String workerId,
                                 @RequestBody PullJobRequest pullJobRequest) {

        Assert.notNull(workerId);
        Assert.notNull(pullJobRequest);

        PullJobResult pullJobResult = new PullJobResult();
        Worker worker = workerService.getWorker(workerId);
        workerService.updateOnline(worker, pullJobRequest.getRunningJobList());
        //先 stop 的任务
        JobExt stopJobExt = jobClientService.fetchStopJob(worker.getId());
        if (null != stopJobExt) {
            pullJobResult.addJob(stopJobExt);
            return pullJobResult;
        }
        if (workerService.canPullJob(worker, pullJobRequest.getRunningJobList())) {
            JobExt jobExt = jobClientService.fetchJob(worker.getId());
            if (null != jobExt) {
                pullJobResult.addJob(jobExt);
            }
        }
        return pullJobResult;
    }

}
