package com.alibaba.emas.mtl4.services.copilot.web;

import javax.servlet.http.HttpServletRequest;

import com.alibaba.emas.mtl4.boot.starter.web.utils.WebRequestUtil;
import com.alibaba.emas.mtl4.commons.utils.WebResult;
import com.alibaba.emas.mtl4.services.copilot.client.CopilotChatService;
import com.alibaba.emas.mtl4.services.copilot.client.dto.CopilotChatMessageAgreeDTO;
import com.alibaba.emas.mtl4.services.copilot.client.dto.CopilotChatMessageDisagreeDTO;
import com.alibaba.emas.mtl4.services.copilot.client.dto.CopilotChatNewUserMessageDTO;
import com.alibaba.emas.mtl4.services.copilot.model.CopilotChat;
import com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessage;
import com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessageStatus;
import com.alibaba.emas.mtl4.services.copilot.model.query.CopilotChatMessageQuery;
import com.alibaba.emas.mtl4.services.copilot.repository.CopilotChatMessageRepository;
import com.alibaba.emas.mtl4.services.copilot.service.CopilotChatFeedbackManager;
import com.alibaba.emas.mtl4.services.copilot.service.CopilotChatHelper;
import com.alibaba.emas.mtl4.services.copilot.client.dto.CopilotChatAgentDTO;
import com.alibaba.emas.mtl4.services.copilot.client.dto.CopilotChatAgentResultDTO;
import com.alibaba.emas.mtl4.services.copilot.service.ReleaseIncrScanRequestProcessor;
import com.alibaba.fastjson.JSON;

import com.google.common.collect.ImmutableMap;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import static com.alibaba.emas.mtl4.services.copilot.model.CopilotChatConstant.SLIMMING_AGENT;
import static com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessageContentType.PIPELINE_INSTANCE_SLIMMING_REQUEST;
import static com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessageContentType.RELEASE_SLIMMING_REQUEST;
import static com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessageContentType.SLIMMING_RESULT;

@Slf4j
@RestController
@RequestMapping("/api/v1/copilot/chat")
@Api(tags = "Copilot对话")
public class CopilotChatController {

    @Autowired
    private CopilotChatService copilotChatService;

    @Autowired
    private CopilotChatMessageRepository copilotChatMessageRepository;

    @Autowired
    private CopilotChatHelper copilotChatHelper;

    @Autowired
    private CopilotChatFeedbackManager copilotChatFeedbackManager;

    @ApiOperation(value = "获取或者新增对话")
    @GetMapping("getOrNewChat")
    @ResponseBody
    public WebResult<CopilotChat> getOrNewChat(@RequestParam String agentIdentifier,
        @RequestParam(required = false) String entityType,
        @RequestParam(required = false) Long entityId, HttpServletRequest request) {
        String empId = WebRequestUtil.getEmpIdFromRequest(request);
        return WebResult.ok(copilotChatService.getOrNewChat(agentIdentifier, empId, entityType, entityId));
    }

    @ApiOperation(value = "新增用户消息")
    @PostMapping("sendNewUserMessage")
    @ResponseBody
    public WebResult<Boolean> sendNewUserMessage(@RequestBody CopilotChatNewUserMessageDTO newMessage,
        HttpServletRequest request) {
        log.info(">>>sendNewUserMessage:{}", JSON.toJSONString(newMessage));
        String empId = WebRequestUtil.getEmpIdFromRequest(request);
        if (null == newMessage.getChatId()) {
            CopilotChat copilotChat = copilotChatService.getOrNewChat(SLIMMING_AGENT, empId, null, null);
            newMessage.setChatId(copilotChat.getId());
        }
        if (null == newMessage.getRetry()) {
            newMessage.setRetry(false);
        }
        newMessage.setCreator(empId);

        copilotChatService.sendNewUserMessage(newMessage);
        return WebResult.ok(true);
    }

    @ApiOperation(value = "重试用户消息")
    @GetMapping("resendExistUserMessage")
    @ResponseBody
    public WebResult<Boolean> resendExistUserMessage(@RequestParam Long messageId, HttpServletRequest request) {
        log.info(">>>resendExistUserMessage:{}", messageId);
        CopilotChatMessage message = copilotChatService.getMessageById(messageId);
        CopilotChatNewUserMessageDTO newMessage = new CopilotChatNewUserMessageDTO();
        newMessage.setChatId(message.getChatId());
        newMessage.setContent(message.getContent());
        newMessage.setContentType(message.getContentType());
        newMessage.setRedirects(message.getRedirects());
        newMessage.setContentMeta(message.getContentMeta());
        newMessage.setCreator(message.getCreator());
        newMessage.setRetry(true);

        copilotChatService.sendNewUserMessage(newMessage);
        return WebResult.ok(true);
    }

    @ApiOperation(value = "取消消息")
    @PostMapping("cancelMessageById")
    @ResponseBody
    public WebResult<Boolean> cancelMessageById(@RequestParam Long id) {
        log.info(">>>cancelMessageById:{}", id);
        copilotChatService.updateMessageStatus(id, CopilotChatMessageStatus.CANCELED);
        return WebResult.ok(true);
    }

    @ApiOperation(value = "删除消息")
    @DeleteMapping("deleteMessageById")
    @ResponseBody
    public WebResult<Boolean> deleteMessageById(@RequestParam Long id) {
        CopilotChatMessage message = copilotChatService.getMessageById(id);
        if (null != message) {
            if (RELEASE_SLIMMING_REQUEST.equals(message.getContentType())
                || PIPELINE_INSTANCE_SLIMMING_REQUEST.equals(message.getContentType())) {
                copilotChatService.deleteMessageByQuery(CopilotChatMessageQuery.builder()
                    .contentType(SLIMMING_RESULT)
                    .contentMeta(ImmutableMap.of("messageId", id))
                    .build());
            }
            copilotChatMessageRepository.deleteById(id);
        }

        return WebResult.ok(true);
    }

    @ApiOperation(value = "根据ID获取对话")
    @GetMapping("getChatById")
    @ResponseBody
    public WebResult<CopilotChat> getChatById(@RequestParam Long id, HttpServletRequest request) {
        String empId = WebRequestUtil.getEmpIdFromRequest(request);
        return WebResult.ok(copilotChatService.getChatById(id, empId));
    }

    @ApiOperation(value = "Copilot Chat Agent结果")
    @PostMapping("getCopilotChatAgentResult")
    @ResponseBody
    public WebResult<CopilotChatAgentResultDTO> getCopilotChatAgentResult(
        @RequestBody CopilotChatAgentDTO copilotChatAgentDTO) {
        return WebResult.ok(copilotChatService.getCopilotChatAgentResult(copilotChatAgentDTO));
    }

    @ApiOperation(value = "新增赞同")
    @PostMapping("addAgree")
    @ResponseBody
    public WebResult<Boolean> addAgree(@RequestBody CopilotChatMessageAgreeDTO agreeDTO) {
        copilotChatFeedbackManager.addAgree(agreeDTO);
        return WebResult.ok(true);
    }

    @ApiOperation(value = "删除赞同")
    @PostMapping("removeAgree")
    @ResponseBody
    public WebResult<Boolean> removeAgree(@RequestBody CopilotChatMessageAgreeDTO agreeDTO) {
        copilotChatFeedbackManager.removeAgree(agreeDTO);
        return WebResult.ok(true);
    }

    @ApiOperation(value = "新增不赞同")
    @PostMapping("addDisagree")
    @ResponseBody
    public WebResult<Boolean> addDisagree(@RequestBody CopilotChatMessageDisagreeDTO disagreeDTO) {
        copilotChatFeedbackManager.addDisagree(disagreeDTO);
        return WebResult.ok(true);
    }

    @ApiOperation(value = "删除不赞同")
    @PostMapping("removeDisagree")
    @ResponseBody
    public WebResult<Boolean> removeDisagree(@RequestBody CopilotChatMessageDisagreeDTO disagreeDTO) {
        copilotChatFeedbackManager.removeDisagree(disagreeDTO);
        return WebResult.ok(true);
    }

    @Autowired
    private ReleaseIncrScanRequestProcessor releaseIncrScanRequestProcessor;

    @ApiOperation(value = "版本增量代码扫描请求")
    @GetMapping("sendReleaseIncrScanRequest")
    @ResponseBody
    public WebResult<Boolean> sendReleaseIncrScanRequest(@RequestParam Long appId, @RequestParam Long releaseId, @RequestParam Boolean retry) {
        new Thread(() -> releaseIncrScanRequestProcessor.sendReleaseIncrScanRequest(appId, releaseId, retry)).start();
        return WebResult.ok(true);
    }

}
