package com.alibaba.emas.mtl4.services.copilot.web.assembler;

import com.alibaba.emas.mtl4.commons.utils.CompletableFutureUtils;
import com.alibaba.emas.mtl4.commons.utils.ConversionUtils;
import com.alibaba.emas.mtl4.commons.utils.GitUtils;
import com.alibaba.emas.mtl4.commons.utils.Tuple2;
import com.alibaba.emas.mtl4.services.ai.api.model.SlimmingStrategy;
import com.alibaba.emas.mtl4.services.copilot.client.CopilotChatService;
import com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessage;
import com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessageRole;
import com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessageStatus;
import com.alibaba.emas.mtl4.services.copilot.model.query.CopilotChatMessageQuery;
import com.alibaba.emas.mtl4.services.copilot.service.CopilotChatHelper;
import com.alibaba.emas.mtl4.services.copilot.web.vo.ModuleIterationSlimmingSummaryVO;
import com.alibaba.emas.mtl4.services.copilot.web.vo.ModuleSlimmingSummaryVO;
import com.alibaba.emas.mtl4.services.dev.api.model.*;
import com.alibaba.emas.mtl4.services.dev.api.service.*;
import com.alibaba.emas.mtl4.services.dev.collaboration.CollaborationSpaceCodeService;
import com.alibaba.emas.mtl4.services.dev.collaboration.model.CollaborationSpaceModuleSize;
import com.alibaba.emas.mtl4.services.dev.module.ModuleConfigService;
import com.alibaba.emas.mtl4.services.dev.module.ModuleSizeService;
import com.alibaba.emas.mtl4.services.dev.module.model.ModuleDeliveryEfficiency;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.motu.utils.DateUtils;
import com.alibaba.motu.utils.result.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessageContentType.RELEASE_SLIMMING_REQUEST;

/**
 * <AUTHOR>
 * @Date 2024-08-02
 */
@Component
@Slf4j
public class ModuleSlimmingSummaryAssembler {

    @Autowired
    private AppService appService;
    @Autowired
    private PublishArchiveOpenService archiveOpenService;
    @Autowired
    private CopilotChatService copilotChatService;
    @Autowired
    private CopilotChatHelper copilotChatHelper;
    @Autowired
    private CollaborationSpaceCodeService spaceCodeService;
    @Autowired
    private ModuleSizeService moduleSizeService;
    @Autowired
    private ModuleConfigService configService;
    @Autowired
    private ApplicationDependencyService appDepService;
    @Autowired
    private ApplicationDependencyDetailService appDepDetailService;
    @Autowired
    private AlterSheetOpenService alterSheetService;

    private static final Pattern GIT_COMMIT_ID_PATTERN = Pattern.compile("^[0-9a-fA-F]{40}$");
    private final int timeout = 60;


    public ModuleIterationSlimmingSummaryVO assembleLatestArchivedModuleSummary(Long appId, Long moduleId) {
        PublishArchiveBO archiveBO = archiveOpenService.getLastReleaseArchiveBO(appId);
        if (archiveBO == null) {
            return null;
        }
        CopilotChatMessage latestAppSlimmingMessage = getLatestAppSlimmingMessage(appId);
        return assembleLatestArchivedModuleSummary(appId, moduleId, archiveBO.getArchiveVersion(), latestAppSlimmingMessage);
    }

    public ModuleIterationSlimmingSummaryVO assembleLatestArchivedModuleSummary(Long appId, Long moduleId, String appVersion, CopilotChatMessage latestAppSlimmingMessage) {
        ModuleIterationSlimmingSummaryVO summaryVO = new ModuleIterationSlimmingSummaryVO();

        CompletableFutureUtils.allOf(() -> {
            summaryVO.setAppId(appId);
            summaryVO.setAppVersion(appVersion);
            summaryVO.setAppName(appService.getAppNameById(appId));
        }, () -> {
            ApplicationBO module = appService.findApplication(moduleId);
            summaryVO.setModuleId(module.getId());
            summaryVO.setModuleName(module.getName());
            summaryVO.setModulePlatform(module.getPlatformType());
        }, () -> {
            summaryVO.setSpaceId(spaceCodeService.getModuleTeamSpaceId(moduleId));
        }, () -> {
            Long bizSpaceId = spaceCodeService.getModuleBizSpaceId(moduleId);
            summaryVO.setBizSpaceId(bizSpaceId);
            Boolean isDynamic = configService.getModuleIsDynamic(moduleId);
            summaryVO.setIsDynamic(isDynamic);
            if (bizSpaceId != null) {
                moduleSizeService.getSpaceReleaseModuleSize(bizSpaceId, appId, appVersion)
                    .ifPresent(spaceModuleSize -> {
                        Long moduleSizeBalance = spaceModuleSize.getModuleSizeBalance();
                        summaryVO.setSpaceBalance(ConversionUtils.toDouble(moduleSizeBalance));
                        ModuleDeliveryEfficiency efficiency = ModuleSizeService.calculateDeliveryEfficiency(isDynamic, moduleSizeBalance);
                        summaryVO.setDeliveryEfficiency(efficiency);
                    });
            }
        }, () -> {
            if (latestAppSlimmingMessage != null) {
                CopilotChatMessage assistantMessage = getModuleReleaseVersionMessage(latestAppSlimmingMessage, moduleId);
                if (assistantMessage != null) {
                    assembleSlimmingMessageContent(summaryVO, assistantMessage);
                }
            }
        });

        return summaryVO;
    }

    public List<ModuleIterationSlimmingSummaryVO> batchAssembleLatestArchivedModuleSummary(Long alterSheetId) {
        AlterSheetBO alterSheetBO = alterSheetService.findAlterSheetBOById(alterSheetId);
        if (alterSheetBO == null || CollectionUtils.isEmpty(alterSheetBO.getAlterSheetModuleList())) {
           return Collections.emptyList();
        }
        Long appId = alterSheetBO.getApplicationId();
        PublishArchiveBO archiveBO = archiveOpenService.getLastReleaseArchiveBO(appId);
        CopilotChatMessage latestAppSlimmingMessage = getLatestAppSlimmingMessage(appId);
        List<CompletableFuture<ModuleIterationSlimmingSummaryVO>> futures =  alterSheetBO.getAlterSheetModuleList()
                .stream()
                .map(alterSheetModule -> CompletableFuture.supplyAsync(() -> assembleLatestArchivedModuleSummary(appId,
                                alterSheetModule.getModuleId(), archiveBO.getArchiveVersion(), latestAppSlimmingMessage)))
                .collect(Collectors.toList());
        CompletableFuture<Void> resultCf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]));
        CompletableFuture<List<ModuleIterationSlimmingSummaryVO>> allCfResult = resultCf
                .thenApply(t -> futures.stream().map(CompletableFuture::join)
                .collect(Collectors.toList()));
        try {
            return allCfResult.get(timeout, TimeUnit.SECONDS);
        } catch (ExecutionException | InterruptedException | TimeoutException e) {
            log.error("alterSheetId {} batchAssembleLatestArchivedModuleSummary error {}", alterSheetId, e.getMessage());
        }
        return Collections.emptyList();
    }

    private CopilotChatMessage getLatestAppSlimmingMessage(Long appId) {
        //获取最新的有优化建议的 版本号
        Map<String, Object> filter = new HashMap<>();
        filter.put("appId", appId);
        CopilotChatMessageQuery query = new CopilotChatMessageQuery();
        query.setRole(CopilotChatMessageRole.USER);
        query.setContentType(RELEASE_SLIMMING_REQUEST);
        query.setStatus(CopilotChatMessageStatus.FINAL);
        query.setContentMeta(filter);
        return copilotChatService.findMessageByQuery(query)
            .stream()
            .max(Comparator.comparing(CopilotChatMessage::getId))
            .orElse(null);
    }

    private CopilotChatMessage getModuleReleaseVersionMessage(CopilotChatMessage userMessage, Long moduleId) {
        Map<String, Object> contentMeta = new HashMap<>(4);
        contentMeta.put("messageId", userMessage.getId());
        contentMeta.put("module", String.valueOf(moduleId));
        List<CopilotChatMessage> assistantMessages = copilotChatService.findMessageByQuery(
                CopilotChatMessageQuery.builder()
                        .role(CopilotChatMessageRole.ASSISTANT)
                        .chatId(userMessage.getChatId())
                        .contentMeta(contentMeta)
                        .build());
        if (CollectionUtils.isNotEmpty(assistantMessages)) {
           return assistantMessages.get(0);
        }
        return null;
    }

    public void assembleSlimmingMessageContent(ModuleIterationSlimmingSummaryVO summaryVO,
                                               CopilotChatMessage assistantMessage) {
        summaryVO.setMessageId(assistantMessage.getId());
        JSONObject contentJO = JSONObject.parseObject(assistantMessage.getContent());
        summaryVO.setDiffByteCount(contentJO.getLong("diffByteCount"));
    }

    public String trimCodeRef(String ref) {
        if (StringUtils.isBlank(ref)) {
            return null;
        }
        if (GIT_COMMIT_ID_PATTERN.matcher(ref).matches()) {
            return ref.substring(0, 8);
        }
        return ref;
    }


}
