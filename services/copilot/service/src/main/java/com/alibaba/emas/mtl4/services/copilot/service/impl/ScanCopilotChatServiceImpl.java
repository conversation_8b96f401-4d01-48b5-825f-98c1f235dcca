package com.alibaba.emas.mtl4.services.copilot.service.impl;

import java.util.List;

import com.alibaba.emas.mtl4.services.copilot.client.CopilotChatService;
import com.alibaba.emas.mtl4.services.copilot.client.ScanCopilotChatService;
import com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessage;
import com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessageRole;
import com.alibaba.emas.mtl4.services.copilot.model.query.CopilotChatMessageQuery;

import com.google.common.collect.ImmutableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessageContentType.CODE_SCAN_RESULT;
import static com.alibaba.emas.mtl4.services.copilot.model.CopilotChatMessageContentType.SLIMMING_RESULT;

@Component
public class ScanCopilotChatServiceImpl implements ScanCopilotChatService {

    @Autowired
    private CopilotChatService copilotChatService;

    @Override
    public List<CopilotChatMessage> findScanResultMessages(Long messageId) {
        return copilotChatService.findMessages(CopilotChatMessageRole.ASSISTANT, CODE_SCAN_RESULT, messageId);
    }

    @Override
    public CopilotChatMessage findScanResultMessage(Long messageId, Long moduleId) {
        return copilotChatService.findLatestMessageByQuery(CopilotChatMessageQuery.builder()
            .role(CopilotChatMessageRole.ASSISTANT)
            .contentType(CODE_SCAN_RESULT)
            .contentMeta(ImmutableMap.of("messageId", messageId, "module", moduleId.toString()))
            .build());
    }
}
