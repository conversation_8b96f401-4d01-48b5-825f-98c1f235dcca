package com.alibaba.emas.mtl4.services.plugin.service.info.impl;

import com.alibaba.emas.mtl4.core.plugin.model.PluginLocalDescriptor;
import com.alibaba.emas.mtl4.core.plugin.model.PluginMavenRepoDescriptor;
import com.alibaba.emas.mtl4.core.plugin.service.repo.PluginMavenDownloadService;
import com.alibaba.emas.mtl4.core.plugin.service.repo.impl.PluginMavenDownloadServiceImpl;
import com.alibaba.emas.mtl4.plugin.core.pf4j.LocalPluginRepository;
import com.alibaba.emas.mtl4.plugin.core.pf4j.PluginDescriptor;
import com.alibaba.emas.mtl4.services.plugin.model.plugin.PluginBaseInfo;
import com.alibaba.emas.mtl4.services.plugin.service.info.PluginBaseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 */
@Service
public class PluginBaseInfoServiceImpl implements PluginBaseInfoService {
    @Autowired
    PluginMavenDownloadService pluginMavenDownloadService;

    @Override
    public PluginBaseInfo getPluginBaseInfoRemote(PluginMavenRepoDescriptor pluginMavenRepoDescriptor) throws Exception {
        if (pluginMavenRepoDescriptor != null && pluginMavenRepoDescriptor.getGroupId() != null && pluginMavenRepoDescriptor.getArtifactId() != null && pluginMavenRepoDescriptor.getMavenVersion() != null) {
            File downloadFile = pluginMavenDownloadService.download(pluginMavenRepoDescriptor);
            if (downloadFile != null) {
                PluginLocalDescriptor pluginLocalDescriptor = PluginLocalDescriptor.from(pluginMavenRepoDescriptor);
                pluginLocalDescriptor.setPluginJarFile(downloadFile);
                return getPluginBaseInfoLocal(pluginLocalDescriptor);
            }
        }
        return null;
    }

    @Override
    public PluginBaseInfo getPluginBaseInfoLocal(PluginLocalDescriptor pluginLocalDescriptor) {
        if (pluginLocalDescriptor != null && pluginLocalDescriptor.getPluginJarFile() != null) {
            LocalPluginRepository localPluginRepository = new LocalPluginRepository(Paths.get(pluginLocalDescriptor.getPluginJarFile().getParent()), true);
            PluginDescriptor pluginDescriptor = localPluginRepository.findPluginDescriptor(pluginLocalDescriptor.getPluginJarFile().toPath());
            PluginBaseInfo pluginBaseInfo = new PluginBaseInfo();
            if (pluginDescriptor != null) {
                pluginBaseInfo.setPluginId(pluginDescriptor.getPluginId());
                pluginBaseInfo.setPluginProvider(pluginDescriptor.getProvider());
                pluginBaseInfo.setPluginVersion(pluginDescriptor.getVersion());
                pluginBaseInfo.setPluginCoreVersion(pluginDescriptor.getPluginCoreVersion());
            }
            return pluginBaseInfo;
        }
        return null;
    }
}
