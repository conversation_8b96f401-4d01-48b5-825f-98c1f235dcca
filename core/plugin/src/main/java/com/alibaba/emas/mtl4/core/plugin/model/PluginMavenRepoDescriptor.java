package com.alibaba.emas.mtl4.core.plugin.model;

import com.alibaba.emas.mtl4.plugin.core.maven.MavenRepoDescriptor;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PluginMavenRepoDescriptor extends MavenRepoDescriptor {
    String pluginId;
    String pluginVersion;

    public PluginMavenRepoDescriptor(String groupId, String artifactId, String mavenVersion, String pluginId, String pluginVersion) {
        super(null, groupId, artifactId, mavenVersion);
        this.pluginId = pluginId;
        this.pluginVersion = pluginVersion;
    }

    public PluginMavenRepoDescriptor(String groupId, String artifactId, String mavenVersion) {
        super(null, groupId, artifactId, mavenVersion);
    }


}
