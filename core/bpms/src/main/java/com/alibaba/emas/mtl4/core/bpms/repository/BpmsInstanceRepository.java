package com.alibaba.emas.mtl4.core.bpms.repository;


import com.alibaba.emas.mtl4.core.bpms.model.BpmsApprovalStatus;
import com.alibaba.emas.mtl4.core.bpms.model.domain.BpmsInstance;
import com.alibaba.emas.mtl4.core.bpms.model.BpmsRelatedType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * created by qingli.hwz on 2019-6-10
 */
public interface BpmsInstanceRepository extends JpaRepository<BpmsInstance, Long>, JpaSpecificationExecutor<BpmsInstance> {

    BpmsInstance findByRelatedId(Long relatedId);

    BpmsInstance findByProcessInstanceId(String processInstanceId);

    List<BpmsInstance> findByRelatedTypeAndRelatedIdOrderByIdDesc(BpmsRelatedType relatedType, Long relatedId);

    List<BpmsInstance> findByRelatedTypeAndRelatedIdAndApprovalStatusOrderByIdDesc(BpmsRelatedType relatedType, Long relatedId, BpmsApprovalStatus approvalStatus);


    List<BpmsInstance> findByRelatedTypeAndRelatedIdInAndApprovalStatusOrderByIdDesc(BpmsRelatedType relatedType,
                                                                                     List<Long> relatedIds,
                                                                                     BpmsApprovalStatus approvalStatus);

    @Modifying(clearAutomatically = true)
    @Transactional
    @Query("UPDATE BpmsInstance bpmsInstance " +
            "SET bpmsInstance.approvalStatus = :approvalStatus, " +
            "    bpmsInstance.gmtModified = now(), " +
            "    bpmsInstance.modifier = :user " +
            "WHERE bpmsInstance.relatedId = :relatedId and bpmsInstance.relatedType = :relatedType")
    void alterApprovalStatus(@Param("relatedId") Long relatedId, @Param("relatedType")BpmsRelatedType relatedType, @Param("approvalStatus")BpmsApprovalStatus approvalStatus, @Param("user") String modifier);


    @Modifying(clearAutomatically = true)
    @Transactional
    @Query("UPDATE BpmsInstance bpmsInstance " +
            "SET bpmsInstance.approvalStatus = :approvalStatus, " +
            "    bpmsInstance.gmtModified = now(), " +
            "    bpmsInstance.modifier = :user " +
            "WHERE bpmsInstance.relatedId = :relatedId and bpmsInstance.relatedType = :relatedType and bpmsInstance.processInstanceId = :processInstanceId")
    void alterApprovalStatus(@Param("relatedId") Long relatedId, @Param("relatedType")BpmsRelatedType relatedType, @Param("approvalStatus")BpmsApprovalStatus approvalStatus,
                             @Param("processInstanceId")String processInstanceId, @Param("user") String modifier);


    @Modifying(clearAutomatically = true)
    @Transactional
    @Query("UPDATE BpmsInstance bpmsInstance " +
            "SET bpmsInstance.approvalStatus = :approvalStatus, " +
            "    bpmsInstance.gmtModified = now(), " +
            "    bpmsInstance.modifier = :user " +
            "WHERE bpmsInstance.processInstanceId = :processInstanceId")
    void alterApprovalStatus(@Param("approvalStatus")BpmsApprovalStatus approvalStatus,
                             @Param("processInstanceId")String processInstanceId,
                             @Param("user") String modifier);

}
